# Server
PORT=5000
HOSTNAME=localhost
NODE_ENV=development

# App
FRONTEND_URL=http://localhost:8000

# MongoDB
# MONGODB_URI=mongodb+srv://asphum:<EMAIL>/webshop05new?retryWrites=true&w=majority
MONGODB_URI=mongodb://localhost:27017/webshop05new


# JWT
JWT_SECRET=7THG83x5TcQPa8NS7TH7THG83x5TcQPa8NSG83x5TcQPa8NS
REFRESH_TOKEN_SECRET=7THG83x5TcQPa8NS7THG83x5TcQPa8NS7THG83x5TcQPa8NS
JWT_EXPIRES_IN=1h
REFRESH_TOKEN_EXPIRES_IN=7d


# Email (Gmail)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=nyue pyaq ukzk otxz
EMAIL_FROM=<EMAIL>

# Cloudinary
CLOUDINARY_CLOUD_NAME=dsoy8tgfc
CLOUDINARY_API_KEY=139741931552624
CLOUDINARY_API_SECRET=LR7ShBIjTR4kjZajyOl-geawG4s

# Vercel
VC_TOKEN=************************
VC_PROJECT_ID_IS1SHOP=prj_nPN1bEIofKYYUW8swEqvjfv0iJVp
VC_PROJECT_ID_PEESADCOM=prj_nPN1bEIofKYYUW8swEqvjfv0iJVp