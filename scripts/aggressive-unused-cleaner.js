#!/usr/bin/env node

/**
 * Aggressive Unused Code Cleaner
 * ลบ unused code อย่างจริงจัง รวมถึง parameters ที่ไม่ใช้
 */

import { ESLint } from 'eslint';
import fs from 'fs';
import path from 'path';

class AggressiveUnusedCleaner {
  constructor() {
    this.removedCount = 0;
    this.processedFiles = 0;
    this.modifiedFiles = 0;
  }

  /**
   * รัน ESLint และได้ข้อมูล unused code
   */
  async getUnusedCodeInfo() {
    console.log('🔍 กำลังวิเคราะห์ unused code ด้วย ESLint...');

    const eslint = new ESLint({
      overrideConfigFile: 'eslint.config.js',
    });

    const results = await eslint.lintFiles(['src/**/*.ts']);
    const unusedItems = [];

    for (const result of results) {
      for (const message of result.messages) {
        if (
          message.message.includes('never used')
          || message.message.includes('is defined but never used')
          || message.message.includes('is assigned a value but never used')
          || message.message.includes('is declared but its value is never read')
        ) {
          unusedItems.push({
            file: result.filePath,
            line: message.line,
            column: message.column,
            message: message.message,
            ruleId: message.ruleId,
            severity: message.severity,
          });
        }
      }
    }

    return unusedItems;
  }

  /**
   * ลบ unused parameters จาก function
   */
  removeUnusedParameters(content, unusedItems, filePath) {
    const lines = content.split('\n');
    let modified = false;
    let removedInThisFile = 0;

    // หา unused parameters
    const unusedParams = unusedItems.filter(item =>
      item.file === filePath
      && (item.message.includes('is defined but never used')
        || item.message.includes('is declared but its value is never read'))
    );

    for (const unusedParam of unusedParams) {
      const lineIndex = unusedParam.line - 1;
      if (lineIndex >= 0 && lineIndex < lines.length) {
        const line = lines[lineIndex];

        // Extract parameter name from message
        const paramMatch = unusedParam.message.match(/'([^']+)'/);
        if (!paramMatch) continue;

        const paramName = paramMatch[1];

        // ลบ parameter ที่ไม่ใช้ออกจาก function signature
        if (this.removeParameterFromLine(lines, lineIndex, paramName)) {
          console.log(`  🗑️  ลบ unused parameter: ${paramName} ใน ${path.basename(filePath)}:${unusedParam.line}`);
          modified = true;
          removedInThisFile++;
        }
      }
    }

    return { content: lines.join('\n'), modified, removedCount: removedInThisFile };
  }

  /**
   * ลบ parameter จากบรรทัด
   */
  removeParameterFromLine(lines, lineIndex, paramName) {
    const line = lines[lineIndex];

    // Pattern สำหรับ function parameters
    const patterns = [
      // Arrow function: (param1, unusedParam, param2) =>
      new RegExp(`\\(([^)]*),\\s*${paramName}\\s*,([^)]*)\\)\\s*=>`),
      new RegExp(`\\(([^)]*),\\s*${paramName}\\s*\\)\\s*=>`),
      new RegExp(`\\(\\s*${paramName}\\s*,([^)]*)\\)\\s*=>`),
      new RegExp(`\\(\\s*${paramName}\\s*\\)\\s*=>`),

      // Regular function: function name(param1, unusedParam, param2)
      new RegExp(`function\\s+\\w+\\s*\\(([^)]*),\\s*${paramName}\\s*,([^)]*)\\)`),
      new RegExp(`function\\s+\\w+\\s*\\(([^)]*),\\s*${paramName}\\s*\\)`),
      new RegExp(`function\\s+\\w+\\s*\\(\\s*${paramName}\\s*,([^)]*)\\)`),
      new RegExp(`function\\s+\\w+\\s*\\(\\s*${paramName}\\s*\\)`),

      // Method: methodName(param1, unusedParam, param2)
      new RegExp(`(\\w+)\\s*\\(([^)]*),\\s*${paramName}\\s*,([^)]*)\\)`),
      new RegExp(`(\\w+)\\s*\\(([^)]*),\\s*${paramName}\\s*\\)`),
      new RegExp(`(\\w+)\\s*\\(\\s*${paramName}\\s*,([^)]*)\\)`),
      new RegExp(`(\\w+)\\s*\\(\\s*${paramName}\\s*\\)`),
    ];

    for (const pattern of patterns) {
      if (pattern.test(line)) {
        // ลบ parameter
        let newLine = line;

        // ลบ parameter ที่อยู่กลาง (มี comma ทั้งสองข้าง)
        newLine = newLine.replace(new RegExp(`,\\s*${paramName}\\s*,`), ',');

        // ลบ parameter ที่อยู่ท้าย (มี comma ข้างหน้า)
        newLine = newLine.replace(new RegExp(`,\\s*${paramName}\\s*\\)`), ')');

        // ลบ parameter ที่อยู่หน้า (มี comma ข้างหลัง)
        newLine = newLine.replace(new RegExp(`\\(\\s*${paramName}\\s*,`), '(');

        // ลบ parameter เดียว
        newLine = newLine.replace(new RegExp(`\\(\\s*${paramName}\\s*\\)`), '()');

        if (newLine !== line) {
          lines[lineIndex] = newLine;
          return true;
        }
      }
    }

    return false;
  }

  /**
   * ลบ unused variables
   */
  removeUnusedVariables(content, unusedItems, filePath) {
    const lines = content.split('\n');
    let modified = false;
    let removedInThisFile = 0;

    // หา unused variables
    const unusedVars = unusedItems.filter(item =>
      item.file === filePath
      && (item.message.includes('is assigned a value but never used')
        || (item.message.includes('is defined but never used')
          && !item.message.includes('parameter')))
    );

    for (const unusedVar of unusedVars) {
      const lineIndex = unusedVar.line - 1;
      if (lineIndex >= 0 && lineIndex < lines.length) {
        const line = lines[lineIndex];

        // ลบ variable declarations ที่ไม่ใช้
        if (line.includes('const ') || line.includes('let ') || line.includes('var ')) {
          // ตรวจสอบว่าเป็น destructuring หรือไม่
          if (line.includes('{') && line.includes('}')) {
            // Destructuring assignment - ลบเฉพาะ property ที่ไม่ใช้
            const varMatch = unusedVar.message.match(/'([^']+)'/);
            if (varMatch) {
              const varName = varMatch[1];
              const newLine = this.removeFromDestructuring(line, varName);
              if (newLine !== line) {
                lines[lineIndex] = newLine;
                console.log(`  🔧 ลบจาก destructuring: ${varName} ใน ${path.basename(filePath)}:${unusedVar.line}`);
                modified = true;
                removedInThisFile++;
              }
            }
          }
          else {
            // Regular variable - ลบทั้งบรรทัด
            console.log(`  🗑️  ลบ unused variable: ${line.trim()} ใน ${path.basename(filePath)}:${unusedVar.line}`);
            lines[lineIndex] = '';
            modified = true;
            removedInThisFile++;
          }
        }
      }
    }

    return { content: lines.join('\n'), modified, removedCount: removedInThisFile };
  }

  /**
   * ลบ property จาก destructuring
   */
  removeFromDestructuring(line, varName) {
    // { unusedVar, usedVar } = object
    let newLine = line;

    // ลบ property ที่อยู่กลาง
    newLine = newLine.replace(new RegExp(`,\\s*${varName}\\s*,`), ',');

    // ลบ property ที่อยู่ท้าย
    newLine = newLine.replace(new RegExp(`,\\s*${varName}\\s*}`), ' }');

    // ลบ property ที่อยู่หน้า
    newLine = newLine.replace(new RegExp(`{\\s*${varName}\\s*,`), '{ ');

    // ลบ property เดียว
    newLine = newLine.replace(new RegExp(`{\\s*${varName}\\s*}`), '{}');

    // ถ้าเหลือ {} ว่างๆ ให้ลบทั้งบรรทัด
    if (newLine.includes('= {}') || newLine.includes('{ }')) {
      return '';
    }

    return newLine;
  }

  /**
   * ประมวลผลไฟล์
   */
  async processFile(filePath, unusedItems) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      let newContent = content;
      let fileModified = false;
      let totalRemovedInFile = 0;

      // ลบ unused parameters
      const paramResult = this.removeUnusedParameters(newContent, unusedItems, filePath);
      if (paramResult.modified) {
        newContent = paramResult.content;
        fileModified = true;
        totalRemovedInFile += paramResult.removedCount;
      }

      // ลบ unused variables
      const varResult = this.removeUnusedVariables(newContent, unusedItems, filePath);
      if (varResult.modified) {
        newContent = varResult.content;
        fileModified = true;
        totalRemovedInFile += varResult.removedCount;
      }

      // เขียนไฟล์กลับถ้ามีการเปลี่ยนแปลง
      if (fileModified) {
        // ลบบรรทัดว่างที่เกินไป
        const cleanedContent = this.cleanupEmptyLines(newContent);
        fs.writeFileSync(filePath, cleanedContent);
        console.log(`✅ แก้ไข: ${path.relative(process.cwd(), filePath)} (ลบ ${totalRemovedInFile} รายการ)`);
        this.modifiedFiles++;
      }

      this.removedCount += totalRemovedInFile;
      this.processedFiles++;
      return fileModified;
    }
    catch (error) {
      console.error(`❌ ข้อผิดพลาดในไฟล์ ${filePath}:`, error.message);
      return false;
    }
  }

  /**
   * ลบบรรทัดว่างที่เกินไป
   */
  cleanupEmptyLines(content) {
    const lines = content.split('\n');
    const cleanedLines = [];
    let consecutiveEmptyLines = 0;

    for (const line of lines) {
      if (line.trim() === '') {
        consecutiveEmptyLines++;
        if (consecutiveEmptyLines <= 1) {
          cleanedLines.push(line);
        }
      }
      else {
        consecutiveEmptyLines = 0;
        cleanedLines.push(line);
      }
    }

    return cleanedLines.join('\n');
  }

  /**
   * ประมวลผลไฟล์ทั้งหมด
   */
  async processAllFiles() {
    console.log('🧹 Aggressive Unused Code Cleaner');
    console.log('==================================\n');

    // รับข้อมูล unused code จาก ESLint
    const unusedItems = await this.getUnusedCodeInfo();

    if (unusedItems.length === 0) {
      console.log('🎉 ไม่พบ unused code ที่สามารถลบได้!');
      return;
    }

    console.log(`📋 พบ unused items: ${unusedItems.length} รายการ\n`);

    // จัดกลุ่มตามไฟล์
    const fileGroups = {};
    unusedItems.forEach(item => {
      if (!fileGroups[item.file]) {
        fileGroups[item.file] = [];
      }
      fileGroups[item.file].push(item);
    });

    console.log('🔧 กำลังลบ unused code อย่างจริงจัง...\n');

    // ประมวลผลแต่ละไฟล์
    for (const [filePath, items] of Object.entries(fileGroups)) {
      console.log(`📁 ประมวลผล: ${path.relative(process.cwd(), filePath)}`);
      await this.processFile(filePath, items);
      console.log('');
    }

    // สรุปผลลัพธ์
    console.log('📊 สรุปผลลัพธ์:');
    console.log(`📁 ประมวลผล: ${this.processedFiles} ไฟล์`);
    console.log(`✅ แก้ไข: ${this.modifiedFiles} ไฟล์`);
    console.log(`🗑️  ลบ unused items: ${this.removedCount} รายการ`);

    if (this.removedCount > 0) {
      console.log('\n⚠️  คำเตือน: ทดสอบระบบให้แน่ใจว่าทุกอย่างยังทำงานได้ปกติ!');
      console.log('💡 หากมีปัญหา ใช้ git checkout เพื่อ rollback');
    }
  }
}

// Main function
async function main() {
  const cleaner = new AggressiveUnusedCleaner();
  await cleaner.processAllFiles();
}

// Check if this is the main module
if (process.argv[1] === new URL(import.meta.url).pathname) {
  main().catch(console.error);
}

export default AggressiveUnusedCleaner;
