#!/bin/bash

# Quick Clean Script - ลบ unused code อย่างรวดเร็ว
# ใช้งาน: ./scripts/quick-clean.sh [option]

set -e

echo "🧹 Quick Clean - ลบ unused code อย่างรวดเร็ว"
echo "================================================"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to show help
show_help() {
    echo ""
    echo "การใช้งาน:"
    echo "  ./scripts/quick-clean.sh [option]"
    echo ""
    echo "Options:"
    echo "  imports    - ลบ unused imports เท่านั้น (แนะนำ)"
    echo "  eslint     - ใช้ ESLint --fix"
    echo "  all        - ลบ unused code ทั้งหมด"
    echo "  check      - ตรวจสอบ unused code"
    echo "  report     - สร้างรายงาน"
    echo "  help       - แสดงความช่วยเหลือ"
    echo ""
    echo "ตัวอย่าง:"
    echo "  ./scripts/quick-clean.sh imports"
    echo "  ./scripts/quick-clean.sh check"
    echo ""
}

# Function to check current status
check_status() {
    echo -e "${BLUE}🔍 ตรวจสอบสถานะปัจจุบัน...${NC}"
    
    # Count errors and warnings
    local lint_output=$(bunx eslint . 2>/dev/null | tail -1)
    echo -e "${YELLOW}📊 ESLint Status:${NC}"
    echo "   $lint_output"
    
    # Count unused variables specifically
    local unused_count=$(bunx eslint . 2>/dev/null | grep -c "never used" || echo "0")
    echo -e "${YELLOW}🗑️  Unused items: ${unused_count} รายการ${NC}"
    echo ""
}

# Function to clean imports
clean_imports() {
    echo -e "${GREEN}🧹 กำลังลบ unused imports...${NC}"
    bun run clean:imports-only
    echo ""
    echo -e "${GREEN}✅ เสร็จสิ้นการลบ unused imports${NC}"
}

# Function to use ESLint fix
eslint_fix() {
    echo -e "${GREEN}🔧 กำลังใช้ ESLint --fix...${NC}"
    bunx eslint . --fix
    echo ""
    echo -e "${GREEN}✅ เสร็จสิ้น ESLint --fix${NC}"
}

# Function to clean all
clean_all() {
    echo -e "${GREEN}🧹 กำลังลบ unused code ทั้งหมด...${NC}"
    
    # Step 1: Clean imports
    echo -e "${BLUE}Step 1: ลบ unused imports${NC}"
    bun run clean:imports-only
    
    # Step 2: ESLint fix
    echo -e "${BLUE}Step 2: ESLint --fix${NC}"
    bunx eslint . --fix
    
    echo ""
    echo -e "${GREEN}✅ เสร็จสิ้นการทำความสะอาดทั้งหมด${NC}"
}

# Function to generate report
generate_report() {
    echo -e "${BLUE}📋 กำลังสร้างรายงาน...${NC}"
    bun run clean:report
    
    if [ -f "unused-code-report.json" ]; then
        echo -e "${GREEN}📄 รายงานถูกสร้างแล้ว: unused-code-report.json${NC}"
        
        # Show summary
        local total_files=$(jq '.summary.totalFiles' unused-code-report.json 2>/dev/null || echo "N/A")
        local files_with_unused=$(jq '.summary.filesWithUnused' unused-code-report.json 2>/dev/null || echo "N/A")
        local total_unused=$(jq '.summary.totalUnusedItems' unused-code-report.json 2>/dev/null || echo "N/A")
        
        echo ""
        echo -e "${YELLOW}📊 สรุปรายงาน:${NC}"
        echo "   📁 ไฟล์ทั้งหมด: $total_files"
        echo "   ⚠️  ไฟล์ที่มี unused code: $files_with_unused"
        echo "   🗑️  Unused items ทั้งหมด: $total_unused"
    fi
}

# Main logic
case "${1:-help}" in
    "imports")
        check_status
        clean_imports
        check_status
        ;;
    "eslint")
        check_status
        eslint_fix
        check_status
        ;;
    "all")
        check_status
        clean_all
        check_status
        ;;
    "check")
        check_status
        ;;
    "report")
        generate_report
        ;;
    "help"|*)
        show_help
        ;;
esac

echo ""
echo -e "${GREEN}🎉 เสร็จสิ้น!${NC}"
echo ""
echo -e "${BLUE}💡 Tips:${NC}"
echo "   - ใช้ 'imports' สำหรับการทำความสะอาดปกติ"
echo "   - ใช้ 'check' เพื่อดูสถานะปัจจุบัน"
echo "   - ใช้ 'report' เพื่อดูรายละเอียด unused code"
echo ""
