#!/usr/bin/env node

/**
 * Simple Unused Code Cleaner
 * ใช้ regex patterns เพื่อลบ unused imports และ variables
 */

import fs from 'fs';
import { glob } from 'glob';
import path from 'path';

class SimpleUnusedCleaner {
  constructor() {
    this.removedCount = 0;
    this.processedFiles = 0;
  }

  /**
   * ลบ unused imports จากไฟล์
   */
  removeUnusedImports(content, filePath) {
    let modified = false;
    let newContent = content;

    // Pattern สำหรับ import statements
    const importPatterns = [
      // import { unused } from 'module'
      /import\s*{\s*([^}]+)\s*}\s*from\s*['"][^'"]+['"];?\s*\n?/g,
      // import unused from 'module'
      /import\s+(\w+)\s+from\s+['"][^'"]+['"];?\s*\n?/g,
      // import * as unused from 'module'
      /import\s*\*\s*as\s+(\w+)\s+from\s+['"][^'"]+['"];?\s*\n?/g,
    ];

    // ค้นหา unused imports
    const lines = content.split('\n');
    const usedIdentifiers = new Set();

    // หา identifiers ที่ใช้งานจริง (ไม่ใช่ใน import statements)
    lines.forEach((line, index) => {
      if (!line.trim().startsWith('import ') && !line.trim().startsWith('//')) {
        // หา identifiers ที่ใช้งาน
        const identifierMatches = line.match(/\b[a-zA-Z_$][a-zA-Z0-9_$]*\b/g);
        if (identifierMatches) {
          identifierMatches.forEach(id => usedIdentifiers.add(id));
        }
      }
    });

    // ลบ unused imports
    newContent = newContent.replace(
      /import\s*{\s*([^}]+)\s*}\s*from\s*(['"][^'"]+['"])\s*;?\s*\n?/g,
      (match, imports, module) => {
        const importList = imports.split(',').map(imp => imp.trim());
        const usedImports = importList.filter(imp => {
          const cleanImp = imp.replace(/\s+as\s+\w+/, '').trim();
          return usedIdentifiers.has(cleanImp) || usedIdentifiers.has(imp.trim());
        });

        if (usedImports.length === 0) {
          console.log(`  ❌ ลบ import ทั้งหมดจาก ${module} ใน ${path.basename(filePath)}`);
          this.removedCount++;
          modified = true;
          return '';
        }
        else if (usedImports.length < importList.length) {
          console.log(`  🔧 ลบ unused imports บางตัวจาก ${module} ใน ${path.basename(filePath)}`);
          this.removedCount++;
          modified = true;
          return `import { ${usedImports.join(', ')} } from ${module};\n`;
        }

        return match;
      },
    );

    return { content: newContent, modified };
  }

  /**
   * ลบ unused variables (แบบง่าย)
   */
  removeUnusedVariables(content, filePath) {
    let modified = false;
    let newContent = content;

    // Pattern สำหรับ unused variables ที่ชัดเจน
    const unusedPatterns = [
      // const unused = ...;
      /^\s*const\s+_\w+\s*=\s*[^;]+;\s*$/gm,
      // let unused = ...;
      /^\s*let\s+_\w+\s*=\s*[^;]+;\s*$/gm,
      // var unused = ...;
      /^\s*var\s+_\w+\s*=\s*[^;]+;\s*$/gm,
    ];

    unusedPatterns.forEach(pattern => {
      const matches = newContent.match(pattern);
      if (matches) {
        matches.forEach(match => {
          console.log(`  🗑️  ลบ unused variable: ${match.trim()} ใน ${path.basename(filePath)}`);
          newContent = newContent.replace(match, '');
          this.removedCount++;
          modified = true;
        });
      }
    });

    return { content: newContent, modified };
  }

  /**
   * ประมวลผลไฟล์เดียว
   */
  async processFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      let newContent = content;
      let fileModified = false;

      // ลบ unused imports
      const importResult = this.removeUnusedImports(newContent, filePath);
      if (importResult.modified) {
        newContent = importResult.content;
        fileModified = true;
      }

      // ลบ unused variables
      const variableResult = this.removeUnusedVariables(newContent, filePath);
      if (variableResult.modified) {
        newContent = variableResult.content;
        fileModified = true;
      }

      // เขียนไฟล์กลับถ้ามีการเปลี่ยนแปลง
      if (fileModified) {
        fs.writeFileSync(filePath, newContent);
        console.log(`✅ แก้ไข: ${path.relative(process.cwd(), filePath)}`);
      }

      this.processedFiles++;
      return fileModified;
    }
    catch (error) {
      console.error(`❌ ข้อผิดพลาดในไฟล์ ${filePath}:`, error.message);
      return false;
    }
  }

  /**
   * ประมวลผลไฟล์ทั้งหมด
   */
  async processAllFiles() {
    console.log('🔍 กำลังค้นหาไฟล์ TypeScript...');

    const files = await glob('src/**/*.ts', {
      ignore: ['**/*.d.ts', '**/node_modules/**'],
    });

    console.log(`📁 พบไฟล์: ${files.length} ไฟล์`);
    console.log('🧹 กำลังทำความสะอาด unused code...\n');

    let modifiedFiles = 0;

    for (const file of files) {
      const wasModified = await this.processFile(file);
      if (wasModified) {
        modifiedFiles++;
      }
    }

    console.log('\n📊 สรุปผลลัพธ์:');
    console.log(`📁 ประมวลผล: ${this.processedFiles} ไฟล์`);
    console.log(`✅ แก้ไข: ${modifiedFiles} ไฟล์`);
    console.log(`🗑️  ลบ unused items: ${this.removedCount} รายการ`);

    if (this.removedCount === 0) {
      console.log('🎉 ไม่พบ unused code ที่สามารถลบได้อัตโนมัติ');
    }
  }

  /**
   * ลบเฉพาะ unused imports
   */
  async cleanImportsOnly() {
    console.log('🔍 กำลังลบ unused imports เท่านั้น...');

    const files = await glob('src/**/*.ts', {
      ignore: ['**/*.d.ts', '**/node_modules/**'],
    });

    let modifiedFiles = 0;

    for (const file of files) {
      try {
        const content = fs.readFileSync(file, 'utf8');
        const result = this.removeUnusedImports(content, file);

        if (result.modified) {
          fs.writeFileSync(file, result.content);
          console.log(`✅ แก้ไข imports: ${path.relative(process.cwd(), file)}`);
          modifiedFiles++;
        }

        this.processedFiles++;
      }
      catch (error) {
        console.error(`❌ ข้อผิดพลาดในไฟล์ ${file}:`, error.message);
      }
    }

    console.log('\n📊 สรุปผลลัพธ์:');
    console.log(`📁 ประมวลผล: ${this.processedFiles} ไฟล์`);
    console.log(`✅ แก้ไข: ${modifiedFiles} ไฟล์`);
    console.log(`🗑️  ลบ unused imports: ${this.removedCount} รายการ`);
  }
}

// Main function
async function main() {
  const command = process.argv[2];
  const cleaner = new SimpleUnusedCleaner();

  switch (command) {
    case 'all':
      await cleaner.processAllFiles();
      break;
    case 'imports':
      await cleaner.cleanImportsOnly();
      break;
    default:
      console.log(`
🧹 Simple Unused Code Cleaner

การใช้งาน:
  node scripts/simple-unused-cleaner.js all      - ลบ unused code ทั้งหมด
  node scripts/simple-unused-cleaner.js imports  - ลบ unused imports เท่านั้น

ตัวอย่าง:
  bun run clean:simple
  bun run clean:imports-only
      `);
  }
}

// Check if this is the main module
if (process.argv[1] === new URL(import.meta.url).pathname) {
  main().catch(console.error);
}

export default SimpleUnusedCleaner;
