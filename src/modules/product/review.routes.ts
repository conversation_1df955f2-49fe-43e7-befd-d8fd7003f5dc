import { userAuthPlugin } from '@/core/plugins/auth';
import { HttpError } from '@/core/utils/error';
import { Elysia } from 'elysia';
import {
  createReviewSchema,
  ratingStatsResponseSchema,
  reviewFilterSchema,
  reviewListResponseSchema,
  reviewResponseSchema,
  reviewStatsResponseSchema,
  updateReviewSchema,
} from './review.schema';
import {
  approveReview,
  createReview,
  deleteReview,
  getProductRatingStats,
  getReviewById,
  getReviewsByCustomer,
  getReviewsByProduct,
  getReviewStats,
  markReviewAsHelpful,
  rejectReview,
  reportReview,
  unmarkReviewAsHelpful,
  updateReview,
} from './review.service';
export const reviewRoutes = new Elysia({ prefix: '/review' })
  .use(userAuthPlugin)
  // สร้างรีวิวใหม่
  .post(
    '/create',
    async ({ body, user }: any) => {
      const { siteId, productId, rating, title, comment, images } = body;
      const reviewData = {
        siteId,
        productId,
        customerId: user._id,
        customerName: user.name || user.email,
        customerEmail: user.email,
        rating,
        title,
        comment,
        images,
      };

      const result = await createReview(reviewData);
      return {
        ...result,
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
      };
    },
    {
      body: createReviewSchema,
      response: reviewResponseSchema,
    },
  )
  // ดึงรีวิวของสินค้า
  .get(
    '/product/:productId',
    async ({ params, query }: any) => {
      const { productId } = params;
      const filter: any = {};

      if (query.rating) filter.rating = parseInt(query.rating);
      if (query.isVerified !== undefined) filter.isVerified = query.isVerified === 'true';

      let sortQuery: any = { isVerified: -1, isHelpful: -1, createdAt: -1 };
      if (query.sortBy) {
        switch (query.sortBy) {
          case 'newest':
            sortQuery = { createdAt: -1 };
            break;
          case 'oldest':
            sortQuery = { createdAt: 1 };
            break;
          case 'rating':
            _sortQuery = { rating: -1, createdAt: -1 };
            break;
          case 'helpful':
            _sortQuery = { isHelpful: -1, createdAt: -1 };
            break;
        }
      }

      const page = parseInt(query.page) || 1;
      const limit = parseInt(query.limit) || 20;
      const skip = (page - 1) * limit;

      const reviews = await getReviewsByProduct(productId, filter);
      const total = reviews.length;
      const paginatedReviews = reviews.slice(skip, skip + limit);

      return {
        success: true,
        message: 'ดึงรีวิวสำเร็จ',
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
        data: paginatedReviews,
        total,
        page,
        limit,
      };
    },
    {
      query: reviewFilterSchema,
      response: reviewListResponseSchema,
    },
  )
  // ดึงรีวิวของลูกค้า
  .get(
    '/my-reviews',
    async ({ user, query }: any) => {
      const { siteId } = query;
      if (!siteId) {
        throw new HttpError(400, 'กรุณาระบุ siteId');
      }

      const reviews = await getReviewsByCustomer(user._id, siteId);

      return {
        success: true,
        message: 'ดึงรีวิวของลูกค้าสำเร็จ',
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
        data: reviews,
      };
    },
    {
      response: reviewResponseSchema,
    },
  )
  // ดึงข้อมูลรีวิวเฉพาะ
  .get(
    '/:reviewId',
    async ({ params }: any) => {
      const { reviewId } = params;
      const review = await getReviewById(reviewId);

      return {
        success: true,
        message: 'ดึงข้อมูลรีวิวสำเร็จ',
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
        data: review,
      };
    },
    {
      response: reviewResponseSchema,
    },
  )
  // อัปเดตรีวิว
  .put(
    '/:reviewId',
    async ({ params, body, user }: any) => {
      const { reviewId } = params;
      const review = await getReviewById(reviewId);

      // ตรวจสอบว่าเป็นเจ้าของรีวิว
      if (review.customerId !== user._id) {
        throw new HttpError(403, 'ไม่มีสิทธิ์แก้ไขรีวิวนี้');
      }

      const result = await updateReview(reviewId, body);
      return {
        ...result,
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
      };
    },
    {
      body: updateReviewSchema,
      response: reviewResponseSchema,
    },
  )
  // ลบรีวิว
  .delete(
    '/:reviewId',
    async ({ params, user }: any) => {
      const { reviewId } = params;

      const result = await deleteReview(reviewId, user._id);
      return {
        ...result,
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
      };
    },
    {
      response: reviewResponseSchema,
    },
  )
  // ทำเครื่องหมายว่าช่วยเหลือ
  .post(
    '/:reviewId/helpful',
    async ({ params, user }: any) => {
      const { reviewId } = params;

      const result = await markReviewAsHelpful(reviewId, user._id);
      return {
        ...result,
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
      };
    },
    {
      response: reviewResponseSchema,
    },
  )
  // ยกเลิกการทำเครื่องหมายว่าช่วยเหลือ
  .delete(
    '/:reviewId/helpful',
    async ({ params, user }: any) => {
      const { reviewId } = params;

      const result = await unmarkReviewAsHelpful(reviewId, user._id);
      return {
        ...result,
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
      };
    },
    {
      response: reviewResponseSchema,
    },
  )
  // รายงานรีวิว
  .post(
    '/:reviewId/report',
    async ({ params }: any) => {
      const { reviewId } = params;

      const result = await reportReview(reviewId);
      return {
        ...result,
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
      };
    },
    {
      response: reviewResponseSchema,
    },
  )
  // อนุมัติรีวิว (Admin)
  .put(
    '/:reviewId/approve',
    async ({ params }: any) => {
      const { reviewId } = params;

      const result = await approveReview(reviewId);
      return {
        ...result,
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
      };
    },
    {
      response: reviewResponseSchema,
    },
  )
  // ปฏิเสธรีวิว (Admin)
  .put(
    '/:reviewId/reject',
    async ({ params }: any) => {
      const { reviewId } = params;

      const result = await rejectReview(reviewId);
      return {
        ...result,
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
      };
    },
    {
      response: reviewResponseSchema,
    },
  )
  // สถิติรีวิวของสินค้า
  .get(
    '/stats/product/:productId',
    async ({ params }: any) => {
      const { productId } = params;
      const stats = await getProductRatingStats(productId);

      return {
        success: true,
        message: 'ดึงสถิติรีวิวสำเร็จ',
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
        data: stats,
      };
    },
    {
      response: ratingStatsResponseSchema,
    },
  )
  // สถิติรีวิวของไซต์
  .get(
    '/stats/site/:siteId',
    async ({ params }: any) => {
      const { siteId } = params;
      const stats = await getReviewStats(siteId);

      return {
        success: true,
        message: 'ดึงสถิติรีวิวสำเร็จ',
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
        data: stats,
      };
    },
    {
      response: reviewStatsResponseSchema,
    },
  );
