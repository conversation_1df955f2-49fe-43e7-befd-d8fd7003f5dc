import { generateFileId } from '@/core/utils/idGenerator';
import mongoose, { type Document, Schema } from 'mongoose';

export type CommentType = 'product' | 'review' | 'article' | 'general';
export type CommentStatus = 'pending' | 'approved' | 'rejected' | 'spam';

export interface IComment extends Document {
  _id: string;
  siteId: string;
  type: CommentType;
  targetId: string; // productId, reviewId, articleId, etc.
  parentId?: string; // สำหรับ nested comments

  // User info
  userId: string;
  userName: string;
  userEmail: string;
  userAvatar?: string;

  // Comment content
  content: string;
  images?: string[];

  // Moderation
  status: CommentStatus;
  isVerified: boolean; // ลูกค้าที่ซื้อจริง

  // Engagement
  likes: number;
  dislikes: number;
  reportCount: number;

  // Metadata
  likedBy: string[]; // user IDs ที่กด like
  dislikedBy: string[]; // user IDs ที่กด dislike
  reportedBy: string[]; // user IDs ที่ report

  // Timestamps
  createdAt: Date;
  updatedAt: Date;
  approvedAt?: Date;
  rejectedAt?: Date;
}

const commentSchema = new Schema<IComment>(
  {
    _id: { type: String, default: () => generateFileId(5) },
    siteId: { type: String, required: true, index: true },
    type: {
      type: String,
      enum: ['product', 'review', 'article', 'general'],
      required: true,
      index: true,
    },
    targetId: { type: String, required: true, index: true },
    parentId: { type: String, index: true },

    // User info
    userId: { type: String, required: true, index: true },
    userName: { type: String, required: true },
    userEmail: { type: String, required: true },
    userAvatar: { type: String },

    // Comment content
    content: {
      type: String,
      required: true,
      maxLength: 2000,
    },
    images: [{ type: String }],

    // Moderation
    status: {
      type: String,
      enum: ['pending', 'approved', 'rejected', 'spam'],
      default: 'approved',
      index: true,
    },
    isVerified: { type: Boolean, default: false, index: true },

    // Engagement
    likes: { type: Number, default: 0, index: true },
    dislikes: { type: Number, default: 0 },
    reportCount: { type: Number, default: 0 },

    // Metadata
    likedBy: [{ type: String }],
    dislikedBy: [{ type: String }],
    reportedBy: [{ type: String }],

    // Timestamps
    approvedAt: { type: Date },
    rejectedAt: { type: Date },
  },
  {
    timestamps: true,
    id: false,
    versionKey: false,
    toJSON: {
      virtuals: true,
    },
    toObject: {
      virtuals: true,
    },
  },
);

// Indexes
// commentSchema.index({ siteId: 1, type: 1, targetId: 1 });
// commentSchema.index({ siteId: 1, userId: 1 });
// commentSchema.index({ siteId: 1, status: 1 });
// commentSchema.index({ siteId: 1, parentId: 1 });
// commentSchema.index({ siteId: 1, createdAt: -1 });

// Methods
commentSchema.methods.like = function(userId: string) {
  if (!this.likedBy.includes(userId)) {
    this.likedBy.push(userId);
    this.likes = this.likedBy.length;

    // ถ้าผู้ใช้เคยกด dislike ให้ลบออก
    const dislikeIndex = this.dislikedBy.indexOf(userId);
    if (dislikeIndex > -1) {
      this.dislikedBy.splice(dislikeIndex, 1);
      this.dislikes = this.dislikedBy.length;
    }
  }
  return this.save();
};

commentSchema.methods.unlike = function(userId: string) {
  const index = this.likedBy.indexOf(userId);
  if (index > -1) {
    this.likedBy.splice(index, 1);
    this.likes = this.likedBy.length;
  }
  return this.save();
};

commentSchema.methods.dislike = function(userId: string) {
  if (!this.dislikedBy.includes(userId)) {
    this.dislikedBy.push(userId);
    this.dislikes = this.dislikedBy.length;

    // ถ้าผู้ใช้เคยกด like ให้ลบออก
    const likeIndex = this.likedBy.indexOf(userId);
    if (likeIndex > -1) {
      this.likedBy.splice(likeIndex, 1);
      this.likes = this.likedBy.length;
    }
  }
  return this.save();
};

commentSchema.methods.undislike = function(userId: string) {
  const index = this.dislikedBy.indexOf(userId);
  if (index > -1) {
    this.dislikedBy.splice(index, 1);
    this.dislikes = this.dislikedBy.length;
  }
  return this.save();
};

commentSchema.methods.report = function(userId: string) {
  if (!this.reportedBy.includes(userId)) {
    this.reportedBy.push(userId);
    this.reportCount = this.reportedBy.length;
  }
  return this.save();
};

commentSchema.methods.approve = function() {
  this.status = 'approved';
  this.approvedAt = new Date();
  return this.save();
};

commentSchema.methods.reject = function() {
  this.status = 'rejected';
  this.rejectedAt = new Date();
  return this.save();
};

commentSchema.methods.markAsSpam = function() {
  this.status = 'spam';
  return this.save();
};

// Static methods
commentSchema.statics.getCommentsByTarget = function(
  siteId: string,
  type: CommentType,
  targetId: string,
  filter: any = {},
) {
  const query = { siteId, type, targetId, status: 'approved', ...filter };
  return this.find(query).sort({ createdAt: -1 });
};

commentSchema.statics.getCommentTree = function(siteId: string, type: CommentType, targetId: string) {
  return this.aggregate([
    { $match: { siteId, type, targetId, status: 'approved' } },
    { $sort: { createdAt: -1 } },
    {
      $graphLookup: {
        from: 'comments',
        startWith: '$_id',
        connectFromField: '_id',
        connectToField: 'parentId',
        as: 'replies',
        restrictSearchWithMatch: { status: 'approved' },
      },
    },
    {
      $addFields: {
        replies: {
          $sortArray: {
            input: '$replies',
            sortBy: { createdAt: 1 },
          },
        },
      },
    },
  ]);
};

export const Comment = mongoose.model<IComment>('Comment', commentSchema);
