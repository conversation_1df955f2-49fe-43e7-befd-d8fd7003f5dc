import { HttpError } from '@/core/utils/error';
import { Category } from './category.model';
import { Product } from './product.model';

// Product Service
export async function listProducts(filter: any = {}) {
  const { page = 1, limit = 10, search, category, type, featured, hasVariants, ...rest } = filter;
  const skip = (page - 1) * limit;

  // สร้าง query object
  const query: any = { ...rest };

  // ค้นหาตามชื่อสินค้า
  if (search) {
    query.$or = [
      { name: { $regex: search, $options: 'i' } },
      { description: { $regex: search, $options: 'i' } },
      { tags: { $in: [new RegExp(search, 'i')] } },
    ];
  }

  // กรองตามหมวดหมู่
  if (category) {
    query.categoryId = category;
  }

  // กรองตามประเภท
  if (type) {
    query.type = type;
  }

  // กรองสินค้าแนะนำ
  if (featured !== undefined) {
    query.featured = featured === 'true';
  }

  // กรองสินค้าที่มี variants
  if (hasVariants !== undefined) {
    query.hasVariants = hasVariants === 'true';
  }

  const products = await Product.find(query).populate('categoryId').sort({ createdAt: -1 }).skip(skip).limit(limit);

  const total = await Product.countDocuments(query);

  return {
    products,
    total,
    page: Number(page),
    limit: Number(limit),
    pages: Math.ceil(total / limit),
  };
}

export async function getProductById(id: string) {
  const product = await Product.findById(id).populate('categoryId');
  if (!product) {
    throw new HttpError(404, 'ไม่พบสินค้า');
  }
  return product;
}

export async function getProductBySlug(slug: string, siteId: string) {
  const product = await Product.findOne({ slug, siteId }).populate('categoryId');
  if (!product) {
    throw new HttpError(404, 'ไม่พบสินค้า');
  }
  return product;
}

export async function createProduct(data: any) {
  try {
    // ตรวจสอบ slug ซ้ำ
    if (data.slug) {
      const existingProduct = await Product.findOne({ slug: data.slug, siteId: data.siteId });
      if (existingProduct) {
        throw new HttpError(409, 'Slug นี้มีอยู่แล้ว');
      }
    }

    const product = await Product.create(data);
    return product;
  }
  catch (error: any) {
    if (error instanceof HttpError) throw error;
    if (error.code === 11000) {
      throw new HttpError(409, 'ข้อมูลซ้ำ');
    }
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะสร้างสินค้า');
  }
}

export async function updateProduct(id: string, data: any) {
  try {
    const product = await Product.findByIdAndUpdate(id, data, { new: true });
    if (!product) {
      throw new HttpError(404, 'ไม่พบสินค้า');
    }
    return product;
  }
  catch (error: any) {
    if (error instanceof HttpError) throw error;
    if (error.code === 11000) {
      throw new HttpError(409, 'ข้อมูลซ้ำ');
    }
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะอัปเดตสินค้า');
  }
}

export async function deleteProduct(id: string) {
  const product = await Product.findByIdAndDelete(id);
  if (!product) {
    throw new HttpError(404, 'ไม่พบสินค้า');
  }
  return { message: 'ลบสินค้าสำเร็จ' };
}

// === Variant Services ===

export async function addProductVariant(productId: string, variantData: Partial<IProductVariant>) {
  try {
    const product = await Product.findById(productId);
    if (!product) {
      throw new HttpError(404, 'ไม่พบสินค้า');
    }

    // ตรวจสอบ attributes ซ้ำ
    const existingVariant = product.variants.find(
      v => JSON.stringify(v.attributes) === JSON.stringify(variantData.attributes),
    );

    if (existingVariant) {
      throw new HttpError(409, 'ตัวเลือกสินค้านี้มีอยู่แล้ว');
    }

    product.variants.push(variantData as IProductVariant);
    product.hasVariants = true;

    // อัปเดต variantAttributes
    if (variantData.attributes) {
      const newAttributes = Object.keys(variantData.attributes);
      product.variantAttributes = [...new Set([...product.variantAttributes, ...newAttributes])];
    }

    await product.save();
    return product;
  }
  catch (error: any) {
    if (error instanceof HttpError) throw error;
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะเพิ่มตัวเลือกสินค้า');
  }
}

export async function updateProductVariant(
  productId: string,
  variantId: string,
  variantData: Partial<IProductVariant>,
) {
  try {
    const product = await Product.findById(productId);
    if (!product) {
      throw new HttpError(404, 'ไม่พบสินค้า');
    }

    const variantIndex = product.variants.findIndex(v => v._id === variantId);
    if (variantIndex === -1) {
      throw new HttpError(404, 'ไม่พบตัวเลือกสินค้า');
    }

    // อัปเดต variant
    product.variants[variantIndex] = { ...product.variants[variantIndex], ...variantData };

    await product.save();
    return product;
  }
  catch (error: any) {
    if (error instanceof HttpError) throw error;
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะอัปเดตตัวเลือกสินค้า');
  }
}

export async function deleteProductVariant(productId: string, variantId: string) {
  try {
    const product = await Product.findById(productId);
    if (!product) {
      throw new HttpError(404, 'ไม่พบสินค้า');
    }

    const variantIndex = product.variants.findIndex(v => v._id === variantId);
    if (variantIndex === -1) {
      throw new HttpError(404, 'ไม่พบตัวเลือกสินค้า');
    }

    product.variants.splice(variantIndex, 1);

    // ถ้าไม่มี variants แล้ว
    if (product.variants.length === 0) {
      product.hasVariants = false;
      product.variantAttributes = [];
    }

    await product.save();
    return { message: 'ลบตัวเลือกสินค้าสำเร็จ' };
  }
  catch (error: any) {
    if (error instanceof HttpError) throw error;
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะลบตัวเลือกสินค้า');
  }
}

export async function getProductVariant(productId: string, variantId: string) {
  const product = await Product.findById(productId);
  if (!product) {
    throw new HttpError(404, 'ไม่พบสินค้า');
  }

  const variant = product.variants.find(v => v._id === variantId);
  if (!variant) {
    throw new HttpError(404, 'ไม่พบตัวเลือกสินค้า');
  }

  return {
    product: {
      _id: product._id,
      name: product.name,
      slug: product.slug,
      images: product.images,
    },
    variant,
  };
}

export async function getProductVariantByAttributes(productId: string, attributes: Record<string, string>) {
  const product = await Product.findById(productId);
  if (!product) {
    throw new HttpError(404, 'ไม่พบสินค้า');
  }

  // ค้นหา variant ที่มี attributes ตรงกับที่ส่งมา
  const variant = product.variants.find(v => {
    if (!v.attributes) return false;

    // ตรวจสอบว่า attributes ทุกตัวตรงกัน
    const attributeKeys = Object.keys(attributes);
    return attributeKeys.every(key => v.attributes[key] === attributes[key]);
  });

  if (!variant) {
    throw new HttpError(404, 'ไม่พบตัวเลือกสินค้าที่ตรงกับเงื่อนไข');
  }

  return {
    product: {
      _id: product._id,
      name: product.name,
      slug: product.slug,
      images: product.images,
    },
    variant,
  };
}

// === Stock Management ===

export async function updateProductStock(productId: string, stock: number, variantId?: string) {
  try {
    const product = await Product.findById(productId);
    if (!product) {
      throw new HttpError(404, 'ไม่พบสินค้า');
    }

    if (variantId && product.hasVariants) {
      const variantIndex = product.variants.findIndex(v => v._id === variantId);
      if (variantIndex === -1) {
        throw new HttpError(404, 'ไม่พบตัวเลือกสินค้า');
      }
      product.variants[variantIndex].stock = stock;
    }
    else {
      product.stock = stock;
    }

    await product.save();
    return product;
  }
  catch (error: any) {
    if (error instanceof HttpError) throw error;
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะอัปเดตสต็อก');
  }
}

export async function checkProductStock(productId: string, quantity: number, variantId?: string) {
  const product = await Product.findById(productId);
  if (!product) {
    throw new HttpError(404, 'ไม่พบสินค้า');
  }

  let availableStock = 0;

  if (variantId && product.hasVariants) {
    const variant = product.variants.find(v => v._id === variantId);
    if (!variant) {
      throw new HttpError(404, 'ไม่พบตัวเลือกสินค้า');
    }
    availableStock = variant.stock || 0;
  }
  else {
    availableStock = product.stock || 0;
  }

  const isAvailable = !product.trackStock || availableStock >= quantity || product.allowBackorder;

  return {
    isAvailable,
    availableStock,
    requestedQuantity: quantity,
    allowBackorder: product.allowBackorder,
  };
}

// Category Service (รองรับหมวดหมู่ซ้อนกัน)
export async function listCategories() {
  return Category.find();
}
export async function getCategoryById(id: string) {
  return Category.findById(id);
}
export async function createCategory(data: any) {
  return Category.create(data);
}
export async function updateCategory(id: string, data: any) {
  return Category.findByIdAndUpdate(id, data, { new: true });
}
export async function deleteCategory(id: string) {
  return Category.findByIdAndDelete(id);
}

// Cart Service (mock, สามารถต่อยอดกับ user/session จริงได้)
export async function getCart(userId: string) {
  // TODO: implement cart per user (ใช้ DB หรือ memory store)
  return { userId, items: [] };
}
export async function addToCart(userId: string, productId: string, qty: number) {
  // TODO: implement add to cart logic
  return { userId, productId, qty };
}
export async function removeFromCart(_userId: string, _productId: string) {
  // Implementation for removing from cart
  return { message: 'Removed from cart' };
}

// Product Statistics
export async function getProductStats(siteId: string) {
  try {
    // นับจำนวนสินค้าทั้งหมด
    const totalProducts = await Product.countDocuments({ siteId });

    // นับจำนวนสินค้าที่เปิดขาย
    const activeProducts = await Product.countDocuments({
      siteId,
      isActive: true,
    });

    // นับจำนวนสินค้าหมดสต็อก
    const outOfStockProducts = await Product.countDocuments({
      siteId,
      stock: { $lte: 0 },
    });

    // นับจำนวนสินค้าสต็อกต่ำ (น้อยกว่า 10 ชิ้น)
    const lowStockProducts = await Product.countDocuments({
      siteId,
      stock: { $gt: 0, $lt: 10 },
    });

    return {
      totalProducts,
      activeProducts,
      outOfStockProducts,
      lowStockProducts,
    };
  }
  catch (error) {
    console.error('Error getting product stats:', error);
    throw new HttpError(500, 'เกิดข้อผิดพลาดในการดึงสถิติสินค้า');
  }
}
