import mongoose, { type Document, Schema } from 'mongoose';
import { customAlphabet } from 'nanoid';

const generateId = () => {
  const timestamp = Date.now().toString(36);
  const nanoid = customAlphabet('0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz', 12)();
  return `${timestamp}${nanoid}`;
};

export interface ILanguage extends Document {
  _id: string;
  siteId: string;
  code: string; // en, th, zh, etc.
  name: string; // English, ไทย, 中文
  nativeName: string; // English, ไทย, 中文
  isActive: boolean;
  isDefault: boolean;
  direction: 'ltr' | 'rtl';
  flag?: string;
  settings: {
    dateFormat: string;
    timeFormat: string;
    currency: string;
    currencySymbol: string;
    currencyPosition: 'before' | 'after';
    decimalSeparator: string;
    thousandSeparator: string;
    timezone: string;
  };
  createdAt: Date;
  updatedAt: Date;
}

export interface ITranslation extends Document {
  _id: string;
  siteId: string;
  languageCode: string;
  namespace: string; // 'common', 'product', 'checkout', etc.
  key: string;
  value: string;
  context?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface ICurrency extends Document {
  _id: string;
  siteId: string;
  code: string; // USD, THB, EUR, etc.
  name: string;
  symbol: string;
  rate: number; // Exchange rate to base currency
  isBase: boolean;
  isActive: boolean;
  precision: number;
  position: 'before' | 'after';
  createdAt: Date;
  updatedAt: Date;
}

export interface ITimezone extends Document {
  _id: string;
  siteId: string;
  name: string;
  offset: string; // +07:00, -05:00, etc.
  abbreviation: string; // GMT, EST, etc.
  isActive: boolean;
  isDefault: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const LanguageSchema = new Schema<ILanguage>(
  {
    _id: { type: String, default: generateId },
    siteId: { type: String, required: true, index: true },
    code: { type: String, required: true, unique: true },
    name: { type: String, required: true },
    nativeName: { type: String, required: true },
    isActive: { type: Boolean, default: true },
    isDefault: { type: Boolean, default: false },
    direction: {
      type: String,
      enum: ['ltr', 'rtl'],
      default: 'ltr',
    },
    flag: { type: String },
    settings: {
      dateFormat: { type: String, default: 'DD/MM/YYYY' },
      timeFormat: { type: String, default: 'HH:mm' },
      currency: { type: String, default: 'THB' },
      currencySymbol: { type: String, default: '฿' },
      currencyPosition: {
        type: String,
        enum: ['before', 'after'],
        default: 'before',
      },
      decimalSeparator: { type: String, default: '.' },
      thousandSeparator: { type: String, default: ',' },
      timezone: { type: String, default: 'Asia/Bangkok' },
    },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

const TranslationSchema = new Schema<ITranslation>(
  {
    _id: { type: String, default: generateId },
    siteId: { type: String, required: true, index: true },
    languageCode: { type: String, required: true, index: true },
    namespace: { type: String, required: true, index: true },
    key: { type: String, required: true },
    value: { type: String, required: true },
    context: { type: String },
    isActive: { type: Boolean, default: true },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

const CurrencySchema = new Schema<ICurrency>(
  {
    _id: { type: String, default: generateId },
    siteId: { type: String, required: true, index: true },
    code: { type: String, required: true },
    name: { type: String, required: true },
    symbol: { type: String, required: true },
    rate: { type: Number, required: true, default: 1 },
    isBase: { type: Boolean, default: false },
    isActive: { type: Boolean, default: true },
    precision: { type: Number, default: 2 },
    position: {
      type: String,
      enum: ['before', 'after'],
      default: 'before',
    },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

const TimezoneSchema = new Schema<ITimezone>(
  {
    _id: { type: String, default: generateId },
    siteId: { type: String, required: true, index: true },
    name: { type: String, required: true },
    offset: { type: String, required: true },
    abbreviation: { type: String, required: true },
    isActive: { type: Boolean, default: true },
    isDefault: { type: Boolean, default: false },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

// Indexes
LanguageSchema.index({ siteId: 1, code: 1 });
LanguageSchema.index({ siteId: 1, isActive: 1 });
LanguageSchema.index({ siteId: 1, isDefault: 1 });

TranslationSchema.index({ siteId: 1, languageCode: 1, namespace: 1 });
TranslationSchema.index({ siteId: 1, languageCode: 1, key: 1 });
TranslationSchema.index({ siteId: 1, isActive: 1 });

CurrencySchema.index({ siteId: 1, code: 1 });
CurrencySchema.index({ siteId: 1, isActive: 1 });
CurrencySchema.index({ siteId: 1, isBase: 1 });

TimezoneSchema.index({ siteId: 1, isActive: 1 });
TimezoneSchema.index({ siteId: 1, isDefault: 1 });

// Static methods
LanguageSchema.statics.findActive = async function(siteId: string) {
  return this.find({ siteId, isActive: true }).sort({ isDefault: -1, name: 1 });
};

LanguageSchema.statics.findDefault = async function(siteId: string) {
  return this.findOne({ siteId, isDefault: true });
};

TranslationSchema.statics.findByNamespace = async function(siteId: string, languageCode: string, namespace: string) {
  return this.find({ siteId, languageCode, namespace, isActive: true });
};

TranslationSchema.statics.findByKey = async function(siteId: string, languageCode: string, key: string) {
  return this.findOne({ siteId, languageCode, key, isActive: true });
};

CurrencySchema.statics.findActive = async function(siteId: string) {
  return this.find({ siteId, isActive: true }).sort({ isBase: -1, name: 1 });
};

CurrencySchema.statics.findBase = async function(siteId: string) {
  return this.findOne({ siteId, isBase: true });
};

TimezoneSchema.statics.findActive = async function(siteId: string) {
  return this.find({ siteId, isActive: true }).sort({ isDefault: -1, name: 1 });
};

TimezoneSchema.statics.findDefault = async function(siteId: string) {
  return this.findOne({ siteId, isDefault: true });
};

export const Language = mongoose.model<ILanguage>('Language', LanguageSchema);
export const Translation = mongoose.model<ITranslation>('Translation', TranslationSchema);
export const Currency = mongoose.model<ICurrency>('Currency', CurrencySchema);
export const Timezone = mongoose.model<ITimezone>('Timezone', TimezoneSchema);
