import { t } from 'elysia';

// Shipping Address Schema
export const shippingAddressSchema = t.Object({
  name: t.String({ minLength: 1, error: 'name ต้องไม่ว่าง' }),
  phone: t.String({ minLength: 1, error: 'phone ต้องไม่ว่าง' }),
  address: t.String({ minLength: 1, error: 'address ต้องไม่ว่าง' }),
  city: t.String({ minLength: 1, error: 'city ต้องไม่ว่าง' }),
  state: t.String({ minLength: 1, error: 'state ต้องไม่ว่าง' }),
  postalCode: t.String({ minLength: 1, error: 'postalCode ต้องไม่ว่าง' }),
  country: t.String({ minLength: 1, error: 'country ต้องไม่ว่าง' }),
  isDefault: t.Optional(t.<PERSON>({ error: 'isDefault ต้องเป็น true หรือ false เท่านั้น' })),
});

// Package Details Schema
export const packageDetailsSchema = t.Object({
  length: t.Number({ minimum: 0, error: 'length ต้องไม่น้อยกว่า 0' }),
  width: t.Number({ minimum: 0, error: 'width ต้องไม่น้อยกว่า 0' }),
  height: t.Number({ minimum: 0, error: 'height ต้องไม่น้อยกว่า 0' }),
  packageType: t.Union([
    t.Literal('small', { error: 'packageType ต้องเป็น small, medium, large หรือ oversize' }),
    t.Literal('medium', { error: 'packageType ต้องเป็น small, medium, large หรือ oversize' }),
    t.Literal('large', { error: 'packageType ต้องเป็น small, medium, large หรือ oversize' }),
    t.Literal('oversize', { error: 'packageType ต้องเป็น small, medium, large หรือ oversize' }),
  ]),
  items: t.Array(
    t.Object({
      productId: t.String({ minLength: 1, error: 'productId ต้องไม่ว่าง' }),
      quantity: t.Number({ minimum: 1, error: 'quantity ต้องไม่น้อยกว่า 1' }),
      weight: t.Number({ minimum: 0, error: 'weight ต้องไม่น้อยกว่า 0' }),
    }),
  ),
});

// Create Shipping Schema
export const createShippingSchema = t.Object({
  orderId: t.String({ minLength: 1, error: 'orderId ต้องไม่ว่าง' }),
  shippingMethod: t.Union([
    t.Literal('standard', { error: 'shippingMethod ต้องเป็น standard, express, overnight, pickup หรือ local' }),
    t.Literal('express', { error: 'shippingMethod ต้องเป็น standard, express, overnight, pickup หรือ local' }),
    t.Literal('overnight', { error: 'shippingMethod ต้องเป็น standard, express, overnight, pickup หรือ local' }),
    t.Literal('pickup', { error: 'shippingMethod ต้องเป็น standard, express, overnight, pickup หรือ local' }),
    t.Literal('local', { error: 'shippingMethod ต้องเป็น standard, express, overnight, pickup หรือ local' }),
  ]),
  fromAddress: shippingAddressSchema,
  toAddress: shippingAddressSchema,
  packageDetails: packageDetailsSchema,
  insuranceCost: t.Optional(t.Number({ minimum: 0, error: 'insuranceCost ต้องไม่น้อยกว่า 0' })),
});

// Update Shipping Status Schema
export const updateShippingStatusSchema = t.Object({
  status: t.Union([
    t.Literal('pending', { error: 'status ต้องเป็น pending, processing, shipped, delivered, failed หรือ returned' }),
    t.Literal('processing', { error: 'status ต้องเป็น pending, processing, shipped, delivered, failed หรือ returned' }),
    t.Literal('shipped', { error: 'status ต้องเป็น pending, processing, shipped, delivered, failed หรือ returned' }),
    t.Literal('delivered', { error: 'status ต้องเป็น pending, processing, shipped, delivered, failed หรือ returned' }),
    t.Literal('failed', { error: 'status ต้องเป็น pending, processing, shipped, delivered, failed หรือ returned' }),
    t.Literal('returned', { error: 'status ต้องเป็น pending, processing, shipped, delivered, failed หรือ returned' }),
  ]),
});

// Tracking Event Schema
export const trackingEventSchema = t.Object({
  status: t.String({ minLength: 1, error: 'status ต้องไม่ว่าง' }),
  location: t.String({ minLength: 1, error: 'location ต้องไม่ว่าง' }),
  description: t.String({ minLength: 1, error: 'description ต้องไม่ว่าง' }),
  timestamp: t.String({ format: 'date-time', error: 'timestamp ต้องเป็นรูปแบบ date-time' }),
  trackingCode: t.Optional(t.String({ error: 'trackingCode ต้องเป็น string หรือเป็น null' })),
});

// Add Tracking Event Schema
export const addTrackingEventSchema = t.Object({
  status: t.String({ minLength: 1, error: 'status ต้องไม่ว่าง' }),
  location: t.String({ minLength: 1, error: 'location ต้องไม่ว่าง' }),
  description: t.String({ minLength: 1, error: 'description ต้องไม่ว่าง' }),
  trackingCode: t.Optional(t.String({ error: 'trackingCode ต้องเป็น string หรือเป็น null' })),
});

// Update Shipping Info Schema
export const updateShippingInfoSchema = t.Object({
  trackingNumber: t.Optional(t.String({ error: 'trackingNumber ต้องเป็น string หรือเป็น null' })),
  carrier: t.Optional(t.String({ error: 'carrier ต้องเป็น string หรือเป็น null' })),
  estimatedDelivery: t.Optional(t.String({ format: 'date-time', error: 'estimatedDelivery ต้องเป็นรูปแบบ date-time' })),
  shippingMethod: t.Optional(
    t.Union([
      t.Literal('standard', { error: 'shippingMethod ต้องเป็น standard, express, overnight, pickup หรือ local' }),
      t.Literal('express', { error: 'shippingMethod ต้องเป็น standard, express, overnight, pickup หรือ local' }),
      t.Literal('overnight', { error: 'shippingMethod ต้องเป็น standard, express, overnight, pickup หรือ local' }),
      t.Literal('pickup', { error: 'shippingMethod ต้องเป็น standard, express, overnight, pickup หรือ local' }),
      t.Literal('local', { error: 'shippingMethod ต้องเป็น standard, express, overnight, pickup หรือ local' }),
    ]),
  ),
  insuranceCost: t.Optional(t.Number({ minimum: 0, error: 'insuranceCost ต้องไม่น้อยกว่า 0' })),
});

// Calculate Shipping Cost Schema
export const calculateShippingCostSchema = t.Object({
  weight: t.Number({ minimum: 0, error: 'weight ต้องไม่น้อยกว่า 0' }),
  shippingMethod: t.Union([
    t.Literal('standard', { error: 'shippingMethod ต้องเป็น standard, express, overnight, pickup หรือ local' }),
    t.Literal('express', { error: 'shippingMethod ต้องเป็น standard, express, overnight, pickup หรือ local' }),
    t.Literal('overnight', { error: 'shippingMethod ต้องเป็น standard, express, overnight, pickup หรือ local' }),
    t.Literal('pickup', { error: 'shippingMethod ต้องเป็น standard, express, overnight, pickup หรือ local' }),
    t.Literal('local', { error: 'shippingMethod ต้องเป็น standard, express, overnight, pickup หรือ local' }),
  ]),
  insuranceCost: t.Optional(t.Number({ minimum: 0, error: 'insuranceCost ต้องไม่น้อยกว่า 0' })),
  distance: t.Optional(t.Number({ minimum: 0, error: 'distance ต้องไม่น้อยกว่า 0' })),
});

// Shipping Filter Schema
export const shippingFilterSchema = t.Object({
  status: t.Optional(
    t.Union([
      t.Literal('pending', { error: 'status ต้องเป็น pending, processing, shipped, delivered, failed หรือ returned' }),
      t.Literal('processing', {
        error: 'status ต้องเป็น pending, processing, shipped, delivered, failed หรือ returned',
      }),
      t.Literal('shipped', { error: 'status ต้องเป็น pending, processing, shipped, delivered, failed หรือ returned' }),
      t.Literal('delivered', {
        error: 'status ต้องเป็น pending, processing, shipped, delivered, failed หรือ returned',
      }),
      t.Literal('failed', { error: 'status ต้องเป็น pending, processing, shipped, delivered, failed หรือ returned' }),
      t.Literal('returned', { error: 'status ต้องเป็น pending, processing, shipped, delivered, failed หรือ returned' }),
    ]),
  ),
  startDate: t.Optional(t.String({ format: 'date-time', error: 'startDate ต้องเป็นรูปแบบ date-time' })),
  endDate: t.Optional(t.String({ format: 'date-time', error: 'endDate ต้องเป็นรูปแบบ date-time' })),
  page: t.Optional(t.Number({ minimum: 1, error: 'page ต้องไม่น้อยกว่า 1' })),
  limit: t.Optional(t.Number({ minimum: 1, maximum: 100, error: 'limit ต้องไม่น้อยกว่า 1 และไม่มากกว่า 100' })),
});

// Shipping Response Schema
export const shippingResponseSchema = t.Object({
  success: t.Boolean(),
  message: t.String(),
  statusMessage: t.String(),
  timestamp: t.String(),
  data: t.Optional(t.Any()),
});

// Shipping List Response Schema
export const shippingListResponseSchema = t.Object({
  success: t.Boolean(),
  message: t.String(),
  statusMessage: t.String(),
  timestamp: t.String(),
  data: t.Array(t.Any()),
  total: t.Number(),
  page: t.Number(),
  limit: t.Number(),
});

// Shipping Cost Response Schema
export const shippingCostResponseSchema = t.Object({
  success: t.Boolean(),
  message: t.String(),
  statusMessage: t.String(),
  timestamp: t.String(),
  data: t.Object({
    shippingCost: t.Number(),
    insuranceCost: t.Number(),
    totalCost: t.Number(),
    estimatedDays: t.Number(),
  }),
});

// Shipping Stats Response Schema
export const shippingStatsResponseSchema = t.Object({
  success: t.Boolean(),
  message: t.String(),
  statusMessage: t.String(),
  timestamp: t.String(),
  data: t.Object({
    totalShipments: t.Number(),
    pendingShipments: t.Number(),
    shippedShipments: t.Number(),
    deliveredShipments: t.Number(),
    totalCost: t.Number(),
  }),
});
