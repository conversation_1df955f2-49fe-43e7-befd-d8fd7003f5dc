import { generateFileId } from '@/core/utils/idGenerator';
import mongoose, { type Document, Schema } from 'mongoose';

export type ShippingStatus = 'pending' | 'processing' | 'shipped' | 'delivered' | 'failed' | 'returned';
export type ShippingMethod = 'standard' | 'express' | 'overnight' | 'pickup' | 'local';
export type PackageType = 'small' | 'medium' | 'large' | 'oversize';

export interface IShippingAddress {
  name: string;
  phone: string;
  address: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  isDefault?: boolean;
}

export interface IPackageDetails {
  weight: number; // kg
  length: number; // cm
  width: number; // cm
  height: number; // cm
  packageType: PackageType;
  items: Array<{
    productId: string;
    quantity: number;
    weight: number;
  }>;
}

export interface ITrackingEvent {
  status: string;
  location: string;
  description: string;
  timestamp: Date;
  trackingCode?: string;
}

export interface IShipping extends Document {
  _id: string;
  siteId: string;
  orderId: string;

  // Shipping info
  shippingMethod: ShippingMethod;
  trackingNumber?: string;
  carrier?: string;
  estimatedDelivery?: Date;
  actualDelivery?: Date;

  // Addresses
  fromAddress: IShippingAddress;
  toAddress: IShippingAddress;

  // Package details
  packageDetails: IPackageDetails;

  // Costs
  shippingCost: number;
  insuranceCost: number;
  totalCost: number;

  // Status & tracking
  status: ShippingStatus;
  trackingEvents: ITrackingEvent[];

  // Timestamps
  createdAt: Date;
  updatedAt: Date;
  shippedAt?: Date;
  deliveredAt?: Date;
}

const shippingAddressSchema = new Schema(
  {
    name: { type: String, required: true },
    phone: { type: String, required: true },
    address: { type: String, required: true },
    city: { type: String, required: true },
    state: { type: String, required: true },
    postalCode: { type: String, required: true },
    country: { type: String, required: true },
    isDefault: { type: Boolean, default: false },
  },
  { _id: false },
);

const packageDetailsSchema = new Schema(
  {
    weight: { type: Number, required: true },
    length: { type: Number, required: true },
    width: { type: Number, required: true },
    height: { type: Number, required: true },
    packageType: {
      type: String,
      enum: ['small', 'medium', 'large', 'oversize'],
      required: true,
    },
    items: [
      {
        productId: { type: String, required: true },
        quantity: { type: Number, required: true },
        weight: { type: Number, required: true },
      },
    ],
  },
  { _id: false },
);

const trackingEventSchema = new Schema(
  {
    status: { type: String, required: true },
    location: { type: String, required: true },
    description: { type: String, required: true },
    timestamp: { type: Date, required: true },
    trackingCode: { type: String },
  },
  { _id: false },
);

const shippingSchema = new Schema<IShipping>(
  {
    _id: { type: String, default: () => generateFileId(5) },
    siteId: { type: String, required: true, index: true },
    orderId: { type: String, required: true, index: true },

    // Shipping info
    shippingMethod: {
      type: String,
      enum: ['standard', 'express', 'overnight', 'pickup', 'local'],
      required: true,
      index: true,
    },
    trackingNumber: { type: String, index: true },
    carrier: { type: String },
    estimatedDelivery: { type: Date },
    actualDelivery: { type: Date },

    // Addresses
    fromAddress: { type: shippingAddressSchema, required: true },
    toAddress: { type: shippingAddressSchema, required: true },

    // Package details
    packageDetails: { type: packageDetailsSchema, required: true },

    // Costs
    shippingCost: { type: Number, required: true, default: 0 },
    insuranceCost: { type: Number, default: 0 },
    totalCost: { type: Number, required: true, default: 0 },

    // Status & tracking
    status: {
      type: String,
      enum: ['pending', 'processing', 'shipped', 'delivered', 'failed', 'returned'],
      default: 'pending',
      index: true,
    },
    trackingEvents: [trackingEventSchema],
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

// Indexes
shippingSchema.index({ siteId: 1, orderId: 1 }, { unique: true });
shippingSchema.index({ siteId: 1, status: 1 });
shippingSchema.index({ siteId: 1, trackingNumber: 1 });
shippingSchema.index({ siteId: 1, createdAt: -1 });

// Methods
shippingSchema.methods.updateStatus = function(newStatus: ShippingStatus) {
  this.status = newStatus;

  if (newStatus === 'shipped') {
    this.shippedAt = new Date();
  }
  else if (newStatus === 'delivered') {
    this.deliveredAt = new Date();
    this.actualDelivery = new Date();
  }

  return this.save();
};

shippingSchema.methods.addTrackingEvent = function(event: ITrackingEvent) {
  this.trackingEvents.push(event);
  return this.save();
};

shippingSchema.methods.calculateShippingCost = function() {
  const baseCost = this.packageDetails.weight * 10; // 10 บาทต่อกิโลกรัม
  let methodMultiplier = 1;

  switch (this.shippingMethod) {
    case 'standard':
      methodMultiplier = 1;
      break;
    case 'express':
      methodMultiplier = 1.5;
      break;
    case 'overnight':
      methodMultiplier = 2;
      break;
    case 'pickup':
      methodMultiplier = 0.5;
      break;
    case 'local':
      methodMultiplier = 0.8;
      break;
  }

  this.shippingCost = baseCost * methodMultiplier;
  this.totalCost = this.shippingCost + this.insuranceCost;

  return this.save();
};

// Static methods
shippingSchema.statics.getShippingByOrder = function(orderId: string, siteId: string) {
  return this.findOne({ orderId, siteId });
};

shippingSchema.statics.getShippingByTracking = function(trackingNumber: string, siteId: string) {
  return this.findOne({ trackingNumber, siteId });
};

export const Shipping = mongoose.model<IShipping>('Shipping', shippingSchema);
