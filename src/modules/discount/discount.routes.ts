import { userAuthPlugin } from '@/core/plugins/auth';
import { HttpError } from '@/core/utils/error';
import { Elysia } from 'elysia';
import { Discount } from './discount.model';
import {
  applyDiscount,
  checkUserFirstTimeStatus,
  createDiscount,
  deleteDiscount,
  findApplicableDiscounts,
  getActiveDiscounts,
  getDiscountByCode,
  getDiscountsByStatus,
  getDiscountsByType,
  getDiscountStats,
  getDiscountUsageHistory,
  getValidDiscounts,
  updateDiscount,
  updateDiscountStatuses,
  validateDiscount,
} from './discount.service';

import { getResultSite } from '@/core/middleware/checkSite';
import {
  applyDiscountSchema,
  createDiscountSchema,
  discountListResponseSchema,
  discountResponseSchema,
  discountStatsResponseSchema,
  discountValidationResponseSchema,
  updateDiscountSchema,
  validateDiscountSchema,
} from './discount.schema';

// Helper function for consistent response format
const createSuccessResponse = (data: any, _message: string) => ({
  ...data,
  statusMessage: 'สำเร็จ!',
  timestamp: new Date().toISOString(),
});

export const discountRoutes = new Elysia({ prefix: '/discount' })
  .use(userAuthPlugin)
  // สร้าง discount ใหม่
  .post(
    '/create',
    async ({ body, site }: any) => {
      try {
        if (!site?._id) {
          throw new HttpError(400, 'ไม่พบข้อมูลเว็บไซต์');
        }

        const result = await createDiscount({
          siteId: site._id,
          ...body,
          startDate: new Date(body.startDate),
          endDate: new Date(body.endDate),
        });

        return createSuccessResponse(result, 'สร้าง discount สำเร็จ');
      }
      catch (error: any) {
        console.error('Error creating discount:', error);
        if (error instanceof HttpError) throw error;
        throw new HttpError(500, 'เกิดข้อผิดพลาดขณะสร้าง discount');
      }
    },
    {
      body: createDiscountSchema,
      response: discountResponseSchema,
      beforeHandle: async (c: any) => {
        await getResultSite(c, { checkExpiry: false, checkActive: false, requireSite: true });
      },
    },
  )
  // ตรวจสอบ discount และความถูกต้องของ discount (ปลอดภัย)
  .post(
    '/validate',
    async ({ body, user }: any) => {
      const { discountCode, target, orderAmount, items } = body;
      console.log(user);

      try {
        if (!user?._id) {
          throw new HttpError(400, 'ไม่พบข้อมูลเว็บไซต์ของผู้ใช้');
        }

        // คำนวณ isFirstTime ใน backend เพื่อความปลอดภัย
        const isFirstTime = await checkUserFirstTimeStatus(user._id, user.siteId);

        const result = await validateDiscount(discountCode, {
          target,
          siteId: user.siteId,
          userId: user._id,
          orderAmount,
          items,
          isFirstTime,
        });

        return createSuccessResponse(result, 'ตรวจสอบ discount สำเร็จ');
      }
      catch (error: any) {
        console.error('Error validating discount:', error);
        if (error instanceof HttpError) throw error;
        throw new HttpError(500, 'เกิดข้อผิดพลาดขณะตรวจสอบ discount');
      }
    },
    {
      body: validateDiscountSchema,
      response: discountValidationResponseSchema,
    },
  )
  // ดึง discount ตาม code
  .get(
    '/code/:code',
    async ({ params, query }: any) => {
      const { code } = params;
      const { siteId } = query;

      try {
        if (!siteId) {
          throw new HttpError(400, 'กรุณาระบุ siteId');
        }

        const discount = await getDiscountByCode(code, siteId);

        return createSuccessResponse(
          {
            success: true,
            message: 'ดึงข้อมูล discount สำเร็จ',
            data: discount,
          },
          'ดึงข้อมูล discount สำเร็จ',
        );
      }
      catch (error: any) {
        console.error('Error getting discount by code:', error);
        if (error instanceof HttpError) throw error;
        throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงข้อมูล discount');
      }
    },
    {
      response: discountResponseSchema,
    },
  )
  // ดึงรายการ discount ทั้งหมด
  .get(
    '/list',
    async ({ query }: any) => {
      const { siteId, status, type, target, page = 1, limit = 20 } = query;

      try {
        if (!siteId) {
          throw new HttpError(400, 'กรุณาระบุ siteId');
        }

        const filter: any = { siteId };
        if (status) filter.status = status;
        if (type) filter.type = type;
        if (target) filter.target = target;

        const skip = (page - 1) * limit;
        const [discounts, total] = await Promise.all([
          Discount.find(filter).sort({ createdAt: -1 }).skip(skip).limit(parseInt(limit)),
          Discount.countDocuments(filter),
        ]);

        return {
          success: true,
          message: 'ดึงรายการ discount สำเร็จ',
          statusMessage: 'สำเร็จ!',
          timestamp: new Date().toISOString(),
          data: discounts,
          total,
          page: parseInt(page),
          limit: parseInt(limit),
        };
      }
      catch (error: any) {
        console.error('Error getting discount list:', error);
        if (error instanceof HttpError) throw error;
        throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงรายการ discount');
      }
    },
    {
      response: discountListResponseSchema,
    },
  )
  // ดึง active discounts
  .get(
    '/active',
    async ({ query }: any) => {
      const { siteId } = query;

      try {
        if (!siteId) {
          throw new HttpError(400, 'กรุณาระบุ siteId');
        }

        const discounts = await getActiveDiscounts(siteId);

        return createSuccessResponse(
          {
            success: true,
            message: 'ดึง active discounts สำเร็จ',
            data: discounts,
          },
          'ดึง active discounts สำเร็จ',
        );
      }
      catch (error: any) {
        console.error('Error getting active discounts:', error);
        if (error instanceof HttpError) throw error;
        throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึง active discounts');
      }
    },
    {
      response: discountResponseSchema,
    },
  )
  // อัปเดต discount
  .put(
    '/:discountId',
    async ({ params, body }: any) => {
      const { discountId } = params;

      try {
        if (!discountId) {
          throw new HttpError(400, 'กรุณาระบุ discount ID');
        }

        const updateData = { ...body };
        if (body.startDate) updateData.startDate = new Date(body.startDate);
        if (body.endDate) updateData.endDate = new Date(body.endDate);

        const result = await updateDiscount(discountId, updateData);
        return createSuccessResponse(result, 'อัปเดต discount สำเร็จ');
      }
      catch (error: any) {
        console.error('Error updating discount:', error);
        if (error instanceof HttpError) throw error;
        throw new HttpError(500, 'เกิดข้อผิดพลาดขณะอัปเดต discount');
      }
    },
    {
      body: updateDiscountSchema,
      response: discountResponseSchema,
    },
  )
  // ลบ discount
  .delete(
    '/:discountId',
    async ({ params }: any) => {
      const { discountId } = params;

      try {
        if (!discountId) {
          throw new HttpError(400, 'กรุณาระบุ discount ID');
        }

        const result = await deleteDiscount(discountId);
        return createSuccessResponse(result, 'ลบ discount สำเร็จ');
      }
      catch (error: any) {
        console.error('Error deleting discount:', error);
        if (error instanceof HttpError) throw error;
        throw new HttpError(500, 'เกิดข้อผิดพลาดขณะลบ discount');
      }
    },
    {
      response: discountResponseSchema,
    },
  )
  // ใช้ discount
  .post(
    '/:discountId/apply',
    async ({ params, body, user }: any) => {
      const { discountId } = params;
      const { orderId, discountAmount } = body;

      try {
        if (!discountId) {
          throw new HttpError(400, 'กรุณาระบุ discount ID');
        }

        if (!user?._id) {
          throw new HttpError(401, 'ไม่พบข้อมูลผู้ใช้');
        }

        const result = await applyDiscount(discountId, orderId, user._id, discountAmount);
        return createSuccessResponse(result, 'ใช้ discount สำเร็จ');
      }
      catch (error: any) {
        console.error('Error applying discount:', error);
        if (error instanceof HttpError) throw error;
        throw new HttpError(500, 'เกิดข้อผิดพลาดขณะใช้ discount');
      }
    },
    {
      body: applyDiscountSchema,
      response: discountResponseSchema,
    },
  )
  // ดึง discounts ตามสถานะ
  .get(
    '/status/:status',
    async ({ params, query }: any) => {
      const { status } = params;
      const { siteId } = query;

      try {
        if (!siteId) {
          throw new HttpError(400, 'กรุณาระบุ siteId');
        }

        const discounts = await getDiscountsByStatus(siteId, status);

        return createSuccessResponse(
          {
            success: true,
            message: 'ดึง discounts ตามสถานะสำเร็จ',
            data: discounts,
          },
          'ดึง discounts ตามสถานะสำเร็จ',
        );
      }
      catch (error: any) {
        console.error('Error getting discounts by status:', error);
        if (error instanceof HttpError) throw error;
        throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึง discounts ตามสถานะ');
      }
    },
    {
      response: discountResponseSchema,
    },
  )
  // ดึง discounts ตามประเภท
  .get(
    '/type/:type',
    async ({ params, query }: any) => {
      const { type } = params;
      const { siteId } = query;

      try {
        if (!siteId) {
          throw new HttpError(400, 'กรุณาระบุ siteId');
        }

        const discounts = await getDiscountsByType(siteId, type);

        return createSuccessResponse(
          {
            success: true,
            message: 'ดึง discounts ตามประเภทสำเร็จ',
            data: discounts,
          },
          'ดึง discounts ตามประเภทสำเร็จ',
        );
      }
      catch (error: any) {
        console.error('Error getting discounts by type:', error);
        if (error instanceof HttpError) throw error;
        throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึง discounts ตามประเภท');
      }
    },
    {
      response: discountResponseSchema,
    },
  )
  // ประวัติการใช้งาน discount
  .get(
    '/:discountId/usage-history',
    async ({ params }: any) => {
      const { discountId } = params;

      try {
        if (!discountId) {
          throw new HttpError(400, 'กรุณาระบุ discount ID');
        }

        const usageHistory = await getDiscountUsageHistory(discountId);

        return createSuccessResponse(
          {
            success: true,
            message: 'ดึงประวัติการใช้งาน discount สำเร็จ',
            data: usageHistory,
          },
          'ดึงประวัติการใช้งาน discount สำเร็จ',
        );
      }
      catch (error: any) {
        console.error('Error getting discount usage history:', error);
        if (error instanceof HttpError) throw error;
        throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงประวัติการใช้งาน discount');
      }
    },
    {
      response: discountResponseSchema,
    },
  )
  // สถิติ discount
  .get(
    '/stats/:siteId',
    async ({ params }: any) => {
      const { siteId } = params;

      try {
        if (!siteId) {
          throw new HttpError(400, 'กรุณาระบุ siteId');
        }

        const stats = await getDiscountStats(siteId);

        return createSuccessResponse(
          {
            success: true,
            message: 'ดึงสถิติ discount สำเร็จ',
            data: stats,
          },
          'ดึงสถิติ discount สำเร็จ',
        );
      }
      catch (error: any) {
        console.error('Error getting discount stats:', error);
        if (error instanceof HttpError) throw error;
        throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงสถิติ discount');
      }
    },
    {
      response: discountStatsResponseSchema,
    },
  )
  // อัปเดตสถานะ discount ทั้งหมด
  .post(
    '/update-statuses',
    async ({ body }: any) => {
      const { siteId } = body;

      try {
        if (!siteId) {
          throw new HttpError(400, 'กรุณาระบุ siteId');
        }

        const result = await updateDiscountStatuses(siteId);
        return createSuccessResponse(result, 'อัปเดตสถานะ discount สำเร็จ');
      }
      catch (error: any) {
        console.error('Error updating discount statuses:', error);
        if (error instanceof HttpError) throw error;
        throw new HttpError(500, 'เกิดข้อผิดพลาดขณะอัปเดตสถานะ discount');
      }
    },
    {
      response: discountResponseSchema,
    },
  )
  // ค้นหา discount ที่ใช้ได้กับ order
  .post(
    '/find-applicable',
    async ({ body }: any) => {
      const { siteId, orderAmount, items, userId, isFirstTime } = body;

      try {
        if (!siteId) {
          throw new HttpError(400, 'กรุณาระบุ siteId');
        }

        const discounts = await findApplicableDiscounts(siteId, {
          orderAmount,
          items,
          userId,
          isFirstTime,
        });

        return createSuccessResponse(
          {
            success: true,
            message: 'ค้นหา applicable discounts สำเร็จ',
            data: discounts,
          },
          'ค้นหา applicable discounts สำเร็จ',
        );
      }
      catch (error: any) {
        console.error('Error finding applicable discounts:', error);
        if (error instanceof HttpError) throw error;
        throw new HttpError(500, 'เกิดข้อผิดพลาดขณะค้นหา applicable discounts');
      }
    },
    {
      response: discountResponseSchema,
    },
  )
  // ดึง valid discounts
  .get(
    '/valid',
    async ({ query }: any) => {
      const { siteId } = query;

      try {
        if (!siteId) {
          throw new HttpError(400, 'กรุณาระบุ siteId');
        }

        const discounts = await getValidDiscounts(siteId);

        return createSuccessResponse(
          {
            success: true,
            message: 'ดึง valid discounts สำเร็จ',
            data: discounts,
          },
          'ดึง valid discounts สำเร็จ',
        );
      }
      catch (error: any) {
        console.error('Error getting valid discounts:', error);
        if (error instanceof HttpError) throw error;
        throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึง valid discounts');
      }
    },
    {
      response: discountResponseSchema,
    },
  );
