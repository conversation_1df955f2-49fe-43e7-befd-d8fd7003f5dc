import mongoose, { type Document, Schema } from 'mongoose';
import { generateFileId } from '../../core/utils';
export type DiscountType =
  | 'percentage'
  | 'fixed'
  | 'free_shipping'
  | 'buy_x_get_y'
  | 'bundle_discount'
  | 'tiered_discount'
  | 'cashback';
export type DiscountStatus = 'active' | 'inactive' | 'expired' | 'scheduled';
export type DiscountTarget = 'all' | 'package' | 'category' | 'product' | 'user_group' | 'first_time';

export interface IDiscountCondition {
  minOrderAmount?: number;
  maxOrderAmount?: number;
  minQuantity?: number;
  maxQuantity?: number;
  applicableCategories?: string[];
  applicableProducts?: string[];
  excludedProducts?: string[];
  userGroups?: string[];
  firstTimeOnly?: boolean;
  usageLimit?: number;
  usageLimitPerUser?: number;
  dayOfWeek?: number[]; // [0-6] วันในสัปดาห์ (0=อาทิตย์)
  timeRange?: { start: string; end: string; }; // เวลาในวัน เช่น "09:00"-"17:00"
  customerTier?: string[]; // ระดับลูกค้า
  minimumPurchaseHistory?: number; // ประวัติซื้อขั้นต่ำ (บาท)
  bundleProducts?: Array<{ productId: string; quantity: number; }>; // สำหรับ bundle_discount
  tierRules?: Array<{ minAmount: number; discountValue: number; }>; // สำหรับ tiered_discount
}

export interface IDiscountUsage {
  userId: string;
  orderId: string;
  discountAmount: number;
  usedAt: Date;
}

export interface IDiscount extends Document {
  _id: string;
  siteId?: string;

  // Basic info
  name: string;
  description?: string;
  code?: string; // สำหรับ coupon code

  // Discount details
  type: DiscountType;
  value: number; // percentage หรือ fixed amount
  maxDiscountAmount?: number; // สำหรับ percentage discount

  // Target & conditions
  target: DiscountTarget;
  conditions: IDiscountCondition;

  // Validity
  status: DiscountStatus;
  startDate: Date;
  endDate: Date;

  // Usage tracking
  totalUsage: number;
  totalDiscountAmount: number;
  usageHistory: IDiscountUsage[];

  // Timestamps
  createdAt: Date;
  updatedAt: Date;
  activatedAt?: Date;
  deactivatedAt?: Date;
}

const discountConditionSchema = new Schema(
  {
    minOrderAmount: { type: Number },
    maxOrderAmount: { type: Number },
    minQuantity: { type: Number },
    maxQuantity: { type: Number },
    applicableCategories: [{ type: String }],
    applicableProducts: [{ type: String }],
    excludedProducts: [{ type: String }],
    userGroups: [{ type: String }],
    firstTimeOnly: { type: Boolean, default: false },
    usageLimit: { type: Number },
    usageLimitPerUser: { type: Number },
    dayOfWeek: [{ type: Number, min: 0, max: 6 }], // 0=อาทิตย์, 6=เสาร์
    timeRange: {
      start: { type: String }, // เช่น "09:00"
      end: { type: String }, // เช่น "17:00"
    },
    customerTier: [{ type: String }],
    minimumPurchaseHistory: { type: Number },
    bundleProducts: [
      {
        productId: { type: String, required: true },
        quantity: { type: Number, required: true, min: 1 },
      },
    ],
    tierRules: [
      {
        minAmount: { type: Number, required: true, min: 0 },
        discountValue: { type: Number, required: true, min: 0 },
      },
    ],
  },
  { _id: false },
);

const discountUsageSchema = new Schema(
  {
    userId: { type: String, required: true },
    orderId: { type: String, required: true },
    discountAmount: { type: Number, required: true },
    usedAt: { type: Date, default: Date.now },
  },
  { _id: false },
);

const discountSchema = new Schema<IDiscount>(
  {
    _id: { type: String, default: () => generateFileId(5) },
    siteId: { type: String, required: false },

    // Basic info
    name: { type: String, required: true },
    description: { type: String },
    code: { type: String, unique: true, sparse: true, index: true },

    // Discount details
    type: {
      type: String,
      enum: ['percentage', 'fixed', 'free_shipping', 'buy_x_get_y', 'bundle_discount', 'tiered_discount', 'cashback'],
      required: true,
      index: true,
    },
    value: { type: Number, required: true },
    maxDiscountAmount: { type: Number },

    // Target & conditions
    target: {
      type: String,
      enum: ['all', 'package', 'category', 'product', 'user_group', 'first_time'],
      required: true,
      index: true,
    },
    conditions: { type: discountConditionSchema, required: true },

    // Validity
    status: {
      type: String,
      enum: ['active', 'inactive', 'expired', 'scheduled'],
      default: 'scheduled',
      index: true,
    },
    startDate: { type: Date, required: true, index: true },
    endDate: { type: Date, required: true, index: true },

    // Usage tracking
    totalUsage: { type: Number, default: 0 },
    totalDiscountAmount: { type: Number, default: 0 },
    usageHistory: [discountUsageSchema],
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

// Indexes for better performance
discountSchema.index({ siteId: 1, status: 1 });
discountSchema.index({ siteId: 1, startDate: 1, endDate: 1 });
discountSchema.index({ siteId: 1, type: 1 });
discountSchema.index({ siteId: 1, target: 1 });
discountSchema.index({ code: 1, siteId: 1 });
discountSchema.index({ status: 1, startDate: 1, endDate: 1 });
// เพิ่ม compound indexes สำหรับ performance
discountSchema.index({ siteId: 1, status: 1, startDate: 1, endDate: 1 });
discountSchema.index({ siteId: 1, code: 1, status: 1 });
discountSchema.index({ siteId: 1, type: 1, status: 1 });

// Methods
discountSchema.methods.isValid = function() {
  const now = new Date();

  // ตรวจสอบว่าอยู่ในช่วงเวลาที่ใช้งานได้หรือไม่
  const isInTimeRange = now >= this.startDate && now <= this.endDate;

  // ตรวจสอบสถานะ - ถ้าเป็น scheduled แต่อยู่ในช่วงเวลาแล้ว ให้ถือว่าใช้ได้
  const isStatusValid = this.status === 'active' || (this.status === 'scheduled' && isInTimeRange);

  // ตรวจสอบขด จำกัดการใช้งาน
  const isUsageLimitValid = !this.conditions.usageLimit || this.totalUsage < this.conditions.usageLimit;
  console.log('isUsageLimitValid', isStatusValid, isInTimeRange, isUsageLimitValid);
  console.log(isStatusValid && isInTimeRange && isUsageLimitValid);

  return isStatusValid && isInTimeRange && isUsageLimitValid;
};

discountSchema.methods.canUseForOrder = function(orderData: {
  userId: string;
  orderAmount: number;
  items: Array<any>;
  isFirstTime: boolean;
}) {
  const { userId, orderAmount, items, isFirstTime } = orderData;

  // ตรวจสอบเงื่อนไขขั้นต่ำ
  if (this.conditions.minOrderAmount && orderAmount < this.conditions.minOrderAmount) {
    return {
      success: false,
      reason: `ยอดสั่งซื้อขั้นต่ำต้องมากกว่าหรือเท่ากับ ${this.conditions.minOrderAmount}`,
      code: 'min_order_amount',
    };
  }

  // ตรวจสอบเงื่อนไขสูงสุด
  if (this.conditions.maxOrderAmount && orderAmount > this.conditions.maxOrderAmount) {
    return {
      success: false,
      reason: `ยอดสั่งซื้อต้องไม่เกิน ${this.conditions.maxOrderAmount}`,
      code: 'max_order_amount',
    };
  }

  // ตรวจสอบจำนวนสินค้าขั้นต่ำ (ยืดหยุ่นกับ items structure)
  if (this.conditions.minQuantity) {
    const totalQuantity = items.reduce((sum, item) => {
      return sum + (item.quantity || item.qty || 1);
    }, 0);
    if (totalQuantity < this.conditions.minQuantity) {
      return {
        success: false,
        reason: `จำนวนสินค้าต้องไม่น้อยกว่า ${this.conditions.minQuantity}`,
        code: 'min_quantity',
      };
    }
  }

  // ตรวจสอบจำนวนสินค้าสูงสุด (ยืดหยุ่นกับ items structure)
  if (this.conditions.maxQuantity) {
    const totalQuantity = items.reduce((sum, item) => {
      return sum + (item.quantity || item.qty || 1);
    }, 0);
    if (totalQuantity > this.conditions.maxQuantity) {
      return {
        success: false,
        reason: `จำนวนสินค้าต้องไม่เกิน ${this.conditions.maxQuantity}`,
        code: 'max_quantity',
      };
    }
  }

  // ตรวจสอบสินค้าที่ใช้ได้ (ยืดหยุ่นกับ items structure)
  if (this.conditions.applicableProducts && this.conditions.applicableProducts.length > 0) {
    const hasApplicableProduct = items.some(item => {
      const productId = item.productId || item.product_id || item.id;
      return productId && this.conditions.applicableProducts.includes(productId);
    });
    if (!hasApplicableProduct) {
      return {
        success: false,
        reason: `ไม่มีสินค้าที่เข้าเงื่อนไขโปรโมชั่นนี้ในตะกร้า`,
        code: 'applicable_products',
      };
    }
  }

  // ตรวจสอบหมวดหมู่ที่ใช้ได้ (ยืดหยุ่นกับ items structure)
  if (this.conditions.applicableCategories && this.conditions.applicableCategories.length > 0) {
    const hasApplicableCategory = items.some(item => {
      const categoryId = item.categoryId || item.category_id || item.category;
      return categoryId && this.conditions.applicableCategories.includes(categoryId);
    });
    if (!hasApplicableCategory) {
      return {
        success: false,
        reason: `ไม่มีสินค้าที่อยู่ในหมวดหมู่ที่เข้าเงื่อนไขโปรโมชั่นนี้`,
        code: 'applicable_categories',
      };
    }
  }

  // ตรวจสอบสินค้าที่ไม่ใช้ได้ (ยืดหยุ่นกับ items structure)
  if (this.conditions.excludedProducts && this.conditions.excludedProducts.length > 0) {
    const hasExcludedProduct = items.some(item => {
      const productId = item.productId || item.product_id || item.id;
      return productId && this.conditions.excludedProducts.includes(productId);
    });
    if (hasExcludedProduct) {
      return {
        success: false,
        reason: `มีสินค้าที่ไม่เข้าเงื่อนไขโปรโมชั่นนี้ในตะกร้า`,
        code: 'excluded_products',
      };
    }
  }

  // ตรวจสอบลูกค้าใหม่
  if (this.conditions.firstTimeOnly && !isFirstTime) {
    return {
      success: false,
      reason: `โปรโมชั่นนี้สำหรับลูกค้าใหม่เท่านั้น`,
      code: 'first_time_only',
    };
  }

  // ตรวจสอบการใช้งานต่อผู้ใช้
  if (this.conditions.usageLimitPerUser) {
    const userUsageCount = this.usageHistory.filter((usage: any) => usage.userId === userId).length;
    if (userUsageCount >= this.conditions.usageLimitPerUser) {
      return {
        success: false,
        reason: `คุณใช้โปรโมชั่นนี้ครบจำนวนครั้งที่กำหนดแล้ว`,
        code: 'usage_limit_per_user',
      };
    }
  }

  // ตรวจสอบวันในสัปดาห์
  if (this.conditions.dayOfWeek && this.conditions.dayOfWeek.length > 0) {
    const currentDay = new Date().getDay();
    if (!this.conditions.dayOfWeek.includes(currentDay)) {
      const dayNames = ['อาทิตย์', 'จันทร์', 'อังคาร', 'พุธ', 'พฤหัสบดี', 'ศุกร์', 'เสาร์'];
      const allowedDays = this.conditions.dayOfWeek.map((day: any) => dayNames[day]).join(', ');
      return {
        success: false,
        reason: `โปรโมชั่นนี้ใช้ได้เฉพาะวัน: ${allowedDays}`,
        code: 'day_of_week',
      };
    }
  }

  // ตรวจสอบช่วงเวลา
  if (this.conditions.timeRange) {
    const now = new Date();
    const currentTime = now.getHours().toString().padStart(2, '0') + ':' + now.getMinutes().toString().padStart(2, '0');
    const { start, end } = this.conditions.timeRange;

    if (currentTime < start || currentTime > end) {
      return {
        success: false,
        reason: `โปรโมชั่นนี้ใช้ได้เฉพาะเวลา ${start} - ${end}`,
        code: 'time_range',
      };
    }
  }

  // ตรวจสอบ bundle products สำหรับ bundle_discount
  if (this.type === 'bundle_discount' && this.conditions.bundleProducts) {
    const bundleCheck = this.conditions.bundleProducts.every((bundleItem: any) => {
      const foundItem = items.find(item => {
        const productId = item.productId || item.product_id || item.id;
        return productId === bundleItem.productId;
      });

      if (!foundItem) return false;

      const quantity = foundItem.quantity || foundItem.qty || 1;
      return quantity >= bundleItem.quantity;
    });

    if (!bundleCheck) {
      return {
        success: false,
        reason: `ไม่มีสินค้าครบตามเงื่อนไขแพ็คเกจ`,
        code: 'bundle_products',
      };
    }
  }

  return { success: true };
};

discountSchema.methods.calculateDiscount = function(orderAmount: number, _items?: Array<any>) {
  let discountAmount = 0;

  switch (this.type) {
    case 'percentage':
      discountAmount = (orderAmount * this.value) / 100;
      if (this.maxDiscountAmount) {
        discountAmount = Math.min(discountAmount, this.maxDiscountAmount);
      }
      break;
    case 'fixed':
      discountAmount = this.value;
      break;
    case 'free_shipping':
      discountAmount = 0; // จะคำนวณแยกในระบบ shipping
      break;
    case 'buy_x_get_y':
      discountAmount = 0; // จะคำนวณแยกตาม logic ของแต่ละร้าน
      break;
    case 'bundle_discount':
      // ส่วนลดแบบแพ็คเกจ - ส่วนลดคงที่เมื่อซื้อครบตามเงื่อนไข
      discountAmount = this.value;
      break;
    case 'tiered_discount':
      // ส่วนลดแบบขั้นบันได
      if (this.conditions.tierRules && this.conditions.tierRules.length > 0) {
        // เรียงจากมากไปน้อย
        const sortedTiers = this.conditions.tierRules.sort((a: any, b: any) => b.minAmount - a.minAmount);

        for (const tier of sortedTiers) {
          if (orderAmount >= tier.minAmount) {
            discountAmount = tier.discountValue;
            break;
          }
        }
      }
      break;
    case 'cashback':
      // คืนเงิน - คำนวณเหมือน percentage แต่จะจ่ายในรูปแบบ cashback
      discountAmount = (orderAmount * this.value) / 100;
      if (this.maxDiscountAmount) {
        discountAmount = Math.min(discountAmount, this.maxDiscountAmount);
      }
      break;
  }

  return Math.min(discountAmount, orderAmount);
};

discountSchema.methods.recordUsage = function(usageData: { userId: string; orderId: string; discountAmount: number; }) {
  this.totalUsage += 1;
  this.totalDiscountAmount += usageData.discountAmount;
  this.usageHistory.push({
    userId: usageData.userId,
    orderId: usageData.orderId,
    discountAmount: usageData.discountAmount,
    usedAt: new Date(),
  });

  return this.save();
};

// Auto-update status based on current time
discountSchema.methods.updateStatus = function() {
  const now = new Date();

  if (now < this.startDate) {
    this.status = 'scheduled';
  }
  else if (now > this.endDate) {
    this.status = 'expired';
    if (!this.deactivatedAt) {
      this.deactivatedAt = new Date();
    }
  }
  else if (this.status === 'scheduled') {
    this.status = 'active';
    if (!this.activatedAt) {
      this.activatedAt = new Date();
    }
  }

  return this.save();
};

// Get remaining usage count
discountSchema.methods.getRemainingUsage = function() {
  if (!this.conditions.usageLimit) return null;
  return Math.max(0, this.conditions.usageLimit - this.totalUsage);
};

// Get user usage count
discountSchema.methods.getUserUsageCount = function(userId: string) {
  return this.usageHistory.filter((usage: any) => usage.userId === userId).length;
};

// Get remaining usage for specific user
discountSchema.methods.getUserRemainingUsage = function(userId: string) {
  if (!this.conditions.usageLimitPerUser) return null;
  const userUsageCount = this.getUserUsageCount(userId);
  return Math.max(0, this.conditions.usageLimitPerUser - userUsageCount);
};

// Static methods
discountSchema.statics.getActiveDiscounts = function(siteId: string) {
  const now = new Date();
  return this.find({
    siteId,
    $or: [{ status: 'active' }, { status: 'scheduled', startDate: { $lte: now }, endDate: { $gte: now } }],
    startDate: { $lte: now },
    endDate: { $gte: now },
  }).sort({ createdAt: -1 });
};

discountSchema.statics.getDiscountByCode = function(code: string, siteId: string) {
  return this.findOne({ code, siteId });
};

// Get valid discounts (active or scheduled but in time range)
discountSchema.statics.getValidDiscounts = function(siteId: string) {
  const now = new Date();
  return this.find({
    siteId,
    $or: [{ status: 'active' }, { status: 'scheduled', startDate: { $lte: now } }],
    startDate: { $lte: now },
    endDate: { $gte: now },
  }).sort({ createdAt: -1 });
};

// Get expired discounts
discountSchema.statics.getExpiredDiscounts = function(siteId: string) {
  const now = new Date();
  return this.find({
    siteId,
    $or: [{ status: 'expired' }, { endDate: { $lt: now } }],
  }).sort({ endDate: -1 });
};

// Get scheduled discounts
discountSchema.statics.getScheduledDiscounts = function(siteId: string) {
  const now = new Date();
  return this.find({
    siteId,
    status: 'scheduled',
    startDate: { $gt: now },
  }).sort({ startDate: 1 });
};

// Auto-update all discount statuses
discountSchema.statics.updateAllStatuses = function(siteId?: string) {
  const now = new Date();
  const filter = siteId ? { siteId } : {};

  return Promise.all([
    // Update to expired
    this.updateMany(
      { ...filter, endDate: { $lt: now }, status: { $ne: 'expired' } },
      {
        status: 'expired',
        deactivatedAt: now,
      },
    ),
    // Update scheduled to active
    this.updateMany(
      {
        ...filter,
        status: 'scheduled',
        startDate: { $lte: now },
        endDate: { $gte: now },
      },
      {
        status: 'active',
        activatedAt: now,
      },
    ),
  ]);
};

// Get discount statistics
discountSchema.statics.getStats = function(siteId: string) {
  return this.aggregate([
    { $match: { siteId } },
    {
      $group: {
        _id: null,
        total: { $sum: 1 },
        active: {
          $sum: {
            $cond: [{ $eq: ['$status', 'active'] }, 1, 0],
          },
        },
        scheduled: {
          $sum: {
            $cond: [{ $eq: ['$status', 'scheduled'] }, 1, 0],
          },
        },
        expired: {
          $sum: {
            $cond: [{ $eq: ['$status', 'expired'] }, 1, 0],
          },
        },
        totalUsage: { $sum: '$totalUsage' },
        totalDiscountAmount: { $sum: '$totalDiscountAmount' },
      },
    },
  ]);
};

// Find discounts by target and conditions
discountSchema.statics.findApplicableDiscounts = function(
  siteId: string,
  orderData: {
    orderAmount: number;
    items: Array<any>;
    userId: string;
    isFirstTime: boolean;
  },
) {
  const now = new Date();

  return this.find({
    siteId,
    $or: [{ status: 'active' }, { status: 'scheduled', startDate: { $lte: now } }],
    startDate: { $lte: now },
    endDate: { $gte: now },
    'conditions.minOrderAmount': { $lte: orderData.orderAmount },
  }).sort({ value: -1 }); // Sort by discount value descending
};

export const Discount = mongoose.model<IDiscount>('Discount', discountSchema);
