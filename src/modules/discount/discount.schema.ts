import { t } from 'elysia';

// Discount Condition Schema
export const discountConditionSchema = t.Object({
  minOrderAmount: t.Optional(t.Number({ minimum: 0, error: 'ยอดสั่งซื้อต้องเป็นตัวเลขและไม่ติดลบ' })),
  maxOrderAmount: t.Optional(t.Number({ minimum: 0, error: 'ยอดสั่งซื้อต้องเป็นตัวเลขและไม่ติดลบ' })),
  minQuantity: t.Optional(t.Number({ minimum: 1, error: 'จำนวนสินค้าต้องเป็นตัวเลขและมากกว่า 0' })),
  maxQuantity: t.Optional(t.Number({ minimum: 1, error: 'จำนวนสินค้าต้องเป็นตัวเลขและมากกว่า 0' })),
  applicableCategories: t.Optional(t.Array(t.String({ error: 'หมวดหมู่ต้องเป็นข้อความ' }))),
  applicableProducts: t.Optional(t.Array(t.String({ error: 'สินค้า ID ต้องเป็นข้อความ' }))),
  excludedProducts: t.Optional(t.Array(t.String({ error: 'สินค้า ID ต้องเป็นข้อความ' }))),
  userGroups: t.Optional(t.Array(t.String({ error: 'กลุ่มผู้ใช้ต้องเป็นข้อความ' }))),
  firstTimeOnly: t.Optional(t<PERSON>({ error: 'firstTimeOnly ต้องเป็น true หรือ false เท่านั้น' })),
  usageLimit: t.Optional(t.Number({ minimum: 1, error: 'จำนวนครั้งใช้งานต้องเป็นตัวเลขและมากกว่า 0' })),
  usageLimitPerUser: t.Optional(t.Number({ minimum: 1, error: 'จำนวนครั้งใช้งานต่อผู้ใช้ต้องเป็นตัวเลขและมากกว่า 0' })),
  dayOfWeek: t.Optional(t.Array(t.Number({ minimum: 0, maximum: 6, error: 'วันในสัปดาห์ต้องเป็น 0-6' }))),
  timeRange: t.Optional(
    t.Object({
      start: t.String({ error: 'เวลาเริ่มต้องเป็นรูปแบบ HH:MM' }),
      end: t.String({ error: 'เวลาสิ้นสุดต้องเป็นรูปแบบ HH:MM' }),
    }),
  ),
  customerTier: t.Optional(t.Array(t.String({ error: 'ระดับลูกค้าต้องเป็นข้อความ' }))),
  minimumPurchaseHistory: t.Optional(t.Number({ minimum: 0, error: 'ประวัติซื้อขั้นต่ำต้องเป็นตัวเลขและไม่ติดลบ' })),
  bundleProducts: t.Optional(
    t.Array(
      t.Object({
        productId: t.String({ error: 'Product ID ต้องเป็นข้อความ' }),
        quantity: t.Number({ minimum: 1, error: 'จำนวนต้องเป็นตัวเลขและมากกว่า 0' }),
      }),
    ),
  ),
  tierRules: t.Optional(
    t.Array(
      t.Object({
        minAmount: t.Number({ minimum: 0, error: 'ยอดขั้นต่ำต้องเป็นตัวเลขและไม่ติดลบ' }),
        discountValue: t.Number({ minimum: 0, error: 'ค่าส่วนลดต้องเป็นตัวเลขและไม่ติดลบ' }),
      }),
    ),
  ),
});

// Create Discount Schema
export const createDiscountSchema = t.Object({
  name: t.String({ minLength: 1, error: 'ชื่อต้องไม่ว่าง' }),
  description: t.Optional(t.String({ error: 'รายละเอียดต้องเป็นข้อความ' })),
  code: t.Optional(t.String({ error: 'โค้ดต้องเป็นข้อความ' })),
  type: t.Union(
    [
      t.Literal('percentage', { error: 'ต้องเป็น percentage เท่านั้น' }),
      t.Literal('fixed', { error: 'ต้องเป็น fixed เท่านั้น' }),
      t.Literal('free_shipping', { error: 'ต้องเป็น free_shipping เท่านั้น' }),
      t.Literal('buy_x_get_y', { error: 'ต้องเป็น buy_x_get_y เท่านั้น' }),
      t.Literal('bundle_discount', { error: 'ต้องเป็น bundle_discount เท่านั้น' }),
      t.Literal('tiered_discount', { error: 'ต้องเป็น tiered_discount เท่านั้น' }),
      t.Literal('cashback', { error: 'ต้องเป็น cashback เท่านั้น' }),
    ],
    { error: 'ประเภทส่วนลดไม่ถูกต้อง' },
  ),
  value: t.Number({ minimum: 0, error: 'ค่าต้องเป็นตัวเลขและไม่ติดลบ' }),
  maxDiscountAmount: t.Optional(t.Number({ minimum: 0, error: 'จำนวนเงินสูงสุดต้องเป็นตัวเลขและไม่ติดลบ' })),
  target: t.Union(
    [
      t.Literal('all', { error: 'ต้องเป็น all เท่านั้น' }),
      t.Literal('package', { error: 'ต้องเป็น package เท่านั้น' }),
      t.Literal('category', { error: 'ต้องเป็น category เท่านั้น' }),
      t.Literal('product', { error: 'ต้องเป็น product เท่านั้น' }),
      t.Literal('user_group', { error: 'ต้องเป็น user_group เท่านั้น' }),
      t.Literal('first_time', { error: 'ต้องเป็น first_time เท่านั้น' }),
    ],
    { error: 'เป้าหมายส่วนลดไม่ถูกต้อง' },
  ),
  conditions: discountConditionSchema,
  startDate: t.String({ format: 'date-time', error: 'วันที่เริ่มต้นต้องเป็นวันที่' }),
  endDate: t.String({ format: 'date-time', error: 'วันที่สิ้นสุดต้องเป็นวันที่' }),
});

// Update Discount Schema
export const updateDiscountSchema = t.Object({
  name: t.Optional(t.String({ minLength: 1, error: 'ชื่อต้องไม่ว่าง' })),
  description: t.Optional(t.String({ error: 'รายละเอียดต้องเป็นข้อความ' })),
  code: t.Optional(t.String({ error: 'โค้ดต้องเป็นข้อความ' })),
  type: t.Optional(
    t.Union(
      [
        t.Literal('percentage', { error: 'ต้องเป็น percentage เท่านั้น' }),
        t.Literal('fixed', { error: 'ต้องเป็น fixed เท่านั้น' }),
        t.Literal('free_shipping', { error: 'ต้องเป็น free_shipping เท่านั้น' }),
        t.Literal('buy_x_get_y', { error: 'ต้องเป็น buy_x_get_y เท่านั้น' }),
        t.Literal('bundle_discount', { error: 'ต้องเป็น bundle_discount เท่านั้น' }),
        t.Literal('tiered_discount', { error: 'ต้องเป็น tiered_discount เท่านั้น' }),
        t.Literal('cashback', { error: 'ต้องเป็น cashback เท่านั้น' }),
      ],
      { error: 'ประเภทส่วนลดไม่ถูกต้อง' },
    ),
  ),
  value: t.Optional(t.Number({ minimum: 0, error: 'ค่าต้องเป็นตัวเลขและไม่ติดลบ' })),
  maxDiscountAmount: t.Optional(t.Number({ minimum: 0, error: 'จำนวนเงินสูงสุดต้องเป็นตัวเลขและไม่ติดลบ' })),
  target: t.Optional(
    t.Union(
      [
        t.Literal('all', { error: 'ต้องเป็น all เท่านั้น' }),
        t.Literal('package', { error: 'ต้องเป็น package เท่านั้น' }),
        t.Literal('category', { error: 'ต้องเป็น category เท่านั้น' }),
        t.Literal('product', { error: 'ต้องเป็น product เท่านั้น' }),
        t.Literal('user_group', { error: 'ต้องเป็น user_group เท่านั้น' }),
        t.Literal('first_time', { error: 'ต้องเป็น first_time เท่านั้น' }),
      ],
      { error: 'เป้าหมายส่วนลดไม่ถูกต้อง' },
    ),
  ),
  conditions: t.Optional(discountConditionSchema),
  startDate: t.Optional(t.String({ format: 'date-time', error: 'วันที่เริ่มต้นต้องเป็นวันที่' })),
  endDate: t.Optional(t.String({ format: 'date-time', error: 'วันที่สิ้นสุดต้องเป็นวันที่' })),
  status: t.Optional(
    t.Union(
      [
        t.Literal('active', { error: 'ต้องเป็น active เท่านั้น' }),
        t.Literal('inactive', { error: 'ต้องเป็น inactive เท่านั้น' }),
        t.Literal('expired', { error: 'ต้องเป็น expired เท่านั้น' }),
        t.Literal('scheduled', { error: 'ต้องเป็น scheduled เท่านั้น' }),
      ],
      { error: 'สถานะไม่ถูกต้อง' },
    ),
  ),
});

// Validate Discount Schema
export const validateDiscountSchema = t.Object({
  discountCode: t.String({ minLength: 1, error: 'discountCode ต้องไม่ว่าง' }),
  orderAmount: t.Number({ minimum: 0, error: 'orderAmount ต้องเป็นตัวเลขและไม่ติดลบ' }),
  target: t.Optional(
    t.Union(
      [
        t.Literal('all', { error: 'ต้องเป็น all เท่านั้น' }),
        t.Literal('package', { error: 'ต้องเป็น package เท่านั้น' }),
        t.Literal('category', { error: 'ต้องเป็น category เท่านั้น' }),
        t.Literal('product', { error: 'ต้องเป็น product เท่านั้น' }),
        t.Literal('user_group', { error: 'ต้องเป็น user_group เท่านั้น' }),
        t.Literal('first_time', { error: 'ต้องเป็น first_time เท่านั้น' }),
      ],
      { error: 'เป้าหมายส่วนลดไม่ถูกต้อง' },
    ),
  ),
  items: t.Array(t.Object({})),
});

// Apply Discount Schema
export const applyDiscountSchema = t.Object({
  orderId: t.String({ minLength: 1, error: 'orderId ต้องไม่ว่าง' }),
  discountAmount: t.Number({ minimum: 0, error: 'discountAmount ต้องเป็นตัวเลขและไม่ติดลบ' }),
});

// Discount Filter Schema
export const discountFilterSchema = t.Object({
  status: t.Optional(
    t.Union(
      [
        t.Literal('active', { error: 'ต้องเป็น active เท่านั้น' }),
        t.Literal('inactive', { error: 'ต้องเป็น inactive เท่านั้น' }),
        t.Literal('expired', { error: 'ต้องเป็น expired เท่านั้น' }),
        t.Literal('scheduled', { error: 'ต้องเป็น scheduled เท่านั้น' }),
      ],
      { error: 'สถานะไม่ถูกต้อง' },
    ),
  ),
  type: t.Optional(
    t.Union(
      [
        t.Literal('percentage', { error: 'ต้องเป็น percentage เท่านั้น' }),
        t.Literal('fixed', { error: 'ต้องเป็น fixed เท่านั้น' }),
        t.Literal('free_shipping', { error: 'ต้องเป็น free_shipping เท่านั้น' }),
        t.Literal('buy_x_get_y', { error: 'ต้องเป็น buy_x_get_y เท่านั้น' }),
        t.Literal('bundle_discount', { error: 'ต้องเป็น bundle_discount เท่านั้น' }),
        t.Literal('tiered_discount', { error: 'ต้องเป็น tiered_discount เท่านั้น' }),
        t.Literal('cashback', { error: 'ต้องเป็น cashback เท่านั้น' }),
      ],
      { error: 'ประเภทส่วนลดไม่ถูกต้อง' },
    ),
  ),
  target: t.Optional(
    t.Union(
      [
        t.Literal('all', { error: 'ต้องเป็น all เท่านั้น' }),
        t.Literal('package', { error: 'ต้องเป็น package เท่านั้น' }),
        t.Literal('category', { error: 'ต้องเป็น category เท่านั้น' }),
        t.Literal('product', { error: 'ต้องเป็น product เท่านั้น' }),
        t.Literal('user_group', { error: 'ต้องเป็น user_group เท่านั้น' }),
        t.Literal('first_time', { error: 'ต้องเป็น first_time เท่านั้น' }),
      ],
      { error: 'เป้าหมายส่วนลดไม่ถูกต้อง' },
    ),
  ),
  page: t.Optional(t.Number({ minimum: 1, error: 'page ต้องเป็นตัวเลขและมากกว่า 0' })),
  limit: t.Optional(t.Number({ minimum: 1, maximum: 100, error: 'limit ต้องเป็นตัวเลข 1-100' })),
});

// Discount Response Schema
export const discountResponseSchema = t.Object({
  success: t.Boolean({ error: 'success ต้องเป็น true หรือ false เท่านั้น' }),
  message: t.String({ error: 'message ต้องเป็นข้อความ' }),
  statusMessage: t.String({ error: 'statusMessage ต้องเป็นข้อความ' }),
  timestamp: t.String({ error: 'timestamp ต้องเป็นข้อความ' }),
  data: t.Optional(t.Any()),
});

// Discount List Response Schema
export const discountListResponseSchema = t.Object({
  success: t.Boolean({ error: 'success ต้องเป็น true หรือ false เท่านั้น' }),
  message: t.String({ error: 'message ต้องเป็นข้อความ' }),
  statusMessage: t.String({ error: 'statusMessage ต้องเป็นข้อความ' }),
  timestamp: t.String({ error: 'timestamp ต้องเป็นข้อความ' }),
  data: t.Array(t.Any()),
  total: t.Number({ error: 'total ต้องเป็นตัวเลข' }),
  page: t.Number({ error: 'page ต้องเป็นตัวเลข' }),
  limit: t.Number({ error: 'limit ต้องเป็นตัวเลข' }),
});

// Discount Validation Response Schema
export const discountValidationResponseSchema = t.Object({
  success: t.Boolean({ error: 'success ต้องเป็น true หรือ false เท่านั้น' }),
  message: t.String({ error: 'message ต้องเป็นข้อความ' }),
  statusMessage: t.String({ error: 'statusMessage ต้องเป็นข้อความ' }),
  timestamp: t.String({ error: 'timestamp ต้องเป็นข้อความ' }),
  data: t.Object({
    discount: t.Any(),
    discountAmount: t.Number({ error: 'discountAmount ต้องเป็นตัวเลข' }),
    finalAmount: t.Number({ error: 'finalAmount ต้องเป็นตัวเลข' }),
  }),
});

// Discount Stats Response Schema
export const discountStatsResponseSchema = t.Object({
  success: t.Boolean({ error: 'success ต้องเป็น true หรือ false เท่านั้น' }),
  message: t.String({ error: 'message ต้องเป็นข้อความ' }),
  statusMessage: t.String({ error: 'statusMessage ต้องเป็นข้อความ' }),
  timestamp: t.String({ error: 'timestamp ต้องเป็นข้อความ' }),
  data: t.Object({
    totalDiscounts: t.Number({ error: 'totalDiscounts ต้องเป็นตัวเลข' }),
    activeDiscounts: t.Number({ error: 'activeDiscounts ต้องเป็นตัวเลข' }),
    totalUsage: t.Number({ error: 'totalUsage ต้องเป็นตัวเลข' }),
    totalDiscountAmount: t.Number({ error: 'totalDiscountAmount ต้องเป็นตัวเลข' }),
  }),
});
