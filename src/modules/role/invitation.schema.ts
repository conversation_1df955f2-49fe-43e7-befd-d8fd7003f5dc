import { t } from 'elysia';

// Invitation status types
export const invitationStatusTypes = t.Union([
  t.Literal('pending'),
  t.Literal('accepted'),
  t.Literal('rejected'),
  t.Literal('expired'),
]);

// Role types
export const roleTypes = t.Union([t.Literal('owner'), t.Literal('admin'), t.<PERSON>teral('editor'), t.<PERSON>teral('viewer')]);

// Create invitation schema
export const createInvitationSchema = t.Object({
  toUserId: t.String({
    minLength: 1,
    error: 'toUserId ต้องเป็นข้อความและไม่ว่าง',
  }),
  toEmail: t.Optional(
    t.String({
      format: 'email',
      error: 'toEmail ต้องเป็นรูปแบบอีเมลที่ถูกต้อง',
    }),
  ),
  role: roleTypes,
  message: t.Optional(
    t.String({
      maxLength: 500,
      error: 'ข้อความต้องไม่เกิน 500 ตัวอักษร',
    }),
  ),
});

// Accept/Reject invitation schema
export const invitationActionSchema = t.Object({
  invitationId: t.String({
    minLength: 1,
    error: 'invitationId ต้องเป็นข้อความและไม่ว่าง',
  }),
});

// Invitation response schema
export const invitationResponseSchema = t.Object({
  success: t.Boolean(),
  message: t.String(),
  statusMessage: t.String(),
  timestamp: t.String(),
  data: t.Optional(t.Any()),
});

// Invitations list response schema
export const invitationsListResponseSchema = t.Object({
  success: t.Boolean(),
  message: t.String(),
  statusMessage: t.String(),
  timestamp: t.String(),
  data: t.Array(
    t.Object({
      _id: t.String(),
      siteId: t.String(),
      fromUserId: t.String(),
      toUserId: t.Optional(t.String()),
      toEmail: t.Optional(t.String()),
      role: roleTypes,
      status: invitationStatusTypes,
      message: t.Optional(t.String()),
      expiresAt: t.String(),
      createdAt: t.String(),
      updatedAt: t.String(),
    }),
  ),
});

// Bulk invitation schema
export const bulkInvitationSchema = t.Object({
  invitations: t.Array(
    t.Object({
      toUserId: t.Optional(t.String()),
      toEmail: t.Optional(t.String()),
      role: roleTypes,
      message: t.Optional(t.String()),
    }),
    {
      minItems: 1,
      maxItems: 50,
      error: 'ต้องมีคำเชิญอย่างน้อย 1 รายการ และไม่เกิน 50 รายการ',
    },
  ),
});

// Query parameters
export const invitationQuerySchema = t.Object({
  status: t.Optional(invitationStatusTypes),
  role: t.Optional(roleTypes),
  page: t.Optional(t.String()),
  limit: t.Optional(t.String()),
});
