import { userAuthPlugin } from '@/core/plugins/auth';
import { HttpError } from '@/core/utils/error';
import { Elysia, t } from 'elysia';
import { invitationRoutes } from './invitation.routes';
import { canManageRole, checkSitePermission, isOwner } from './role-permission.util';
import type { SiteRole } from './role.model';
import { paginationQuery } from './role.schema';
import { addCustomer, getUserRole, listCustomers, removeCustomer, transferOwner, updateRole } from './role.service';

export const roleRoutes = new Elysia({ prefix: '/role' })
  .use(userAuthPlugin)
  .use(invitationRoutes)
  // Role management routes
  .get(
    '/:siteId/roles',
    async ({ params, query, user }: any) => {
      if (!params.siteId) throw new HttpError(400, 'siteId จำเป็น');

      // ตรวจสอบสิทธิ์
      const hasPermission = await checkSitePermission(params.siteId, user._id, ['owner', 'admin']);
      if (!hasPermission) {
        throw new HttpError(403, 'คุณไม่มีสิทธิ์ดูรายการทีมงาน');
      }

      const { roles, pagination } = await listCustomers(params.siteId, {
        page: Number(query.page || 1),
        limit: Number(query.limit || 10),
      });

      return {
        success: true,
        message: 'ดึงรายการทีมงานเรียบร้อย',
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
        data: {
          roles,
          pagination,
        },
      };
    },
    {
      query: paginationQuery,
      params: t.Object({ siteId: t.String() }),
    },
  )
  .get(
    '/:siteId/roles/:id',
    async ({ params, user }: any) => {
      // ตรวจสอบสิทธิ์
      const hasPermission = await checkSitePermission(params.siteId, user._id, ['owner', 'admin']);
      if (!hasPermission) {
        throw new HttpError(403, 'คุณไม่มีสิทธิ์ดูข้อมูลบทบาท');
      }

      const data = await getUserRole(params.siteId, params.id);

      if (!data) {
        throw new HttpError(404, 'ไม่พบบทบาทของผู้ใช้');
      }

      return {
        success: true,
        message: 'ดึงข้อมูลบทบาทเรียบร้อย',
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
        data,
      };
    },
    {
      params: t.Object({
        siteId: t.String(),
        id: t.String(),
      }),
    },
  )
  .post(
    '/:siteId/roles',
    async ({ body, params, user }: any) => {
      const { userId, role } = body;

      // ตรวจสอบสิทธิ์
      const hasPermission = await checkSitePermission(params.siteId, user._id, ['owner', 'admin']);
      if (!hasPermission) {
        throw new HttpError(403, 'คุณไม่มีสิทธิ์เพิ่มสมาชิก');
      }

      // ตรวจสอบว่าสามารถกำหนด role นี้ได้หรือไม่
      const canManage = await canManageRole(params.siteId, user._id, role as SiteRole);
      if (!canManage) {
        throw new HttpError(403, 'คุณไม่มีสิทธิ์กำหนดบทบาทนี้');
      }

      const data = await addCustomer(params.siteId, userId, role as SiteRole);

      return {
        success: true,
        message: 'เพิ่มสมาชิกเรียบร้อย',
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
        data,
      };
    },
    {
      body: t.Object({
        userId: t.String({
          minLength: 1,
          error: 'userId ต้องเป็นข้อความและไม่ว่าง',
        }),
        role: t.Union([t.Literal('owner'), t.Literal('admin'), t.Literal('editor'), t.Literal('viewer')], {
          error: 'role ไม่ถูกต้อง',
        }),
      }),
      params: t.Object({ siteId: t.String() }),
    },
  )
  .put(
    '/:siteId/roles/:id',
    async ({ params, body, user }: any) => {
      // ตรวจสอบสิทธิ์
      const hasPermission = await checkSitePermission(params.siteId, user._id, ['owner', 'admin']);
      if (!hasPermission) {
        throw new HttpError(403, 'คุณไม่มีสิทธิ์แก้ไขบทบาท');
      }

      // ไม่สามารถแก้ไขบทบาทตัวเองได้
      if (params.id === user._id) {
        throw new HttpError(400, 'ไม่สามารถแก้ไขบทบาทตัวเองได้');
      }

      // ตรวจสอบว่าสามารถกำหนด role นี้ได้หรือไม่
      const canManage = await canManageRole(params.siteId, user._id, body.role as SiteRole);
      if (!canManage) {
        throw new HttpError(403, 'คุณไม่มีสิทธิ์กำหนดบทบาทนี้');
      }

      const data = await updateRole(params.siteId, params.id, body.role as SiteRole);

      return {
        success: true,
        message: 'อัปเดตบทบาทเรียบร้อย',
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
        data,
      };
    },
    {
      body: t.Object({
        role: t.Union([t.Literal('owner'), t.Literal('admin'), t.Literal('editor'), t.Literal('viewer')], {
          error: 'role ไม่ถูกต้อง',
        }),
      }),
      params: t.Object({
        siteId: t.String(),
        id: t.String(),
      }),
    },
  )
  .delete(
    '/:siteId/roles/:id',
    async ({ params, user }: any) => {
      // ตรวจสอบสิทธิ์
      const hasPermission = await checkSitePermission(params.siteId, user._id, ['owner', 'admin']);
      if (!hasPermission) {
        throw new HttpError(403, 'คุณไม่มีสิทธิ์ลบสมาชิก');
      }

      // ไม่สามารถลบตัวเองได้
      if (params.id === user._id) {
        throw new HttpError(400, 'ไม่สามารถลบตัวเองได้');
      }

      // ตรวจสอบ role ของคนที่จะลบ
      const targetRole = await getUserRole(params.siteId, params.id);
      if (targetRole && !(await canManageRole(params.siteId, user._id, targetRole))) {
        throw new HttpError(403, 'คุณไม่มีสิทธิ์ลบบทบาทนี้');
      }

      const data = await removeCustomer(params.siteId, params.id);

      return {
        success: true,
        message: 'ลบสมาชิกเรียบร้อย',
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
        data,
      };
    },
    {
      params: t.Object({
        siteId: t.String(),
        id: t.String(),
      }),
    },
  )
  .post(
    '/:siteId/transfer-owner',
    async ({ body, params, user }: any) => {
      const { toUserId } = body;
      const fromUserId = user._id;

      // ตรวจสอบว่าเป็น owner หรือไม่
      const isCurrentOwner = await isOwner(params.siteId, fromUserId);
      if (!isCurrentOwner) {
        throw new HttpError(403, 'เฉพาะเจ้าของเท่านั้นที่สามารถโอนความเป็นเจ้าของได้');
      }

      // ไม่สามารถโอนให้ตัวเองได้
      if (fromUserId === toUserId) {
        throw new HttpError(400, 'ไม่สามารถโอนความเป็นเจ้าของให้ตัวเองได้');
      }

      // ตรวจสอบว่าผู้รับมี role ในเว็บไซต์นี้หรือไม่
      const targetRole = await getUserRole(params.siteId, toUserId);
      if (!targetRole) {
        throw new HttpError(400, 'ผู้รับต้องเป็นสมาชิกของเว็บไซต์นี้ก่อน');
      }

      const data = await transferOwner(params.siteId, fromUserId, toUserId);

      return {
        success: true,
        message: 'โอนความเป็นเจ้าของเรียบร้อย',
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
        data,
      };
    },
    {
      body: t.Object({
        toUserId: t.String({
          minLength: 1,
          error: 'toUserId ต้องเป็นข้อความและไม่ว่าง',
        }),
      }),
      params: t.Object({ siteId: t.String() }),
    },
  )
  // ดูข้อมูล role และ permissions ของตัวเอง
  .get(
    '/:siteId/my-role',
    async ({ params, user }: any) => {
      const userRole = await getUserRole(params.siteId, user._id);

      if (!userRole) {
        throw new HttpError(404, 'คุณไม่มีบทบาทในเว็บไซต์นี้');
      }

      // ดึงข้อมูล permissions
      const { getUserRoleInfo } = await import('./role-permission.util');
      const roleInfo = await getUserRoleInfo(params.siteId, user._id);

      return {
        success: true,
        message: 'ดึงข้อมูลบทบาทของคุณเรียบร้อย',
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
        data: roleInfo,
      };
    },
    {
      params: t.Object({ siteId: t.String() }),
    },
  )
  // Invitation routes ที่ใช้ siteId
  .post(
    '/:siteId/invitations',
    async ({ params, body, user }: any) => {
      const { siteId } = params;
      const { toUserId, toEmail, role, message } = body;
      const fromUserId = user._id;

      // ตรวจสอบสิทธิ์
      const hasPermission = await checkSitePermission(siteId, fromUserId, ['owner', 'admin']);
      if (!hasPermission) {
        throw new HttpError(403, 'คุณไม่มีสิทธิ์ส่งคำเชิญ');
      }

      const { createInvitation } = await import('./invitation.service');
      const result = await createInvitation(siteId, fromUserId, toUserId, role, message, toEmail);
      return result;
    },
    {
      body: t.Object({
        toUserId: t.Optional(t.String()),
        toEmail: t.Optional(t.String()),
        role: t.Union([t.Literal('owner'), t.Literal('admin'), t.Literal('editor'), t.Literal('viewer')]),
        message: t.Optional(t.String()),
      }),
      params: t.Object({ siteId: t.String() }),
    },
  )
  .post(
    '/:siteId/invitations/bulk',
    async ({ params, body, user }: any) => {
      const { siteId } = params;
      const { invitations } = body;
      const fromUserId = user._id;

      // ตรวจสอบสิทธิ์
      const hasPermission = await checkSitePermission(siteId, fromUserId, ['owner', 'admin']);
      if (!hasPermission) {
        throw new HttpError(403, 'คุณไม่มีสิทธิ์ส่งคำเชิญ');
      }

      const { createBulkInvitations } = await import('./invitation.service');
      const result = await createBulkInvitations(siteId, fromUserId, invitations);
      return result;
    },
    {
      body: t.Object({
        invitations: t.Array(
          t.Object({
            toUserId: t.Optional(t.String()),
            toEmail: t.Optional(t.String()),
            role: t.Union([t.Literal('owner'), t.Literal('admin'), t.Literal('editor'), t.Literal('viewer')]),
            message: t.Optional(t.String()),
          }),
        ),
      }),
      params: t.Object({ siteId: t.String() }),
    },
  )
  .get(
    '/:siteId/invitations/sent',
    async ({ params, query, user }: any) => {
      const { siteId } = params;
      const fromUserId = user._id;

      // ตรวจสอบสิทธิ์
      const hasPermission = await checkSitePermission(siteId, fromUserId, ['owner', 'admin']);
      if (!hasPermission) {
        throw new HttpError(403, 'คุณไม่มีสิทธิ์ดูคำเชิญ');
      }

      const { getSentInvitations } = await import('./invitation.service');
      const result = await getSentInvitations(siteId, fromUserId, query);
      return result;
    },
    {
      params: t.Object({ siteId: t.String() }),
      query: t.Object({
        page: t.Optional(t.String()),
        limit: t.Optional(t.String()),
        status: t.Optional(t.String()),
      }),
    },
  );
