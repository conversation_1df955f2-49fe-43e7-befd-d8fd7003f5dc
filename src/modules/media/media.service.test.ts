import { afterEach, beforeEach, describe, expect, it } from 'bun:test';
import path from 'path';
import { Media } from './media.model';
import { MediaService } from './media.service';

describe('MediaService', () => {
  let mediaService: MediaService;
  const testSiteId = 'test-site-123';
  const testUserId = 'test-user-123';

  beforeEach(async () => {
    mediaService = new MediaService();
  });

  afterEach(async () => {
    // ลบข้อมูลทดสอบ
    await Media.deleteMany({});

    // ลบไฟล์ทดสอบ
    try {
      const testDir = path.join('public/uploads', testSiteId);
      await fs.rmdir(testDir, { recursive: true });
    }
    catch (error) {
      // ไม่ต้องทำอะไรถ้าลบไม่ได้
    }
  });

  describe('uploadFile', () => {
    it('ควรอัพโหลดรูปภาพได้', async () => {
      // สร้าง mock file
      const mockImageBuffer = Buffer.from('fake-image-data');
      const mockFile = new File([mockImageBuffer], 'test-image.jpg', {
        type: 'image/jpeg',
      });

      const media = await mediaService.uploadFile(mockFile, testSiteId, testUserId, {
        category: 'product',
        alt: 'รูปภาพทดสอบ',
        title: 'รูปภาพสินค้า',
        tags: ['test', 'product'],
      });

      expect(media.originalName).toBe('test-image.jpg');
      expect(media.mimeType).toBe('image/jpeg');
      expect(media.type).toBe('image');
      expect(media.category).toBe('product');
      expect(media.alt).toBe('รูปภาพทดสอบ');
      expect(media.title).toBe('รูปภาพสินค้า');
      expect(media.tags).toContain('test');
      expect(media.tags).toContain('product');
      expect(media.siteId).toBe(testSiteId);
      expect(media.uploadedBy).toBe(testUserId);
    });

    it('ควร throw error เมื่ออัพโหลดไฟล์ประเภทไม่ได้รับอนุญาต', async () => {
      const mockBuffer = Buffer.from('fake-executable-data');
      const mockFile = new File([mockBuffer], 'malware.exe', {
        type: 'application/x-executable',
      });

      await expect(mediaService.uploadFile(mockFile, testSiteId, testUserId)).rejects.toThrow(
        'ประเภทไฟล์ไม่ได้รับอนุญาต',
      );
    });

    it('ควร throw error เมื่าไฟล์ใหญ่เกินไป', async () => {
      // สร้างไฟล์ขนาดใหญ่ (51MB)
      const largeBuffer = Buffer.alloc(51 * 1024 * 1024);
      const mockFile = new File([largeBuffer], 'large-image.jpg', {
        type: 'image/jpeg',
      });

      await expect(mediaService.uploadFile(mockFile, testSiteId, testUserId)).rejects.toThrow('ขนาดไฟล์เกิน 50MB');
    });
  });

  describe('getMedia', () => {
    beforeEach(async () => {
      // สร้างข้อมูลทดสอบ
      await Media.create({
        siteId: testSiteId,
        filename: 'image1.jpg',
        originalName: 'product1.jpg',
        mimeType: 'image/jpeg',
        size: 1024,
        path: '/uploads/image1.jpg',
        url: '/uploads/image1.jpg',
        type: 'image',
        category: 'product',
        uploadedBy: testUserId,
        tags: ['product', 'electronics'],
      });

      await Media.create({
        siteId: testSiteId,
        filename: 'document1.pdf',
        originalName: 'manual.pdf',
        mimeType: 'application/pdf',
        size: 2048,
        path: '/uploads/document1.pdf',
        url: '/uploads/document1.pdf',
        type: 'document',
        category: 'document',
        uploadedBy: testUserId,
        tags: ['manual', 'guide'],
      });
    });

    it('ควรดึงรายการไฟล์ทั้งหมดได้', async () => {
      const result = await mediaService.getMedia({ siteId: testSiteId });

      expect(result.media).toHaveLength(2);
      expect(result.total).toBe(2);
      expect(result.page).toBe(1);
    });

    it('ควรกรองไฟล์ตามประเภทได้', async () => {
      const result = await mediaService.getMedia({
        siteId: testSiteId,
        type: 'image',
      });

      expect(result.media).toHaveLength(1);
      expect(result.media[0].type).toBe('image');
    });

    it('ควรกรองไฟล์ตามหมวดหมู่ได้', async () => {
      const result = await mediaService.getMedia({
        siteId: testSiteId,
        category: 'product',
      });

      expect(result.media).toHaveLength(1);
      expect(result.media[0].category).toBe('product');
    });

    it('ควรค้นหาไฟล์ตาม tags ได้', async () => {
      const result = await mediaService.getMedia({
        siteId: testSiteId,
        tags: 'electronics',
      });

      expect(result.media).toHaveLength(1);
      expect(result.media[0].tags).toContain('electronics');
    });

    it('ควรกรองไฟล์ตามขนาดได้', async () => {
      const result = await mediaService.getMedia({
        siteId: testSiteId,
        minSize: 1500,
        maxSize: 3000,
      });

      expect(result.media).toHaveLength(1);
      expect(result.media[0].size).toBe(2048);
    });

    it('ควรเรียงลำดับไฟล์ได้', async () => {
      const result = await mediaService.getMedia({
        siteId: testSiteId,
        sortBy: 'size',
        sortOrder: 'desc',
      });

      expect(result.media).toHaveLength(2);
      expect(result.media[0].size).toBeGreaterThan(result.media[1].size);
    });
  });

  describe('updateMedia', () => {
    let testMedia: any;

    beforeEach(async () => {
      testMedia = await Media.create({
        siteId: testSiteId,
        filename: 'test.jpg',
        originalName: 'test.jpg',
        mimeType: 'image/jpeg',
        size: 1024,
        path: '/uploads/test.jpg',
        url: '/uploads/test.jpg',
        type: 'image',
        category: 'other',
        uploadedBy: testUserId,
      });
    });

    it('ควรอัพเดทข้อมูลไฟล์ได้', async () => {
      const updatedMedia = await mediaService.updateMedia(testMedia._id, testSiteId, {
        alt: 'รูปภาพใหม่',
        title: 'ชื่อใหม่',
        category: 'product',
        tags: ['updated', 'test'],
      });

      expect(updatedMedia?.alt).toBe('รูปภาพใหม่');
      expect(updatedMedia?.title).toBe('ชื่อใหม่');
      expect(updatedMedia?.category).toBe('product');
      expect(updatedMedia?.tags).toContain('updated');
    });

    it('ควร throw error เมื่อไม่พบไฟล์', async () => {
      await expect(mediaService.updateMedia('invalid-id', testSiteId, { alt: 'test' })).rejects.toThrow(
        'ไม่พบไฟล์ที่ระบุ',
      );
    });
  });

  describe('deleteMedia', () => {
    let testMedia: any;

    beforeEach(async () => {
      testMedia = await Media.create({
        siteId: testSiteId,
        filename: 'delete-test.jpg',
        originalName: 'delete-test.jpg',
        mimeType: 'image/jpeg',
        size: 1024,
        path: '/uploads/delete-test.jpg',
        url: '/uploads/delete-test.jpg',
        type: 'image',
        category: 'other',
        uploadedBy: testUserId,
      });
    });

    it('ควรลบไฟล์ได้', async () => {
      const result = await mediaService.deleteMedia(testMedia._id, testSiteId);
      expect(result).toBe(true);

      // ตรวจสอบว่าไฟล์ถูกลบจากฐานข้อมูล
      const deletedMedia = await Media.findById(testMedia._id);
      expect(deletedMedia).toBeNull();
    });

    it('ควร throw error เมื่อไม่พบไฟล์', async () => {
      await expect(mediaService.deleteMedia('invalid-id', testSiteId)).rejects.toThrow('ไม่พบไฟล์ที่ระบุ');
    });
  });

  describe('bulkAction', () => {
    let testMedia1: any;
    let testMedia2: any;

    beforeEach(async () => {
      testMedia1 = await Media.create({
        siteId: testSiteId,
        filename: 'bulk1.jpg',
        originalName: 'bulk1.jpg',
        mimeType: 'image/jpeg',
        size: 1024,
        path: '/uploads/bulk1.jpg',
        url: '/uploads/bulk1.jpg',
        type: 'image',
        category: 'other',
        uploadedBy: testUserId,
        isPublic: true,
      });

      testMedia2 = await Media.create({
        siteId: testSiteId,
        filename: 'bulk2.jpg',
        originalName: 'bulk2.jpg',
        mimeType: 'image/jpeg',
        size: 2048,
        path: '/uploads/bulk2.jpg',
        url: '/uploads/bulk2.jpg',
        type: 'image',
        category: 'other',
        uploadedBy: testUserId,
        isPublic: true,
      });
    });

    it('ควรเปลี่ยนสถานะเป็น private ได้', async () => {
      const result = await mediaService.bulkAction([testMedia1._id, testMedia2._id], testSiteId, 'makePrivate');

      expect(result.success).toBe(2);
      expect(result.failed).toBe(0);

      const updatedMedia1 = await Media.findById(testMedia1._id);
      const updatedMedia2 = await Media.findById(testMedia2._id);

      expect(updatedMedia1?.isPublic).toBe(false);
      expect(updatedMedia2?.isPublic).toBe(false);
    });

    it('ควรเพิ่ม tags ได้', async () => {
      const result = await mediaService.bulkAction([testMedia1._id, testMedia2._id], testSiteId, 'addTags', {
        tags: ['bulk', 'updated'],
      });

      expect(result.success).toBe(2);
      expect(result.failed).toBe(0);

      const updatedMedia1 = await Media.findById(testMedia1._id);
      const updatedMedia2 = await Media.findById(testMedia2._id);

      expect(updatedMedia1?.tags).toContain('bulk');
      expect(updatedMedia1?.tags).toContain('updated');
      expect(updatedMedia2?.tags).toContain('bulk');
      expect(updatedMedia2?.tags).toContain('updated');
    });

    it('ควรเปลี่ยนหมวดหมู่ได้', async () => {
      const result = await mediaService.bulkAction([testMedia1._id, testMedia2._id], testSiteId, 'changeCategory', {
        category: 'product',
      });

      expect(result.success).toBe(2);
      expect(result.failed).toBe(0);

      const updatedMedia1 = await Media.findById(testMedia1._id);
      const updatedMedia2 = await Media.findById(testMedia2._id);

      expect(updatedMedia1?.category).toBe('product');
      expect(updatedMedia2?.category).toBe('product');
    });
  });

  describe('getMediaStats', () => {
    beforeEach(async () => {
      // สร้างข้อมูลทดสอบ
      await Media.create({
        siteId: testSiteId,
        filename: 'stats1.jpg',
        originalName: 'stats1.jpg',
        mimeType: 'image/jpeg',
        size: 1024,
        path: '/uploads/stats1.jpg',
        url: '/uploads/stats1.jpg',
        type: 'image',
        category: 'product',
        uploadedBy: testUserId,
        usageCount: 10,
      });

      await Media.create({
        siteId: testSiteId,
        filename: 'stats2.pdf',
        originalName: 'stats2.pdf',
        mimeType: 'application/pdf',
        size: 2048,
        path: '/uploads/stats2.pdf',
        url: '/uploads/stats2.pdf',
        type: 'document',
        category: 'document',
        uploadedBy: testUserId,
        usageCount: 5,
      });
    });

    it('ควรคำนวณสถิติได้ถูกต้อง', async () => {
      const stats = await mediaService.getMediaStats(testSiteId);

      expect(stats.totalFiles).toBe(2);
      expect(stats.totalSize).toBe(3072); // 1024 + 2048
      expect(stats.filesByType).toHaveLength(2);
      expect(stats.filesByCategory).toHaveLength(2);
      expect(stats.mostUsed).toHaveLength(2);

      const imageStats = stats.filesByType.find(s => s.type === 'image');
      expect(imageStats?.count).toBe(1);
      expect(imageStats?.size).toBe(1024);

      const documentStats = stats.filesByType.find(s => s.type === 'document');
      expect(documentStats?.count).toBe(1);
      expect(documentStats?.size).toBe(2048);
    });
  });

  describe('incrementUsage', () => {
    let testMedia: any;

    beforeEach(async () => {
      testMedia = await Media.create({
        siteId: testSiteId,
        filename: 'usage-test.jpg',
        originalName: 'usage-test.jpg',
        mimeType: 'image/jpeg',
        size: 1024,
        path: '/uploads/usage-test.jpg',
        url: '/uploads/usage-test.jpg',
        type: 'image',
        category: 'other',
        uploadedBy: testUserId,
        usageCount: 0,
      });
    });

    it('ควรเพิ่มจำนวนการใช้งานได้', async () => {
      await mediaService.incrementUsage(testMedia._id);

      const updatedMedia = await Media.findById(testMedia._id);
      expect(updatedMedia?.usageCount).toBe(1);
      expect(updatedMedia?.lastUsed).toBeDefined();
    });
  });
});
