import { checkSite } from '@/core/middleware/checkSite';
import { checkUser } from '@/core/middleware/checkUser';
import { logger } from '@/core/utils/logger';
import { Elysia } from 'elysia';
import {
  bulkActionSchema,
  generateThumbnailsSchema,
  mediaQuerySchema,
  resizeImageSchema,
  updateMediaSchema,
} from './media.schema';
import { MediaService } from './media.service';

const mediaService = new MediaService();

export const mediaRoutes = new Elysia({ prefix: '/media' })
  .use(checkUser)
  .use(checkSite)
  // อัพโหลดไฟล์
  .post('/upload', async ({ body, user, site }) => {
    try {
      if (user.role !== 'admin') {
        throw new Error('ไม่มีสิทธิ์ในการดำเนินการ');
      }

      const { file, ...options } = body as any;

      if (!file || !(file instanceof File)) {
        throw new Error('กรุณาเลือกไฟล์ที่ต้องการอัพโหลด');
      }

      const media = await mediaService.uploadFile(file, site._id, user._id, options);

      return {
        success: true,
        message: 'อัพโหลดไฟล์สำเร็จ',
        data: media,
      };
    }
    catch (error: any) {
      logger.error('เกิดข้อผิดพลาดในการอัพโหลดไฟล์');
      return {
        success: false,
        message: error.message || 'เกิดข้อผิดพลาดในการอัพโหลดไฟล์',
      };
    }
  })
  // อัพโหลดหลายไฟล์
  .post('/upload-multiple', async ({ body, user, site }) => {
    try {
      if (user.role !== 'admin') {
        throw new Error('ไม่มีสิทธิ์ในการดำเนินการ');
      }

      const { files, ...options } = body as any;

      if (!files || !Array.isArray(files) || files.length === 0) {
        throw new Error('กรุณาเลือกไฟล์ที่ต้องการอัพโหลด');
      }

      const results = [];
      const errors = [];

      for (const file of files) {
        try {
          if (file instanceof File) {
            const media = await mediaService.uploadFile(file, site._id, user._id, options);
            results.push(media);
          }
        }
        catch (error: any) {
          errors.push({
            filename: file.name,
            error: error.message,
          });
        }
      }

      return {
        success: true,
        message: `อัพโหลดไฟล์สำเร็จ ${results.length} ไฟล์`,
        data: {
          uploaded: results,
          errors: errors,
          total: files.length,
          success: results.length,
          failed: errors.length,
        },
      };
    }
    catch (error: any) {
      logger.error('เกิดข้อผิดพลาดในการอัพโหลดหลายไฟล์');
      return {
        success: false,
        message: error.message || 'เกิดข้อผิดพลาดในการอัพโหลดหลายไฟล์',
      };
    }
  })
  // ดึงรายการไฟล์
  .get(
    '/',
    async ({ query, site }) => {
      try {
        const result = await mediaService.getMedia({
          ...query,
          siteId: site._id,
        });

        return {
          success: true,
          message: 'ดึงรายการไฟล์สำเร็จ',
          data: result,
        };
      }
      catch (error: any) {
        logger.error('เกิดข้อผิดพลาดในการดึงรายการไฟล์');
        return {
          success: false,
          message: error.message || 'เกิดข้อผิดพลาดในการดึงรายการไฟล์',
        };
      }
    },
    {
      query: mediaQuerySchema,
    },
  )
  // ดึงไฟล์ตาม ID
  .get('/:id', async ({ params, site }) => {
    try {
      const media = await mediaService.getMediaById(params.id, site._id);

      if (!media) {
        return {
          success: false,
          message: 'ไม่พบไฟล์ที่ระบุ',
        };
      }

      // อัพเดทการใช้งาน
      await mediaService.incrementUsage(params.id);

      return {
        success: true,
        message: 'ดึงข้อมูลไฟล์สำเร็จ',
        data: media,
      };
    }
    catch (error: any) {
      logger.error('เกิดข้อผิดพลาดในการดึงข้อมูลไฟล์');
      return {
        success: false,
        message: error.message || 'เกิดข้อผิดพลาดในการดึงข้อมูลไฟล์',
      };
    }
  })
  // อัพเดทข้อมูลไฟล์
  .patch(
    '/:id',
    async ({ params, body, user, site }) => {
      try {
        if (user.role !== 'admin') {
          throw new Error('ไม่มีสิทธิ์ในการดำเนินการ');
        }

        const media = await mediaService.updateMedia(params.id, site._id, body);

        if (!media) {
          return {
            success: false,
            message: 'ไม่พบไฟล์ที่ระบุ',
          };
        }

        return {
          success: true,
          message: 'อัพเดทข้อมูลไฟล์สำเร็จ',
          data: media,
        };
      }
      catch (error: any) {
        logger.error('เกิดข้อผิดพลาดในการอัพเดทข้อมูลไฟล์');
        return {
          success: false,
          message: error.message || 'เกิดข้อผิดพลาดในการอัพเดทข้อมูลไฟล์',
        };
      }
    },
    {
      body: updateMediaSchema,
    },
  )
  // ลบไฟล์
  .delete('/:id', async ({ params, user, site }) => {
    try {
      if (user.role !== 'admin') {
        throw new Error('ไม่มีสิทธิ์ในการดำเนินการ');
      }

      await mediaService.deleteMedia(params.id, site._id);

      return {
        success: true,
        message: 'ลบไฟล์สำเร็จ',
      };
    }
    catch (error: any) {
      logger.error('เกิดข้อผิดพลาดในการลบไฟล์');
      return {
        success: false,
        message: error.message || 'เกิดข้อผิดพลาดในการลบไฟล์',
      };
    }
  })
  // ปรับขนาดรูปภาพ
  .post(
    '/:id/resize',
    async ({ params, body, user, site }) => {
      try {
        if (user.role !== 'admin') {
          throw new Error('ไม่มีสิทธิ์ในการดำเนินการ');
        }

        const resizedMedia = await mediaService.resizeImage(params.id, site._id, body);

        return {
          success: true,
          message: 'ปรับขนาดรูปภาพสำเร็จ',
          data: resizedMedia,
        };
      }
      catch (error: any) {
        logger.error('เกิดข้อผิดพลาดในการปรับขนาดรูปภาพ');
        return {
          success: false,
          message: error.message || 'เกิดข้อผิดพลาดในการปรับขนาดรูปภาพ',
        };
      }
    },
    {
      body: resizeImageSchema,
    },
  )
  // สร้าง thumbnails
  .post(
    '/:id/thumbnails',
    async ({ params, body, user, site }) => {
      try {
        if (user.role !== 'admin') {
          throw new Error('ไม่มีสิทธิ์ในการดำเนินการ');
        }

        const media = await mediaService.getMediaById(params.id, site._id);
        if (!media) {
          return {
            success: false,
            message: 'ไม่พบไฟล์ที่ระบุ',
          };
        }

        const updatedMedia = await mediaService.generateThumbnails(media, body.sizes, body.quality, body.format);

        return {
          success: true,
          message: 'สร้าง thumbnails สำเร็จ',
          data: updatedMedia,
        };
      }
      catch (error: any) {
        logger.error('เกิดข้อผิดพลาดในการสร้าง thumbnails');
        return {
          success: false,
          message: error.message || 'เกิดข้อผิดพลาดในการสร้าง thumbnails',
        };
      }
    },
    {
      body: generateThumbnailsSchema,
    },
  )
  // ดำเนินการกับหลายไฟล์
  .post(
    '/bulk-action',
    async ({ body, user, site }) => {
      try {
        if (user.role !== 'admin') {
          throw new Error('ไม่มีสิทธิ์ในการดำเนินการ');
        }

        const result = await mediaService.bulkAction(body.mediaIds, site._id, body.action, body.data);

        return {
          success: true,
          message: `ดำเนินการสำเร็จ ${result.success} ไฟล์, ล้มเหลว ${result.failed} ไฟล์`,
          data: result,
        };
      }
      catch (error: any) {
        logger.error('เกิดข้อผิดพลาดในการดำเนินการกับหลายไฟล์');
        return {
          success: false,
          message: error.message || 'เกิดข้อผิดพลาดในการดำเนินการกับหลายไฟล์',
        };
      }
    },
    {
      body: bulkActionSchema,
    },
  )
  // ดึงสถิติไฟล์
  .get('/stats', async ({ user, site }) => {
    try {
      if (user.role !== 'admin') {
        throw new Error('ไม่มีสิทธิ์ในการดำเนินการ');
      }

      const stats = await mediaService.getMediaStats(site._id);

      return {
        success: true,
        message: 'ดึงสถิติไฟล์สำเร็จ',
        data: stats,
      };
    }
    catch (error: any) {
      logger.error('เกิดข้อผิดพลาดในการดึงสถิติไฟล์');
      return {
        success: false,
        message: error.message || 'เกิดข้อผิดพลาดในการดึงสถิติไฟล์',
      };
    }
  })
  // ดึงไฟล์สำหรับ Gallery (Public API)
  .get('/gallery/:category?', async ({ params, query, site }) => {
    try {
      const result = await mediaService.getMedia({
        siteId: site._id,
        category: params.category,
        isPublic: true,
        type: 'image',
        ...query,
      });

      return {
        success: true,
        message: 'ดึงรายการรูปภาพสำเร็จ',
        data: result,
      };
    }
    catch (error: any) {
      logger.error('เกิดข้อผิดพลาดในการดึงรายการรูปภาพ');
      return {
        success: false,
        message: error.message || 'เกิดข้อผิดพลาดในการดึงรายการรูปภาพ',
      };
    }
  })
  // ค้นหาไฟล์
  .get('/search/:term', async ({ params, query, site }) => {
    try {
      const result = await mediaService.getMedia({
        siteId: site._id,
        search: params.term,
        ...query,
      });

      return {
        success: true,
        message: 'ค้นหาไฟล์สำเร็จ',
        data: result,
      };
    }
    catch (error: any) {
      logger.error('เกิดข้อผิดพลาดในการค้นหาไฟล์');
      return {
        success: false,
        message: error.message || 'เกิดข้อผิดพลาดในการค้นหาไฟล์',
      };
    }
  });
