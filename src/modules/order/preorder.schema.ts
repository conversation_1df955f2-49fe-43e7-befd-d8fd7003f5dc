import { t } from 'elysia';

// Address Schema
const addressSchema = t.Object({
  firstName: t.String({ minLength: 1, error: 'First name is required' }),
  lastName: t.String({ minLength: 1, error: 'Last name is required' }),
  phone: t.String({ minLength: 1, error: 'Phone number is required' }),
  email: t.String({ format: 'email', error: 'Invalid email address' }),
  address: t.String({ minLength: 1, error: 'Address is required' }),
  city: t.String({ minLength: 1, error: 'City is required' }),
  state: t.String({ minLength: 1, error: 'State is required' }),
  postalCode: t.String({ minLength: 1, error: 'Postal code is required' }),
  country: t.String({ minLength: 1, error: 'Country is required' }),
});

// PreOrder Item Schema
const preOrderItemSchema = t.Object({
  productId: t.String({ minLength: 1, error: 'Product ID is required' }),
  variantId: t.Optional(t.String({ error: 'Variant ID is required' })),
  quantity: t.Number({ minimum: 1, error: 'Quantity must be at least 1' }),
});

// Create PreOrder Schema
export const createPreOrderSchema = t.Object({
  customerEmail: t.String({ format: 'email', error: 'Invalid email address' }),
  customerName: t.String({ minLength: 1, error: 'Customer name is required' }),
  items: t.Array(preOrderItemSchema, { minItems: 1, error: 'At least one item is required' }),
  expectedReleaseDate: t.String({ format: 'date-time', error: 'Invalid expected release date' }),
  estimatedDeliveryDate: t.Optional(t.String({ format: 'date-time', error: 'Invalid estimated delivery date' })),
  shippingAddress: addressSchema,
  billingAddress: addressSchema,
  paymentMethod: t.String({ minLength: 1, error: 'Payment method is required' }),
  customerNotes: t.Optional(t.String({ error: 'Customer notes are required' })),
  adminNotes: t.Optional(t.String({ error: 'Admin notes are required' })),
  isNotifyOnRelease: t.Optional(t.Boolean({ error: 'Notify on release is required' })),
});

// Update PreOrder Status Schema
export const updatePreOrderStatusSchema = t.Object({
  status: t.Union(
    [
      t.Literal('pending', {
        error: 'Status must be one of: pending, confirmed, processing, available, cancelled, refunded',
      }),
      t.Literal('confirmed', {
        error: 'Status must be one of: pending, confirmed, processing, available, cancelled, refunded',
      }),
      t.Literal('processing', {
        error: 'Status must be one of: pending, confirmed, processing, available, cancelled, refunded',
      }),
      t.Literal('available', {
        error: 'Status must be one of: pending, confirmed, processing, available, cancelled, refunded',
      }),
      t.Literal('cancelled', {
        error: 'Status must be one of: pending, confirmed, processing, available, cancelled, refunded',
      }),
      t.Literal('refunded', {
        error: 'Status must be one of: pending, confirmed, processing, available, cancelled, refunded',
      }),
    ],
    { error: 'Invalid status' },
  ),
});

// Update Payment Status Schema
export const updatePaymentStatusSchema = t.Object({
  paymentStatus: t.Union(
    [
      t.Literal('pending', { error: 'Payment status must be one of: pending, paid, failed, refunded' }),
      t.Literal('paid', { error: 'Payment status must be one of: pending, paid, failed, refunded' }),
      t.Literal('failed', { error: 'Payment status must be one of: pending, paid, failed, refunded' }),
      t.Literal('refunded', { error: 'Payment status must be one of: pending, paid, failed, refunded' }),
    ],
    { error: 'Invalid payment status' },
  ),
  paymentId: t.Optional(t.String({ error: 'Payment ID is required' })),
});

// Update Release Date Schema
export const updateReleaseDateSchema = t.Object({
  expectedReleaseDate: t.String({ format: 'date-time', error: 'Invalid expected release date' }),
  estimatedDeliveryDate: t.Optional(t.String({ format: 'date-time', error: 'Invalid estimated delivery date' })),
});

// Cancel PreOrder Schema
export const cancelPreOrderSchema = t.Object({
  reason: t.Optional(t.String({ error: 'Reason for cancellation is required' })),
});

// PreOrder Filter Schema
export const preOrderFilterSchema = t.Object({
  status: t.Optional(
    t.Union(
      [
        t.Literal('pending', {
          error: 'Status must be one of: pending, confirmed, processing, available, cancelled, refunded',
        }),
        t.Literal('confirmed', {
          error: 'Status must be one of: pending, confirmed, processing, available, cancelled, refunded',
        }),
        t.Literal('processing', {
          error: 'Status must be one of: pending, confirmed, processing, available, cancelled, refunded',
        }),
        t.Literal('available', {
          error: 'Status must be one of: pending, confirmed, processing, available, cancelled, refunded',
        }),
        t.Literal('cancelled', {
          error: 'Status must be one of: pending, confirmed, processing, available, cancelled, refunded',
        }),
        t.Literal('refunded', {
          error: 'Status must be one of: pending, confirmed, processing, available, cancelled, refunded',
        }),
      ],
      { error: 'Invalid status' },
    ),
  ),
  paymentStatus: t.Optional(
    t.Union(
      [
        t.Literal('pending', { error: 'Payment status must be one of: pending, paid, failed, refunded' }),
        t.Literal('paid', { error: 'Payment status must be one of: pending, paid, failed, refunded' }),
        t.Literal('failed', { error: 'Payment status must be one of: pending, paid, failed, refunded' }),
        t.Literal('refunded', { error: 'Payment status must be one of: pending, paid, failed, refunded' }),
      ],
      { error: 'Invalid payment status' },
    ),
  ),
  startDate: t.Optional(t.String({ format: 'date-time', error: 'Invalid start date' })),
  endDate: t.Optional(t.String({ format: 'date-time', error: 'Invalid end date' })),
  page: t.Optional(t.Number({ minimum: 1, error: 'Page must be at least 1' })),
  limit: t.Optional(t.Number({ minimum: 1, maximum: 100, error: 'Limit must be between 1 and 100' })),
});

// PreOrder Response Schema
export const preOrderResponseSchema = t.Object({
  success: t.Boolean({ error: 'Success is required' }),
  message: t.String({ error: 'Message is required' }),
  statusMessage: t.String({ error: 'Status message is required' }),
  timestamp: t.String({ error: 'Timestamp is required' }),
  data: t.Optional(t.Any({ error: 'Data must be an object' })),
});

// PreOrder List Response Schema
export const preOrderListResponseSchema = t.Object({
  success: t.Boolean({ error: 'Success is required' }),
  message: t.String({ error: 'Message is required' }),
  statusMessage: t.String({ error: 'Status message is required' }),
  timestamp: t.String({ error: 'Timestamp is required' }),
  data: t.Array(t.Any({ error: 'Data must be an array' }), { error: 'Data must be an array' }),
  total: t.Number({ error: 'Total is required' }),
  page: t.Number({ error: 'Page is required' }),
  limit: t.Number({ error: 'Limit is required' }),
});

// PreOrder Stats Response Schema
export const preOrderStatsResponseSchema = t.Object({
  success: t.Boolean({ error: 'Success is required' }),
  message: t.String({ error: 'Message is required' }),
  statusMessage: t.String({ error: 'Status message is required' }),
  timestamp: t.String({ error: 'Timestamp is required' }),
  data: t.Object(
    {
      totalPreOrders: t.Number({ error: 'Total pre-orders is required' }),
      todayPreOrders: t.Number({ error: 'Today pre-orders is required' }),
      monthlyPreOrders: t.Number({ error: 'Monthly pre-orders is required' }),
      pendingPreOrders: t.Number({ error: 'Pending pre-orders is required' }),
      availablePreOrders: t.Number({ error: 'Available pre-orders is required' }),
      totalRevenue: t.Number({ error: 'Total revenue is required' }),
    },
    { error: 'Data must be an object' },
  ),
});
