import { beforeEach, describe, expect, it } from 'bun:test';
import * as customerService from './customer.service';

const emailMock = {
  sendEmail: async () => true,
  createVerificationEmail: () => '<html></html>',
  createResetPasswordEmail: () => '<html></html>',
};

let customerDb: any[] = [];
const CustomerMock = {
  findOne: async (query: any) => customerDb.find(u => u.email === query.email) || null,
  create: async (data: any) => {
    customerDb.push(data);
    return { ...data, save: async () => {}, resetPasswordToken: undefined };
  },
  findById: async (id: string) => customerDb.find(u => u._id === id) || null,
};

const deps = {
  Customer: CustomerMock,
  ...emailMock,
};

describe('customer.service', () => {
  beforeEach(() => {
    customerDb = [
      {
        _id: 'id1',
        email: '<EMAIL>',
        isEmailVerified: true,
        comparePassword: async (pw: string) => pw === '123456',
        save: async () => {},
        resetPasswordToken: undefined,
      },
    ];
  });

  it('signupCustomer: สมัครสำเร็จ', async () => {
    customerDb = [];
    const customer = await customerService.signupCustomer({ email: '<EMAIL>', password: '123456' }, deps);
    expect(customer.email).toBe('<EMAIL>');
  });

  it('signupCustomer: อีเมลซ้ำ', async () => {
    await expect(customerService.signupCustomer({ email: '<EMAIL>', password: '123456' }, deps)).rejects.toThrow(
      'อีเมลนี้ถูกใช้งานแล้ว',
    );
  });

  it('signinCustomer: เข้าสู่ระบบเรียบร้อย', async () => {
    const { customer, token } = await customerService.signinCustomer({ email: '<EMAIL>', password: '123456' }, deps);
    expect(customer.email).toBe('<EMAIL>');
    expect(token).toBeDefined();
  });

  it('signinCustomer: ไม่พบสมาชิก', async () => {
    await expect(customerService.signinCustomer({ email: '<EMAIL>', password: '123456' }, deps)).rejects.toThrow(
      'ไม่พบสมาชิก',
    );
  });

  it('signinCustomer: รหัสผ่านผิด', async () => {
    customerDb[0].comparePassword = async () => false;
    await expect(customerService.signinCustomer({ email: '<EMAIL>', password: 'wrong' }, deps)).rejects.toThrow(
      'รหัสผ่านไม่ถูกต้อง',
    );
  });

  it('signinCustomer: ยังไม่ยืนยันอีเมล', async () => {
    customerDb[0].isEmailVerified = false;
    customerDb[0].comparePassword = async () => true;
    await expect(customerService.signinCustomer({ email: '<EMAIL>', password: '123456' }, deps)).rejects.toThrow(
      'กรุณายืนยันอีเมลก่อนเข้าสู่ระบบ',
    );
  });

  it('signoutCustomer: always true', async () => {
    expect(await customerService.signoutCustomer(undefined, deps)).toBe(true);
  });

  it('forgotPasswordCustomer: ไม่พบสมาชิก', async () => {
    await expect(customerService.forgotPasswordCustomer('<EMAIL>', deps)).rejects.toThrow('ไม่พบสมาชิก');
  });

  it('forgotPasswordCustomer: ส่งอีเมลสำเร็จ', async () => {
    // customerDb[0] มี property save/resetPasswordToken อยู่แล้วจาก beforeEach
    await expect(customerService.forgotPasswordCustomer('<EMAIL>', deps)).resolves.toBe(true);
  });

  it('resetPasswordCustomer: โทเค็นผิด', async () => {
    await expect(customerService.resetPasswordCustomer('badtoken', 'newpass', deps)).rejects.toThrow();
  });
});
