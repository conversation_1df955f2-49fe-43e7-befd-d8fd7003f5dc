import { sitePlugin } from '@/core/middleware/checkSite';
import { isSiteOwner } from '@/core/middleware/checkUser';
import { customerAuthPlugin } from '@/core/plugins/auth';
import { userAuthPlugin } from '@/core/plugins/auth';
import { Elysia, t } from 'elysia';
import { forgotPasswordSchema, messageResponseSchema, resetPasswordSchema } from '../user/user.schema';
import { Customer } from './customer.model';
import {
  authResponseCustomerSchema,
  customerProfileResponseSchema,
  signinSchema,
  signupSchema,
} from './customer.schema';
import {
  forgotPasswordCustomer,
  generateCustomerUploadSignature,
  getCustomerProfile,
  refreshTokenCustomer,
  resetPasswordCustomer,
  signinCustomer,
  signoutCustomer,
  signupCustomer,
  uploadCustomerCoverImage,
  uploadCustomerProfileImage,
  verifyEmailCustomer,
} from './customer.service';

export const customerRoutes = new Elysia({ prefix: '/customer' }).group('/website/:fullDomain', app =>
  app
    .use(sitePlugin)
    .post(
      '/signup',
      async ({ body, site }: any) => {
        const { email, password } = body;
        const { customer, token, refreshToken } = await signupCustomer({
          email,
          password,
          siteId: site._id,
        });
        return {
          success: true,
          message: 'สมัครสมาชิกสำเร็จ',
          statusMessage: 'สำเร็จ!',
          timestamp: new Date().toISOString(),
          data: { customer, token, refreshToken },
        };
      },
      {
        body: signupSchema,
        response: authResponseCustomerSchema,
      },
    )
    .post(
      '/signin',
      async ({ body, site, cookie }: any) => {
        const { email, password } = body;
        const { customer, token, refreshToken } = await signinCustomer({
          email,
          password,
          siteId: site._id,
        });

        cookie.session.set({
          value: token,
          httpOnly: true,
          path: '/',
          sameSite: 'lax',
          maxAge: 60 * 60 * 24 * 7,
        });

        return {
          success: true,
          message: 'เข้าสู่ระบบเรียบร้อย',
          statusMessage: 'สำเร็จ!',
          timestamp: new Date().toISOString(),
          data: { customer, token, refreshToken },
        };
      },
      { body: signinSchema, response: authResponseCustomerSchema },
    )
    .get('/verify-email', async ({ query }: any) => {
      const { token } = query as { token: string; };
      return await verifyEmailCustomer(token);
    })
    .post(
      '/forgot-password',
      async ({ body }: any) => {
        const { email } = body as { email: string; };
        return await forgotPasswordCustomer(email);
      },
      {
        body: forgotPasswordSchema,
        response: messageResponseSchema,
      },
    )
    .post(
      '/reset-password',
      async ({ body }: any) => {
        const { token, newPassword } = body as {
          token: string;
          newPassword: string;
        };
        return await resetPasswordCustomer(token, newPassword);
      },
      {
        body: resetPasswordSchema,
        response: messageResponseSchema,
      },
    )
    // Routes ที่ต้องการ authentication
    .use(customerAuthPlugin)
    .post('/signout', async ({ customer, cookie }: any) => {
      await signoutCustomer(customer?._id);

      cookie.session.remove();

      return {
        success: true,
        message: 'ออกจากระบบสำเร็จ',
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
      };
    })
    .post('/refresh', async ({ body }) => {
      const { refreshToken: refreshTokenValue } = body as {
        refreshToken: string;
      };
      const result = await refreshTokenCustomer(refreshTokenValue);
      return result;
    })
    .get(
      '/profile',
      async ({ customer }: any) => {
        const { customer: safeCustomer } = await getCustomerProfile(customer?._id);

        return {
          success: true,
          message: 'ดึงข้อมูลโปรไฟล์สำเร็จ',
          statusMessage: 'สำเร็จ!',
          timestamp: new Date().toISOString(),
          data: safeCustomer,
        };
      },
      {
        response: customerProfileResponseSchema,
      },
    )
    // Upload profile image
    .post(
      '/upload/profile',
      async ({ customer, body }: any) => {
        const file = body.file;
        if (!file) throw new Error('ไม่พบไฟล์รูปภาพ');

        // แปลง file เป็น buffer
        const buffer = await file.arrayBuffer();
        return await uploadCustomerProfileImage(customer._id, Buffer.from(buffer));
      },
      {
        body: t.Object({
          file: t.File(),
        }),
      },
    )
    // Upload cover image
    .post(
      '/upload/cover',
      async ({ customer, body }: any) => {
        const file = body.file;
        if (!file) throw new Error('ไม่พบไฟล์รูปภาพ');

        // แปลง file เป็น buffer
        const buffer = await file.arrayBuffer();
        return await uploadCustomerCoverImage(customer._id, Buffer.from(buffer));
      },
      {
        body: t.Object({
          file: t.File(),
        }),
      },
    )
    // Generate signed URL for client-side upload
    .get('/upload/signature/:type', async ({ customer, params }: any) => {
      const { type } = params;
      if (type !== 'profile' && type !== 'cover') {
        throw new Error('ประเภทการอัพโหลดไม่ถูกต้อง');
      }

      return await generateCustomerUploadSignature(customer._id, type);
    })
    // Upload profile image via base64
    .post(
      '/upload/profile/base64',
      async ({ customer, body }: any) => {
        const { base64Image } = body;
        if (!base64Image) throw new Error('ไม่พบข้อมูล base64');

        return await uploadCustomerProfileImage(customer._id, base64Image);
      },
      {
        body: t.Object({
          base64Image: t.String(),
        }),
      },
    )
    // Upload cover image via base64
    .post(
      '/upload/cover/base64',
      async ({ customer, body }: any) => {
        const { base64Image } = body;
        if (!base64Image) throw new Error('ไม่พบข้อมูล base64');

        return await uploadCustomerCoverImage(customer._id, base64Image);
      },
      {
        body: t.Object({
          base64Image: t.String(),
        }),
      },
    ));

// Admin routes for managing customers
export const customerAdminRoutes = new Elysia({ prefix: '/customer' }).group(
  '/dashboard/:siteId',
  app =>
    app.use(userAuthPlugin).get(
      '/stats',
      async ({ params, query, user }: any) => {
        try {
          console.log('🔍 user', user);
          const { siteId } = params;
          const userId = user?._id;

          if (!userId) {
            throw new Error('ไม่พบข้อมูลผู้ใช้');
          }

          // ตรวจสอบว่าเป็น site owner หรือไม่
          const isOwner = await isSiteOwner(siteId, userId);
          if (!isOwner) {
            throw new Error('ไม่มีสิทธิ์ในการเข้าถึงข้อมูลนี้');
          }

          const dateRange = {
            start: query.startDate ? new Date(query.startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
            end: query.endDate ? new Date(query.endDate) : new Date(),
          };

          // นับจำนวนลูกค้าทั้งหมด
          const total = await Customer.countDocuments({ siteId });

          // นับจำนวนลูกค้าใหม่ในช่วงเวลาที่กำหนด
          const newCustomers = await Customer.countDocuments({
            siteId,
            createdAt: { $gte: dateRange.start, $lte: dateRange.end },
          });

          // นับจำนวนลูกค้าที่ใช้งานในเดือนที่ผ่านมา
          const activeCustomers = await Customer.countDocuments({
            siteId,
            lastLoginAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) },
          });

          const stats = {
            total,
            new: newCustomers,
            active: activeCustomers,
            newChange: '+0%', // TODO: คำนวณเปอร์เซ็นต์การเปลี่ยนแปลง
            totalChange: '+0%',
          };

          return {
            success: true,
            message: 'ดึงสถิติลูกค้าสำเร็จ',
            statusMessage: 'สำเร็จ!',
            timestamp: new Date().toISOString(),
            data: stats,
          };
        }
        catch (error: any) {
          console.error('เกิดข้อผิดพลาดในการดึงสถิติลูกค้า:', error);
          return {
            success: false,
            message: error.message || 'เกิดข้อผิดพลาดในการดึงสถิติลูกค้า',
            statusMessage: 'ล้มเหลว!',
            timestamp: new Date().toISOString(),
          };
        }
      },
      {
        query: t.Object({
          startDate: t.Optional(t.String()),
          endDate: t.Optional(t.String()),
        }),
      },
    ),
);
