import { HttpError } from '@/core/utils/error';
import { Inventory } from './inventory.model';
import { Order } from './order.model';
import { ProductAnalytics } from './product-analytics.model';
import { Product } from './product.model';

// Product Analytics Service
export async function generateProductAnalytics(siteId: string, startDate: Date, endDate: Date) {
  try {
    // ตรวจสอบว่ามี analytics นี้แล้วหรือไม่
    const existingAnalytics = await (ProductAnalytics as any).getAnalyticsByPeriod(siteId, startDate, endDate);
    if (existingAnalytics) {
      return existingAnalytics;
    }

    // ดึงข้อมูลสินค้าทั้งหมด
    const allProducts = await Product.find({ siteId });
    const totalProducts = allProducts.length;
    const activeProducts = allProducts.filter(p => p.status === 'active').length;
    const inactiveProducts = totalProducts - activeProducts;

    // ดึงข้อมูลออเดอร์ในช่วงเวลาที่กำหนด
    const orders = await Order.find({
      siteId,
      createdAt: { $gte: startDate, $lte: endDate },
      status: { $in: ['completed', 'delivered', 'shipped'] },
    }).populate('items.productId', 'name category price rating reviews');

    // คำนวณ metrics พื้นฐาน
    const totalRevenue = orders.reduce((sum, order) => sum + order.totalAmount, 0);
    const totalOrders = orders.length;
    const averagePrice = allProducts.length > 0 ? allProducts.reduce((sum, p) => sum + p.price, 0) / allProducts.length
      : 0;

    // คำนวณ average rating และ total reviews
    const totalRating = allProducts.reduce((sum, p) => sum + (p.rating || 0), 0);
    const averageRating = allProducts.length > 0 ? totalRating / allProducts.length : 0;
    const totalReviews = allProducts.reduce((sum, p) => sum + (p.reviews?.length || 0), 0);

    // สร้าง top products
    const topProducts = await calculateTopProducts(siteId, startDate, endDate);

    // สร้าง category performance
    const categoryPerformance = await calculateCategoryPerformance(siteId, startDate, endDate);

    // สร้าง price analysis
    const priceAnalysis = await calculatePriceAnalysis(siteId, startDate, endDate);

    // สร้าง inventory analysis
    const inventoryAnalysis = await calculateInventoryAnalysis(siteId);

    // สร้าง performance metrics
    const performanceMetrics = await calculatePerformanceMetrics(siteId, startDate, endDate);

    // สร้าง analytics record
    const analytics = await ProductAnalytics.create({
      siteId,
      startDate,
      endDate,
      period: 'month',
      metrics: {
        totalProducts,
        activeProducts,
        inactiveProducts,
        totalRevenue,
        averagePrice,
        totalOrders,
        averageRating,
        totalReviews,
      },
      topProducts,
      categoryPerformance,
      priceAnalysis,
      inventoryAnalysis,
      performanceMetrics,
    });

    return analytics;
  }
  catch (err: any) {
    console.error('Error in generateProductAnalytics:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะสร้าง product analytics');
  }
}

async function calculateTopProducts(siteId: string, startDate: Date, endDate: Date) {
  const orders = await Order.find({
    siteId,
    createdAt: { $gte: startDate, $lte: endDate },
    status: { $in: ['completed', 'delivered', 'shipped'] },
  }).populate('items.productId', 'name category price rating reviews');

  const productData = new Map();

  // จัดกลุ่มข้อมูลสินค้า
  for (const order of orders) {
    for (const item of order.items) {
      const productId = item.productId.toString();
      if (!productData.has(productId)) {
        productData.set(productId, {
          productId,
          productName: (item.productId as any).name,
          category: (item.productId as any).category,
          totalSales: 0,
          totalRevenue: 0,
          totalOrders: 0,
          averageRating: (item.productId as any).rating || 0,
          totalReviews: (item.productId as any).reviews?.length || 0,
        });
      }
      const data = productData.get(productId);
      data.totalSales += item.quantity;
      data.totalRevenue += item.price * item.quantity;
      data.totalOrders += 1;
    }
  }

  const topProducts = [];
  for (const [productId, data] of productData) {
    const conversionRate = data.totalOrders > 0 ? (data.totalOrders / data.totalOrders) * 100 : 0;

    topProducts.push({
      productId,
      productName: data.productName,
      category: data.category,
      totalSales: data.totalSales,
      totalRevenue: data.totalRevenue,
      totalOrders: data.totalOrders,
      averageRating: data.averageRating,
      totalReviews: data.totalReviews,
      conversionRate,
    });
  }

  return topProducts.sort((a, b) => b.totalRevenue - a.totalRevenue).slice(0, 10);
}

async function calculateCategoryPerformance(siteId: string, startDate: Date, endDate: Date) {
  const products = await Product.find({ siteId });
  const orders = await Order.find({
    siteId,
    createdAt: { $gte: startDate, $lte: endDate },
    status: { $in: ['completed', 'delivered', 'shipped'] },
  }).populate('items.productId', 'category');

  const categoryData = new Map();

  // จัดกลุ่มข้อมูลตาม category
  for (const product of products) {
    const category = product.category;
    if (!categoryData.has(category)) {
      categoryData.set(category, {
        category,
        totalProducts: 0,
        totalSales: 0,
        totalRevenue: 0,
        averageRating: 0,
        totalRating: 0,
        totalReviews: 0,
      });
    }
    const data = categoryData.get(category);
    data.totalProducts += 1;
    data.totalRating += product.rating || 0;
    data.totalReviews += product.reviews?.length || 0;
  }

  // คำนวณข้อมูลจากออเดอร์
  for (const order of orders) {
    for (const item of order.items) {
      const category = (item.productId as any).category;
      if (categoryData.has(category)) {
        const data = categoryData.get(category);
        data.totalSales += item.quantity;
        data.totalRevenue += item.price * item.quantity;
      }
    }
  }

  const result = [];
  for (const [category, data] of categoryData) {
    const averageRating = data.totalProducts > 0 ? data.totalRating / data.totalProducts : 0;
    const conversionRate = data.totalProducts > 0 ? (data.totalSales / data.totalProducts) * 100 : 0;

    result.push({
      category,
      totalProducts: data.totalProducts,
      totalSales: data.totalSales,
      totalRevenue: data.totalRevenue,
      averageRating,
      conversionRate,
    });
  }

  return result;
}

async function calculatePriceAnalysis(siteId: string, startDate: Date, endDate: Date) {
  const products = await Product.find({ siteId });
  const orders = await Order.find({
    siteId,
    createdAt: { $gte: startDate, $lte: endDate },
    status: { $in: ['completed', 'delivered', 'shipped'] },
  }).populate('items.productId', 'price rating');

  // วิเคราะห์ price ranges
  const priceRanges = [
    { range: '0-100', min: 0, max: 100, count: 0, totalRevenue: 0, totalRating: 0 },
    { range: '101-500', min: 101, max: 500, count: 0, totalRevenue: 0, totalRating: 0 },
    { range: '501-1000', min: 501, max: 1000, count: 0, totalRevenue: 0, totalRating: 0 },
    { range: '1001-2000', min: 1001, max: 2000, count: 0, totalRevenue: 0, totalRating: 0 },
    { range: '2000+', min: 2001, max: Infinity, count: 0, totalRevenue: 0, totalRating: 0 },
  ];

  for (const product of products) {
    for (const range of priceRanges) {
      if (product.price >= range.min && product.price <= range.max) {
        range.count++;
        range.totalRating += product.rating || 0;
        break;
      }
    }
  }

  // คำนวณ revenue จากออเดอร์
  for (const order of orders) {
    for (const item of order.items) {
      const price = (item.productId as any).price;
      for (const range of priceRanges) {
        if (price >= range.min && price <= range.max) {
          range.totalRevenue += item.price * item.quantity;
          break;
        }
      }
    }
  }

  // คำนวณ average price by category
  const categoryPriceData = new Map();
  for (const product of products) {
    const category = product.category;
    if (!categoryPriceData.has(category)) {
      categoryPriceData.set(category, {
        category,
        totalPrice: 0,
        count: 0,
        minPrice: Infinity,
        maxPrice: 0,
      });
    }
    const data = categoryPriceData.get(category);
    data.totalPrice += product.price;
    data.count += 1;
    data.minPrice = Math.min(data.minPrice, product.price);
    data.maxPrice = Math.max(data.maxPrice, product.price);
  }

  const averagePriceByCategory = [];
  for (const [category, data] of categoryPriceData) {
    averagePriceByCategory.push({
      category,
      averagePrice: data.count > 0 ? data.totalPrice / data.count : 0,
      minPrice: data.minPrice === Infinity ? 0 : data.minPrice,
      maxPrice: data.maxPrice,
    });
  }

  return {
    priceRanges: priceRanges.map(range => ({
      range: range.range,
      count: range.count,
      totalRevenue: range.totalRevenue,
      averageRating: range.count > 0 ? range.totalRating / range.count : 0,
    })),
    averagePriceByCategory,
  };
}

async function calculateInventoryAnalysis(siteId: string) {
  const products = await Product.find({ siteId });
  const inventories = await Inventory.find({ siteId });

  const inventoryMap = new Map();
  for (const inventory of inventories) {
    inventoryMap.set(inventory.productId.toString(), inventory);
  }

  let lowStockProducts = 0;
  let outOfStockProducts = 0;
  let overstockedProducts = 0;
  let inventoryValue = 0;

  for (const product of products) {
    const inventory = inventoryMap.get(product._id.toString());
    if (inventory) {
      if (inventory.stockQuantity <= 0) {
        outOfStockProducts++;
      }
      else if (inventory.stockQuantity <= inventory.lowStockThreshold) {
        lowStockProducts++;
      }
      else if (inventory.stockQuantity > inventory.maxStockThreshold) {
        overstockedProducts++;
      }
      inventoryValue += product.price * inventory.stockQuantity;
    }
  }

  // คำนวณ turnover rate (จำลอง)
  const turnoverRate = Math.random() * 100;

  return {
    lowStockProducts,
    outOfStockProducts,
    overstockedProducts,
    inventoryValue,
    turnoverRate,
  };
}

async function calculatePerformanceMetrics(_siteId: string, _startDate: Date, _endDate: Date) {
  // จำลองข้อมูล performance metrics
  return {
    pageViews: Math.floor(Math.random() * 10000) + 5000,
    uniqueVisitors: Math.floor(Math.random() * 2000) + 1000,
    addToCartRate: Math.random() * 20 + 10,
    purchaseRate: Math.random() * 10 + 5,
    bounceRate: Math.random() * 30 + 20,
    averageSessionDuration: Math.random() * 300 + 120,
  };
}

export async function getProductAnalytics(siteId: string, startDate: Date, endDate: Date) {
  try {
    let analytics = await (ProductAnalytics as any).getAnalyticsByPeriod(siteId, startDate, endDate);

    if (!analytics) {
      analytics = await generateProductAnalytics(siteId, startDate, endDate);
    }

    return analytics;
  }
  catch (err: any) {
    console.error('Error in getProductAnalytics:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึง product analytics');
  }
}

export async function getLatestProductAnalytics(siteId: string) {
  try {
    const analytics = await (ProductAnalytics as any).getLatestAnalytics(siteId);
    if (!analytics) {
      throw new HttpError(404, 'ไม่พบ product analytics');
    }
    return analytics;
  }
  catch (err: any) {
    console.error('Error in getLatestProductAnalytics:', err);
    if (err instanceof HttpError) throw err;
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึง latest product analytics');
  }
}

// Export functions สำหรับ routes
export async function getTopProducts(siteId: string, startDate: Date, endDate: Date) {
  try {
    return await calculateTopProducts(siteId, startDate, endDate);
  }
  catch (err: any) {
    console.error('Error in getTopProducts:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึง top products');
  }
}

export async function getCategoryPerformance(siteId: string, startDate: Date, endDate: Date) {
  try {
    return await calculateCategoryPerformance(siteId, startDate, endDate);
  }
  catch (err: any) {
    console.error('Error in getCategoryPerformance:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึง category performance');
  }
}

export async function getPriceAnalysis(siteId: string, startDate: Date, endDate: Date) {
  try {
    return await calculatePriceAnalysis(siteId, startDate, endDate);
  }
  catch (err: any) {
    console.error('Error in getPriceAnalysis:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึง price analysis');
  }
}

export async function getInventoryAnalysis(siteId: string) {
  try {
    return await calculateInventoryAnalysis(siteId);
  }
  catch (err: any) {
    console.error('Error in getInventoryAnalysis:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึง inventory analysis');
  }
}

export async function getPerformanceMetrics(siteId: string, startDate: Date, endDate: Date) {
  try {
    return await calculatePerformanceMetrics(siteId, startDate, endDate);
  }
  catch (err: any) {
    console.error('Error in getPerformanceMetrics:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึง performance metrics');
  }
}
