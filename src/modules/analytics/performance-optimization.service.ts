import { HttpError } from '@/core/utils/error';
import { PerformanceOptimization } from './performance-optimization.model';

// Performance Optimization Service
export async function createOptimization(siteId: string, type: string, settings: any) {
  try {
    const optimization = await PerformanceOptimization.create({
      siteId,
      type,
      ...settings,
    });

    return optimization;
  }
  catch (err: any) {
    console.error('Error in createOptimization:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะสร้าง optimization');
  }
}

export async function getOptimization(siteId: string, type: string) {
  try {
    const optimization = await (PerformanceOptimization as any).getOptimizationByType(siteId, type);
    if (!optimization) {
      throw new HttpError(404, 'ไม่พบ optimization');
    }
    return optimization;
  }
  catch (err: any) {
    console.error('Error in getOptimization:', err);
    if (err instanceof HttpError) throw err;
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึง optimization');
  }
}

export async function getAllOptimizations(siteId: string) {
  try {
    const optimizations = await (PerformanceOptimization as any).getActiveOptimizations(siteId);
    return optimizations;
  }
  catch (err: any) {
    console.error('Error in getAllOptimizations:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึง optimizations');
  }
}

export async function updateOptimization(siteId: string, type: string, updates: any) {
  try {
    const optimization = await PerformanceOptimization.findOneAndUpdate(
      { siteId, type },
      { $set: updates },
      { new: true, runValidators: true },
    );

    if (!optimization) {
      throw new HttpError(404, 'ไม่พบ optimization');
    }

    return optimization;
  }
  catch (err: any) {
    console.error('Error in updateOptimization:', err);
    if (err instanceof HttpError) throw err;
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะอัปเดต optimization');
  }
}

export async function deleteOptimization(siteId: string, type: string) {
  try {
    const optimization = await PerformanceOptimization.findOneAndDelete({ siteId, type });

    if (!optimization) {
      throw new HttpError(404, 'ไม่พบ optimization');
    }

    return { message: 'ลบ optimization สำเร็จ' };
  }
  catch (err: any) {
    console.error('Error in deleteOptimization:', err);
    if (err instanceof HttpError) throw err;
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะลบ optimization');
  }
}

export async function getOptimizationMetrics(siteId: string) {
  try {
    const metrics = await (PerformanceOptimization as any).getOptimizationMetrics(siteId);
    return (
      metrics[0] || {
        averageResponseTime: 0,
        averageThroughput: 0,
        averageErrorRate: 0,
        averageCpuUsage: 0,
        averageMemoryUsage: 0,
        averageDiskUsage: 0,
      }
    );
  }
  catch (err: any) {
    console.error('Error in getOptimizationMetrics:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึง optimization metrics');
  }
}

export async function updateMetrics(siteId: string, type: string, metrics: any) {
  try {
    const optimization = await PerformanceOptimization.findOneAndUpdate(
      { siteId, type },
      {
        $set: {
          metrics,
          'optimization.lastOptimized': new Date(),
        },
      },
      { new: true },
    );

    if (!optimization) {
      throw new HttpError(404, 'ไม่พบ optimization');
    }

    return optimization;
  }
  catch (err: any) {
    console.error('Error in updateMetrics:', err);
    if (err instanceof HttpError) throw err;
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะอัปเดต metrics');
  }
}

export async function addAlert(siteId: string, type: string, alert: any) {
  try {
    const optimization = await PerformanceOptimization.findOneAndUpdate(
      { siteId, type },
      { $push: { alerts: { ...alert, createdAt: new Date() } } },
      { new: true },
    );

    if (!optimization) {
      throw new HttpError(404, 'ไม่พบ optimization');
    }

    return optimization;
  }
  catch (err: any) {
    console.error('Error in addAlert:', err);
    if (err instanceof HttpError) throw err;
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะเพิ่ม alert');
  }
}

export async function clearAlerts(siteId: string, type: string) {
  try {
    const optimization = await PerformanceOptimization.findOneAndUpdate(
      { siteId, type },
      { $set: { alerts: [] } },
      { new: true },
    );

    if (!optimization) {
      throw new HttpError(404, 'ไม่พบ optimization');
    }

    return optimization;
  }
  catch (err: any) {
    console.error('Error in clearAlerts:', err);
    if (err instanceof HttpError) throw err;
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะล้าง alerts');
  }
}

export async function optimizeCache(siteId: string) {
  try {
    const optimization = await PerformanceOptimization.findOne({ siteId, type: 'cache' });

    if (!optimization) {
      throw new HttpError(404, 'ไม่พบ cache optimization');
    }

    // จำลองการ optimize cache
    const updatedOptimization = await PerformanceOptimization.findOneAndUpdate(
      { siteId, type: 'cache' },
      {
        $set: {
          'cache.hitRate': Math.min(optimization.cache.hitRate + 5, 95),
          'cache.missRate': Math.max(optimization.cache.missRate - 5, 5),
          'optimization.lastOptimized': new Date(),
          'optimization.nextOptimization': new Date(Date.now() + 24 * 60 * 60 * 1000),
        },
      },
      { new: true },
    );

    return updatedOptimization;
  }
  catch (err: any) {
    console.error('Error in optimizeCache:', err);
    if (err instanceof HttpError) throw err;
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะ optimize cache');
  }
}

export async function optimizeDatabase(siteId: string) {
  try {
    const optimization = await PerformanceOptimization.findOne({ siteId, type: 'database' });

    if (!optimization) {
      throw new HttpError(404, 'ไม่พบ database optimization');
    }

    // จำลองการ optimize database
    const updatedOptimization = await PerformanceOptimization.findOneAndUpdate(
      { siteId, type: 'database' },
      {
        $set: {
          'database.slowQueries': Math.max(optimization.database.slowQueries - 2, 0),
          'database.averageQueryTime': Math.max(optimization.database.averageQueryTime - 10, 50),
          'optimization.lastOptimized': new Date(),
          'optimization.nextOptimization': new Date(Date.now() + 24 * 60 * 60 * 1000),
        },
      },
      { new: true },
    );

    return updatedOptimization;
  }
  catch (err: any) {
    console.error('Error in optimizeDatabase:', err);
    if (err instanceof HttpError) throw err;
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะ optimize database');
  }
}

export async function optimizeAPI(siteId: string) {
  try {
    const optimization = await PerformanceOptimization.findOne({ siteId, type: 'api' });

    if (!optimization) {
      throw new HttpError(404, 'ไม่พบ API optimization');
    }

    // จำลองการ optimize API
    const updatedOptimization = await PerformanceOptimization.findOneAndUpdate(
      { siteId, type: 'api' },
      {
        $set: {
          'api.averageResponseTime': Math.max(optimization.api.averageResponseTime - 20, 100),
          'api.requestsPerSecond': optimization.api.requestsPerSecond + 10,
          'optimization.lastOptimized': new Date(),
          'optimization.nextOptimization': new Date(Date.now() + 24 * 60 * 60 * 1000),
        },
      },
      { new: true },
    );

    return updatedOptimization;
  }
  catch (err: any) {
    console.error('Error in optimizeAPI:', err);
    if (err instanceof HttpError) throw err;
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะ optimize API');
  }
}

export async function optimizeFrontend(siteId: string) {
  try {
    const optimization = await PerformanceOptimization.findOne({ siteId, type: 'frontend' });

    if (!optimization) {
      throw new HttpError(404, 'ไม่พบ frontend optimization');
    }

    // จำลองการ optimize frontend
    const updatedOptimization = await PerformanceOptimization.findOneAndUpdate(
      { siteId, type: 'frontend' },
      {
        $set: {
          'frontend.bundleSize': Math.max(optimization.frontend.bundleSize - 50, 100),
          'frontend.loadTime': Math.max(optimization.frontend.loadTime - 100, 500),
          'optimization.lastOptimized': new Date(),
          'optimization.nextOptimization': new Date(Date.now() + 24 * 60 * 60 * 1000),
        },
      },
      { new: true },
    );

    return updatedOptimization;
  }
  catch (err: any) {
    console.error('Error in optimizeFrontend:', err);
    if (err instanceof HttpError) throw err;
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะ optimize frontend');
  }
}

export async function optimizeImages(siteId: string) {
  try {
    const optimization = await PerformanceOptimization.findOne({ siteId, type: 'image' });

    if (!optimization) {
      throw new HttpError(404, 'ไม่พบ image optimization');
    }

    // จำลองการ optimize images
    const updatedOptimization = await PerformanceOptimization.findOneAndUpdate(
      { siteId, type: 'image' },
      {
        $set: {
          'image.averageSize': Math.max(optimization.image.averageSize - 20, 50),
          'image.totalSize': Math.max(optimization.image.totalSize - 1000, 5000),
          'optimization.lastOptimized': new Date(),
          'optimization.nextOptimization': new Date(Date.now() + 24 * 60 * 60 * 1000),
        },
      },
      { new: true },
    );

    return updatedOptimization;
  }
  catch (err: any) {
    console.error('Error in optimizeImages:', err);
    if (err instanceof HttpError) throw err;
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะ optimize images');
  }
}
