import mongoose, { type Document, Schema } from 'mongoose';
import { customAlphabet } from 'nanoid';

const generateId = () => {
  const timestamp = Date.now().toString(36);
  const nanoid = customAlphabet('0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz', 12)();
  return `${timestamp}${nanoid}`;
};

export interface IProductAnalytics extends Document {
  _id: string;
  siteId: string;
  startDate: Date;
  endDate: Date;
  period: 'day' | 'week' | 'month' | 'year';
  metrics: {
    totalProducts: number;
    activeProducts: number;
    inactiveProducts: number;
    totalRevenue: number;
    averagePrice: number;
    totalOrders: number;
    averageRating: number;
    totalReviews: number;
  };
  topProducts: Array<{
    productId: string;
    productName: string;
    category: string;
    totalSales: number;
    totalRevenue: number;
    totalOrders: number;
    averageRating: number;
    totalReviews: number;
    conversionRate: number;
  }>;
  categoryPerformance: Array<{
    category: string;
    totalProducts: number;
    totalSales: number;
    totalRevenue: number;
    averageRating: number;
    conversionRate: number;
  }>;
  priceAnalysis: {
    priceRanges: Array<{
      range: string;
      count: number;
      totalRevenue: number;
      averageRating: number;
    }>;
    averagePriceByCategory: Array<{
      category: string;
      averagePrice: number;
      minPrice: number;
      maxPrice: number;
    }>;
  };
  inventoryAnalysis: {
    lowStockProducts: number;
    outOfStockProducts: number;
    overstockedProducts: number;
    inventoryValue: number;
    turnoverRate: number;
  };
  performanceMetrics: {
    pageViews: number;
    uniqueVisitors: number;
    addToCartRate: number;
    purchaseRate: number;
    bounceRate: number;
    averageSessionDuration: number;
  };
  createdAt: Date;
  updatedAt: Date;
}

const ProductAnalyticsSchema = new Schema<IProductAnalytics>(
  {
    _id: { type: String, default: generateId },
    siteId: { type: String, required: true, index: true },
    startDate: { type: Date, required: true },
    endDate: { type: Date, required: true },
    period: {
      type: String,
      required: true,
      enum: ['day', 'week', 'month', 'year'],
      default: 'month',
    },
    metrics: {
      totalProducts: { type: Number, default: 0 },
      activeProducts: { type: Number, default: 0 },
      inactiveProducts: { type: Number, default: 0 },
      totalRevenue: { type: Number, default: 0 },
      averagePrice: { type: Number, default: 0 },
      totalOrders: { type: Number, default: 0 },
      averageRating: { type: Number, default: 0 },
      totalReviews: { type: Number, default: 0 },
    },
    topProducts: [
      {
        productId: { type: String, required: true },
        productName: { type: String, required: true },
        category: { type: String, required: true },
        totalSales: { type: Number, default: 0 },
        totalRevenue: { type: Number, default: 0 },
        totalOrders: { type: Number, default: 0 },
        averageRating: { type: Number, default: 0 },
        totalReviews: { type: Number, default: 0 },
        conversionRate: { type: Number, default: 0 },
      },
    ],
    categoryPerformance: [
      {
        category: { type: String, required: true },
        totalProducts: { type: Number, default: 0 },
        totalSales: { type: Number, default: 0 },
        totalRevenue: { type: Number, default: 0 },
        averageRating: { type: Number, default: 0 },
        conversionRate: { type: Number, default: 0 },
      },
    ],
    priceAnalysis: {
      priceRanges: [
        {
          range: { type: String, required: true },
          count: { type: Number, default: 0 },
          totalRevenue: { type: Number, default: 0 },
          averageRating: { type: Number, default: 0 },
        },
      ],
      averagePriceByCategory: [
        {
          category: { type: String, required: true },
          averagePrice: { type: Number, default: 0 },
          minPrice: { type: Number, default: 0 },
          maxPrice: { type: Number, default: 0 },
        },
      ],
    },
    inventoryAnalysis: {
      lowStockProducts: { type: Number, default: 0 },
      outOfStockProducts: { type: Number, default: 0 },
      overstockedProducts: { type: Number, default: 0 },
      inventoryValue: { type: Number, default: 0 },
      turnoverRate: { type: Number, default: 0 },
    },
    performanceMetrics: {
      pageViews: { type: Number, default: 0 },
      uniqueVisitors: { type: Number, default: 0 },
      addToCartRate: { type: Number, default: 0 },
      purchaseRate: { type: Number, default: 0 },
      bounceRate: { type: Number, default: 0 },
      averageSessionDuration: { type: Number, default: 0 },
    },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

// Indexes
ProductAnalyticsSchema.index({ siteId: 1, startDate: 1, endDate: 1 });
ProductAnalyticsSchema.index({ siteId: 1, period: 1 });
ProductAnalyticsSchema.index({ createdAt: -1 });

// Static methods
ProductAnalyticsSchema.statics.getAnalyticsByPeriod = async function(siteId: string, startDate: Date, endDate: Date) {
  return this.findOne({
    siteId,
    startDate: { $gte: startDate },
    endDate: { $lte: endDate },
  }).sort({ createdAt: -1 });
};

ProductAnalyticsSchema.statics.getLatestAnalytics = async function(siteId: string) {
  return this.findOne({ siteId }).sort({ createdAt: -1 });
};

ProductAnalyticsSchema.statics.getAnalyticsByPeriodType = async function(siteId: string, period: string) {
  return this.find({ siteId, period }).sort({ startDate: -1 });
};

export const ProductAnalytics = mongoose.model<IProductAnalytics>('ProductAnalytics', ProductAnalyticsSchema);
