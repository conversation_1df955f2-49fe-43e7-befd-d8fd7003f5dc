import { userPlugin } from '@/core/middleware/checkUser';
import { Elysia, t } from 'elysia';
import * as performanceOptimizationService from './performance-optimization.service';

export const performanceOptimizationRoutes = new Elysia({ prefix: '/performance-optimization' })
  .use(userPlugin)
  .get(
    '/',
    async ({ _query, store }: any) => {
      const optimizations = await performanceOptimizationService.getAllOptimizations(store.siteId);

      return {
        success: true,
        data: optimizations,
      };
    },
    {
      query: t.Object({}),
    },
  )
  .get(
    '/:type',
    async ({ params, store }: any) => {
      const { type } = params;

      const optimization = await performanceOptimizationService.getOptimization(store.siteId, type);

      return {
        success: true,
        data: optimization,
      };
    },
    {
      params: t.Object({
        type: t.Union([
          t.Literal('cache'),
          t.Literal('database'),
          t.Literal('api'),
          t.<PERSON>('frontend'),
          t.Literal('image'),
        ]),
      }),
    },
  )
  .post(
    '/',
    async ({ body, store }: any) => {
      const { type, ...settings } = body;

      const optimization = await performanceOptimizationService.createOptimization(store.siteId, type, settings);

      return {
        success: true,
        data: optimization,
      };
    },
    {
      body: t.Object({
        type: t.Union([
          t.Literal('cache'),
          t.Literal('database'),
          t.Literal('api'),
          t.Literal('frontend'),
          t.Literal('image'),
        ]),
        status: t.Optional(t.Union([t.Literal('active'), t.Literal('inactive'), t.Literal('maintenance')])),
        settings: t.Optional(t.Record(t.String(), t.Any())),
      }),
    },
  )
  .put(
    '/:type',
    async ({ params, body, store }: any) => {
      const { type } = params;

      const optimization = await performanceOptimizationService.updateOptimization(store.siteId, type, body);

      return {
        success: true,
        data: optimization,
      };
    },
    {
      params: t.Object({
        type: t.Union([
          t.Literal('cache'),
          t.Literal('database'),
          t.Literal('api'),
          t.Literal('frontend'),
          t.Literal('image'),
        ]),
      }),
      body: t.Record(t.String(), t.Any()),
    },
  )
  .delete(
    '/:type',
    async ({ params, store }: any) => {
      const { type } = params;

      const result = await performanceOptimizationService.deleteOptimization(store.siteId, type);

      return {
        success: true,
        data: result,
      };
    },
    {
      params: t.Object({
        type: t.Union([
          t.Literal('cache'),
          t.Literal('database'),
          t.Literal('api'),
          t.Literal('frontend'),
          t.Literal('image'),
        ]),
      }),
    },
  )
  .get(
    '/metrics/overview',
    async ({ _query, store }: any) => {
      const metrics = await performanceOptimizationService.getOptimizationMetrics(store.siteId);

      return {
        success: true,
        data: metrics,
      };
    },
    {
      query: t.Object({}),
    },
  )
  .post(
    '/:type/metrics',
    async ({ params, body, store }: any) => {
      const { type } = params;

      const optimization = await performanceOptimizationService.updateMetrics(store.siteId, type, body);

      return {
        success: true,
        data: optimization,
      };
    },
    {
      params: t.Object({
        type: t.Union([
          t.Literal('cache'),
          t.Literal('database'),
          t.Literal('api'),
          t.Literal('frontend'),
          t.Literal('image'),
        ]),
      }),
      body: t.Object({
        responseTime: t.Optional(t.Number()),
        throughput: t.Optional(t.Number()),
        errorRate: t.Optional(t.Number()),
        cpuUsage: t.Optional(t.Number()),
        memoryUsage: t.Optional(t.Number()),
        diskUsage: t.Optional(t.Number()),
      }),
    },
  )
  .post(
    '/:type/alerts',
    async ({ params, body, store }: any) => {
      const { type } = params;

      const optimization = await performanceOptimizationService.addAlert(store.siteId, type, body);

      return {
        success: true,
        data: optimization,
      };
    },
    {
      params: t.Object({
        type: t.Union([
          t.Literal('cache'),
          t.Literal('database'),
          t.Literal('api'),
          t.Literal('frontend'),
          t.Literal('image'),
        ]),
      }),
      body: t.Object({
        type: t.Union([t.Literal('warning'), t.Literal('error'), t.Literal('info')]),
        message: t.String(),
        threshold: t.Number(),
        currentValue: t.Number(),
      }),
    },
  )
  .delete(
    '/:type/alerts',
    async ({ params, store }: any) => {
      const { type } = params;

      const optimization = await performanceOptimizationService.clearAlerts(store.siteId, type);

      return {
        success: true,
        data: optimization,
      };
    },
    {
      params: t.Object({
        type: t.Union([
          t.Literal('cache'),
          t.Literal('database'),
          t.Literal('api'),
          t.Literal('frontend'),
          t.Literal('image'),
        ]),
      }),
    },
  )
  .post(
    '/:type/optimize',
    async ({ params, store }: any) => {
      const { type } = params;

      let optimization: any;

      switch (type) {
        case 'cache':
          optimization = await performanceOptimizationService.optimizeCache(store.siteId);
          break;
        case 'database':
          optimization = await performanceOptimizationService.optimizeDatabase(store.siteId);
          break;
        case 'api':
          optimization = await performanceOptimizationService.optimizeAPI(store.siteId);
          break;
        case 'frontend':
          optimization = await performanceOptimizationService.optimizeFrontend(store.siteId);
          break;
        case 'image':
          optimization = await performanceOptimizationService.optimizeImages(store.siteId);
          break;
        default:
          throw new Error('Invalid optimization type');
      }

      return {
        success: true,
        data: optimization,
      };
    },
    {
      params: t.Object({
        type: t.Union([
          t.Literal('cache'),
          t.Literal('database'),
          t.Literal('api'),
          t.Literal('frontend'),
          t.Literal('image'),
        ]),
      }),
    },
  );
