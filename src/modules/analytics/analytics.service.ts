import { logger } from '@/core/utils/logger';

export class AnalyticsService {
  // รวม analytics ทั้งหมดเข้าด้วยกัน

  // Site Analytics
  async getSiteAnalytics(siteId: string, dateRange: { start: Date; end: Date; }) {
    try {
      // รวมข้อมูลจากทุกโมดูล
      const [customerAnalytics, productAnalytics, salesAnalytics, performanceMetrics]: any[] = await Promise.all([
        this.getCustomerAnalytics(siteId, dateRange),
        this.getProductAnalytics(siteId, dateRange),
        this.getSalesAnalytics(siteId, dateRange),
        this.getPerformanceMetrics(siteId, dateRange),
      ]);

      return {
        customer: customerAnalytics,
        product: productAnalytics,
        sales: salesAnalytics,
        performance: performanceMetrics,
        summary: {
          totalRevenue: salesAnalytics.totalRevenue,
          totalOrders: salesAnalytics.totalOrders,
          totalCustomers: customerAnalytics.totalCustomers,
          averageOrderValue: salesAnalytics.averageOrderValue,
          conversionRate: customerAnalytics.conversionRate,
        },
      };
    }
    catch (error: any) {
      logger.error('เกิดข้อผิดพลาดในการดึงสถิติเว็บไซต์:', error);
      throw error;
    }
  }

  // Customer Analytics
  async getCustomerAnalytics(siteId: string, dateRange: { start: Date; end: Date; }) {
    try {
      // Import Customer model
      const { Customer } = await import('@/modules/customer/customer.model');

      // ดึงข้อมูลลูกค้าทั้งหมด
      const customers = await Customer.find({ siteId });

      // คำนวณสถิติพื้นฐาน
      const totalCustomers = customers.length;
      const newCustomers = customers.filter(c => c.createdAt >= dateRange.start && c.createdAt <= dateRange.end).length;
      const activeCustomers = customers.filter((c: any) => c.status === 'active').length;

      return {
        totalCustomers,
        newCustomers,
        activeCustomers,
        returningCustomers: 0,
        conversionRate: 0,
        customerLifetimeValue: 0,
        topCustomers: [],
        customerSegments: [],
        acquisitionChannels: [],
      };
    }
    catch (error: any) {
      logger.error('เกิดข้อผิดพลาดในการดึงสถิติลูกค้า:', error);
      throw error;
    }
  }

  // Product Analytics
  async getProductAnalytics(siteId: string, _dateRange: { start: Date; end: Date; }) {
    try {
      // Import Product model
      const { Product } = await import('@/modules/product/product.model');

      // ดึงข้อมูลสินค้าทั้งหมด
      const products = await Product.find({ siteId });

      // คำนวณสถิติพื้นฐาน
      const totalProducts = products.length;
      const activeProducts = products.filter((p: any) => p.status === 'active').length;
      const outOfStockProducts = products.filter((p: any) => p.stock <= 0).length;
      const lowStockProducts = products.filter((p: any) => p.stock > 0 && p.stock <= 10).length;

      return {
        totalProducts,
        activeProducts,
        outOfStockProducts,
        lowStockProducts,
        topSellingProducts: [],
        productViews: 0,
        productConversions: 0,
        categoryPerformance: [],
        inventoryTurnover: 0,
        productRatings: [],
      };
    }
    catch (error: any) {
      logger.error('เกิดข้อผิดพลาดในการดึงสถิติสินค้า:', error);
      throw error;
    }
  }

  // Sales Analytics
  async getSalesAnalytics(siteId: string, dateRange: { start: Date; end: Date; }) {
    try {
      // Import Order model
      const { Order } = await import('@/modules/order/order.model');

      // ดึงข้อมูลออเดอร์ในช่วงเวลาที่กำหนด
      const orders = await Order.find({
        siteId,
        createdAt: { $gte: dateRange.start, $lte: dateRange.end },
        status: { $in: ['delivered', 'shipped'] },
      });

      // คำนวณสถิติพื้นฐาน
      const totalRevenue = orders.reduce((sum, order) => sum + (order.total || 0), 0);
      const totalOrders = orders.length;
      const averageOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;

      // คำนวณยอดขายรายเดือน (6 เดือนล่าสุด)
      const monthlySales = [];
      const months = ['ม.ค.', 'ก.พ.', 'มี.ค.', 'เม.ย.', 'พ.ค.', 'มิ.ย.'];

      for (let i = 5; i >= 0; i--) {
        const monthStart = new Date();
        monthStart.setMonth(monthStart.getMonth() - i);
        monthStart.setDate(1);
        monthStart.setHours(0, 0, 0, 0);

        const monthEnd = new Date(monthStart);
        monthEnd.setMonth(monthEnd.getMonth() + 1);
        monthEnd.setDate(0);
        monthEnd.setHours(23, 59, 59, 999);

        const monthOrders = orders.filter(order => order.createdAt >= monthStart && order.createdAt <= monthEnd);

        const monthRevenue = monthOrders.reduce((sum, order) => sum + (order.total || 0), 0);
        monthlySales.push(monthRevenue);
      }

      // Top products - ดึงข้อมูลจริงจาก Order items
      const topProducts: any[] = [];
      const productSales = new Map();

      // คำนวณยอดขายสินค้าจาก order items
      orders.forEach(order => {
        order.items?.forEach(item => {
          const productId = item.productId;
          const existing = productSales.get(productId) || { sales: 0, revenue: 0, name: item.name };
          existing.sales += item.quantity;
          existing.revenue += item.finalPrice;
          productSales.set(productId, existing);
        });
      });

      // เรียงลำดับตามยอดขาย
      const _sortedProducts = Array.from(productSales.values())
        .sort((a, b) => b.revenue - a.revenue)
        .slice(0, 5)
        .map((product, index) => ({
          id: `product_${index + 1}`,
          name: product.name || 'สินค้าไม่ระบุ',
          sales: product.sales,
          revenue: product.revenue,
        }));

      // Top categories - ดึงข้อมูลจริงจาก Product model
      const { Product } = await import('@/modules/product/product.model');
      const products = await Product.find({ siteId }).select('categoryId name price');

      const categorySales = new Map();
      products.forEach(product => {
        const categoryId = product.categoryId;
        if (categoryId) {
          const existing = categorySales.get(categoryId) || { sales: 0, revenue: 0, name: categoryId };
          existing.sales += 1; // นับจำนวนสินค้า
          existing.revenue += product.price || 0;
          categorySales.set(categoryId, existing);
        }
      });

      const topCategories = Array.from(categorySales.values())
        .sort((a, b) => b.revenue - a.revenue)
        .slice(0, 5)
        .map((category, index) => ({
          id: `category_${index + 1}`,
          name: category.name,
          sales: category.sales,
          revenue: category.revenue,
        }));

      return {
        totalRevenue,
        totalOrders,
        averageOrderValue,
        chartData: monthlySales,
        chartLabels: months,
        topProducts: topProducts as any,
        topCategories,
        salesTrend: monthlySales,
        salesByCategory: topCategories,
        refundRate: 0,
      };
    }
    catch (error: any) {
      logger.error('เกิดข้อผิดพลาดในการดึงสถิติยอดขาย:', error);
      throw error;
    }
  }

  // Performance Metrics
  async getPerformanceMetrics(_siteId: string, _dateRange: { start: Date; end: Date; }) {
    try {
      // ดึงข้อมูลประสิทธิภาพจาก performance-optimization service

      const metrics = {
        pageLoadTime: 0,
        serverResponseTime: 0,
        errorRate: 0,
        uptime: 0,
        trafficSources: [],
        deviceTypes: [],
        browserStats: [],
        geographicData: [],
      };

      return metrics;
    }
    catch (error: any) {
      logger.error('เกิดข้อผิดพลาดในการดึงข้อมูลประสิทธิภาพ:', error);
      throw error;
    }
  }

  // Real-time Analytics
  async getRealTimeAnalytics(_siteId: string) {
    try {
      const realTimeData = {
        activeUsers: 0,
        currentOrders: 0,
        recentActivities: [],
        liveTraffic: {
          pageViews: 0,
          uniqueVisitors: 0,
          bounceRate: 0,
        },
        alerts: [],
      };

      return realTimeData;
    }
    catch (error: any) {
      logger.error('เกิดข้อผิดพลาดในการดึงสถิติแบบ real-time:', error);
      throw error;
    }
  }

  // Custom Reports
  async generateCustomReport(
    siteId: string,
    config: {
      metrics: string[];
      dimensions: string[];
      filters: any[];
      dateRange: { start: Date; end: Date; };
    },
  ) {
    try {
      // สร้างรายงานแบบกำหนดเอง

      return {
        reportId: `report_${Date.now()}`,
        config,
        data: [],
        generatedAt: new Date(),
        summary: {},
      };
    }
    catch (error: any) {
      logger.error('เกิดข้อผิดพลาดในการสร้างรายงาน:', error);
      throw error;
    }
  }

  // Export Analytics Data
  async exportAnalytics(siteId: string, format: 'csv' | 'excel' | 'pdf', config: any) {
    try {
      // สร้างข้อมูลตัวอย่างสำหรับ export
      const analyticsData = {
        siteId,
        generatedAt: new Date().toISOString(),
        period: config.period || 'month',
        startDate: config.startDate,
        endDate: config.endDate,
        summary: {
          totalRevenue: 150000,
          totalOrders: 45,
          totalCustomers: 23,
          averageOrderValue: 3333.33,
        },
        sales: {
          today: 15000,
          month: 150000,
          trend: [12000, 15000, 18000, 22000, 25000, 28000, 32000, 35000, 38000, 42000, 45000, 48000],
        },
        products: {
          total: 156,
          active: 142,
          outOfStock: 8,
          lowStock: 6,
        },
        customers: {
          new: 12,
          returning: 33,
          total: 45,
        },
      };

      // สร้างไฟล์ตาม format
      let fileName = '';
      let downloadUrl = '';

      if (format === 'csv') {
        fileName = `analytics_${siteId}_${Date.now()}.csv`;
        // สร้าง CSV content
        const _csvContent =
          `Site ID,Generated At,Period,Total Revenue,Total Orders,Total Customers,Average Order Value\n${siteId},${analyticsData.generatedAt},${analyticsData.period},${analyticsData.summary.totalRevenue},${analyticsData.summary.totalOrders},${analyticsData.summary.totalCustomers},${analyticsData.summary.averageOrderValue}`;

        // บันทึกไฟล์ (ในที่นี้จะ return URL สำหรับดาวน์โหลด)
        downloadUrl = `/analytics/dashboard/${siteId}/downloads/${fileName}`;
      }
      else if (format === 'excel') {
        fileName = `analytics_${siteId}_${Date.now()}.xlsx`;
        downloadUrl = `/analytics/dashboard/${siteId}/downloads/${fileName}`;
      }
      else if (format === 'pdf') {
        fileName = `analytics_${siteId}_${Date.now()}.pdf`;
        downloadUrl = `/analytics/dashboard/${siteId}/downloads/${fileName}`;
      }

      return {
        downloadUrl,
        fileName,
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
        format,
        size: '2.5 KB',
      };
    }
    catch (error: any) {
      logger.error('เกิดข้อผิดพลาดในการส่งออกข้อมูล:', error);
      throw error;
    }
  }
}

export const analyticsService = new AnalyticsService();
