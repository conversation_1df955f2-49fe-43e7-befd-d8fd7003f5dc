import { userPlugin } from '@/core/middleware/checkUser';
import { Elysia, t } from 'elysia';
import * as loyaltyService from './loyalty.service';

export const loyaltyRoutes = new Elysia({ prefix: '/loyalty' })
  .use(userPlugin)
  .get('/account', async ({ user }: any) => {
    const account = await loyaltyService.getLoyaltyAccount(user._id, user._id);

    return {
      success: true,
      data: account,
    };
  })
  .post(
    '/earn',
    async ({ body, user }: any) => {
      const result = await loyaltyService.earnPoints(user._id, user._id, body.amount, body.source, {
        orderId: body.orderId,
        productId: body.productId,
      });

      return {
        success: true,
        data: result,
      };
    },
    {
      body: t.Object({
        amount: t.Number(),
        source: t.Union([
          t.Literal('purchase'),
          t.Literal('referral'),
          t.Literal('birthday'),
          t.Literal('review'),
          t.<PERSON>('social'),
          t.Literal('manual'),
          t.Literal('expiry'),
        ]),
        description: t.String(),
        orderId: t.Optional(t.String()),
        productId: t.Optional(t.String()),
      }),
    },
  )
  .post(
    '/spend',
    async ({ body, user }: any) => {
      const result = await loyaltyService.spendPoints(user._id, user._id, body.amount, body.purpose, {
        orderId: body.orderId,
      });

      return {
        success: true,
        data: result,
      };
    },
    {
      body: t.Object({
        amount: t.Number(),
        purpose: t.String(),
        orderId: t.Optional(t.String()),
      }),
    },
  )
  .get(
    '/history',
    async ({ query, user }: any) => {
      const { page = 1, limit = 20 } = query;

      const result = await loyaltyService.getPointsHistory(user._id, user._id, Number(page), Number(limit));

      return {
        success: true,
        data: result,
      };
    },
    {
      query: t.Object({
        page: t.Optional(t.String()),
        limit: t.Optional(t.String()),
      }),
    },
  )
  .get('/rewards', async ({ user }: any) => {
    const rewards = await loyaltyService.getAvailableRewards(user._id, user._id);

    return {
      success: true,
      data: rewards,
    };
  })
  .post(
    '/redeem',
    async ({ body, user }: any) => {
      const result = await loyaltyService.redeemReward(user._id, user._id, body.rewardId);

      return {
        success: true,
        data: result,
      };
    },
    {
      body: t.Object({
        rewardId: t.String(),
      }),
    },
  )
  .get('/tiers', async ({ user }: any) => {
    const tiers = await loyaltyService.getLoyaltyTiers(user._id);

    return {
      success: true,
      data: tiers,
    };
  })
  .get(
    '/top-customers',
    async ({ query, user }: any) => {
      const { limit = 10 } = query;

      const customers = await loyaltyService.getTopCustomers(user._id, Number(limit));

      return {
        success: true,
        data: customers,
      };
    },
    {
      query: t.Object({
        limit: t.Optional(t.String()),
      }),
    },
  )
  .get('/stats', async ({ user }: any) => {
    const stats = await loyaltyService.getLoyaltyStats(user._id, user._id);

    return {
      success: true,
      data: stats,
    };
  })
  .post(
    '/rewards',
    async ({ body, user }: any) => {
      const reward = await loyaltyService.createLoyaltyReward(user._id, body);

      return {
        success: true,
        data: reward,
      };
    },
    {
      body: t.Object({
        name: t.String(),
        description: t.String(),
        type: t.Union([
          t.Literal('discount'),
          t.Literal('free_shipping'),
          t.Literal('free_product'),
          t.Literal('cashback'),
          t.Literal('voucher'),
        ]),
        pointsRequired: t.Number(),
        value: t.Number(),
        maxUses: t.Number(),
        minOrderAmount: t.Optional(t.Number()),
        applicableProducts: t.Optional(t.Array(t.String())),
        applicableCategories: t.Optional(t.Array(t.String())),
        startDate: t.String(),
        endDate: t.Optional(t.String()),
      }),
    },
  )
  .get('/rewards/all', async ({ user }: any) => {
    const rewards = await loyaltyService.getLoyaltyRewards(user._id);

    return {
      success: true,
      data: rewards,
    };
  })
  .post(
    '/tiers',
    async ({ body, user }: any) => {
      const tier = await loyaltyService.createLoyaltyTier(user._id, body);

      return {
        success: true,
        data: tier,
      };
    },
    {
      body: t.Object({
        name: t.String(),
        tier: t.Union([
          t.Literal('bronze'),
          t.Literal('silver'),
          t.Literal('gold'),
          t.Literal('platinum'),
          t.Literal('diamond'),
        ]),
        pointsRequired: t.Number(),
        benefits: t.Object({
          pointMultiplier: t.Number(),
          discountPercentage: t.Number(),
          freeShipping: t.Boolean(),
          prioritySupport: t.Boolean(),
          exclusiveAccess: t.Boolean(),
          birthdayBonus: t.Number(),
          referralBonus: t.Number(),
        }),
      }),
    },
  );
