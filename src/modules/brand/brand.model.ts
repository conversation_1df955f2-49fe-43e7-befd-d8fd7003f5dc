import { generateFileId } from '@/core/utils/idGenerator';
import mongoose, { type Document, Schema } from 'mongoose';

export type BrandStatus = 'active' | 'inactive' | 'pending';

export interface IBrand extends Document {
  _id: string;
  siteId: string;

  // Basic info
  name: string;
  slug: string;
  description?: string;
  logo?: string;
  banner?: string;

  // Contact info
  website?: string;
  email?: string;
  phone?: string;
  address?: string;

  // Social media
  facebook?: string;
  instagram?: string;
  twitter?: string;
  youtube?: string;

  // SEO
  metaTitle?: string;
  metaDescription?: string;
  metaKeywords?: string[];

  // Settings
  status: BrandStatus;
  featured: boolean;
  sortOrder: number;

  // Statistics
  productCount: number;
  totalSales: number;
  averageRating: number;

  // Timestamps
  createdAt: Date;
  updatedAt: Date;
}

const brandSchema = new Schema<IBrand>(
  {
    _id: { type: String, default: () => generateFileId(5) },
    siteId: { type: String, required: true, index: true },

    // Basic info
    name: { type: String, required: true },
    slug: { type: String, required: true, unique: true },
    description: { type: String },
    logo: { type: String },
    banner: { type: String },

    // Contact info
    website: { type: String },
    email: { type: String },
    phone: { type: String },
    address: { type: String },

    // Social media
    facebook: { type: String },
    instagram: { type: String },
    twitter: { type: String },
    youtube: { type: String },

    // SEO
    metaTitle: { type: String },
    metaDescription: { type: String },
    metaKeywords: [{ type: String }],

    // Settings
    status: {
      type: String,
      enum: ['active', 'inactive', 'pending'],
      default: 'active',
      index: true,
    },
    featured: { type: Boolean, default: false, index: true },
    sortOrder: { type: Number, default: 0 },

    // Statistics
    productCount: { type: Number, default: 0 },
    totalSales: { type: Number, default: 0 },
    averageRating: { type: Number, default: 0 },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

// Indexes
brandSchema.index({ siteId: 1, slug: 1 }, { unique: true });
brandSchema.index({ siteId: 1, status: 1 });
brandSchema.index({ siteId: 1, featured: 1 });
brandSchema.index({ siteId: 1, sortOrder: 1 });
brandSchema.index({ siteId: 1, name: 1 });

// Methods
brandSchema.methods.updateProductCount = function() {
  return this.model('Product')
    .countDocuments({ brandId: this._id, status: 'active' })
    .then((count: number) => {
      this.productCount = count;
      return this.save();
    });
};

brandSchema.methods.updateSalesStats = function() {
  return this.model('Order')
    .aggregate([
      { $match: { 'items.brandId': this._id, status: { $in: ['delivered', 'shipped'] } } },
      { $group: { _id: null, total: { $sum: '$totalAmount' } } },
    ])
    .then((result: any[]) => {
      this.totalSales = result[0]?.total || 0;
      return this.save();
    });
};

brandSchema.methods.updateRatingStats = function() {
  return this.model('Review')
    .aggregate([
      { $match: { 'product.brandId': this._id, status: 'approved' } },
      { $group: { _id: null, average: { $avg: '$rating' } } },
    ])
    .then((result: any[]) => {
      this.averageRating = result[0]?.average || 0;
      return this.save();
    });
};

// Static methods
brandSchema.statics.getBrandBySlug = function(slug: string, siteId: string) {
  return this.findOne({ slug, siteId });
};

brandSchema.statics.getFeaturedBrands = function(siteId: string) {
  return this.find({ siteId, status: 'active', featured: true }).sort({ sortOrder: 1, name: 1 });
};

brandSchema.statics.getActiveBrands = function(siteId: string) {
  return this.find({ siteId, status: 'active' }).sort({ sortOrder: 1, name: 1 });
};

export const Brand = mongoose.model<IBrand>('Brand', brandSchema);
