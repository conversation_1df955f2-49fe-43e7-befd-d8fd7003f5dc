import { userPlugin } from '@/core/middleware/checkUser';
import { Elysia, t } from 'elysia';
import {
  BulkNotificationSchema,
  ExpiryNotificationSchema,
  LowStockNotificationSchema,
  MarkAsReadSchema,
  MembershipNotificationSchema,
  NewProductNotificationSchema,
  NotificationQuerySchema,
  NotificationTemplateSchema,
  OrderPurchasedNotificationSchema,
  TopupNotificationSchema,
} from './notification.schema';
import * as notificationService from './notification.service';
export const notificationRoutes = new Elysia({ prefix: '/notifications' })
  .use(userPlugin)
  // ดึงการแจ้งเตือนทั้งหมด
  .get(
    '/',
    async ({ query, store }: any) => {
      const { page = '1', limit = '20', type, status } = query;

      const result = await notificationService.getNotifications(
        store.siteId,
        store.userId,
        Number(page),
        Number(limit),
        type,
        status,
      );

      return {
        success: true,
        data: result,
      };
    },
    {
      query: NotificationQuerySchema,
    },
  )
  // ดึงจำนวนการแจ้งเตือนที่ยังไม่อ่าน
  .get('/unread-count', async ({ store }: any) => {
    const count = await notificationService.getUnreadCount(store.siteId, store.userId);

    return {
      success: true,
      data: count,
    };
  })
  // ทำเครื่องหมายว่าอ่านแล้ว
  .post(
    '/mark-read',
    async ({ body, store }: any) => {
      const { notificationIds } = body;

      const result = await notificationService.markAsRead(store.siteId, notificationIds);

      return {
        success: true,
        data: result,
      };
    },
    {
      body: MarkAsReadSchema,
    },
  )
  // ลบการแจ้งเตือน
  .delete(
    '/:notificationId',
    async ({ params, store }: any) => {
      const { notificationId } = params;

      const result = await notificationService.deleteNotification(store.siteId, notificationId, store.userId);

      return {
        success: true,
        data: result,
      };
    },
    {
      params: t.Object({
        notificationId: t.String(),
      }),
    },
  )
  // ดึงสถิติการแจ้งเตือน
  .get('/stats', async ({ store }: any) => {
    const stats = await notificationService.getNotificationStats(store.siteId, store.userId);

    return {
      success: true,
      data: stats,
    };
  })
  // จัดการ Templates
  .get(
    '/templates',
    async ({ query, store }: any) => {
      const { type } = query;
      const templates = await notificationService.getNotificationTemplates(store.siteId, type);

      return {
        success: true,
        data: templates,
      };
    },
    {
      query: t.Object({
        type: t.Optional(
          t.Union([
            t.Literal('order'),
            t.Literal('product'),
            t.Literal('promotion'),
            t.Literal('system'),
            t.Literal('chat'),
            t.Literal('affiliate'),
            t.Literal('topup'),
            t.Literal('membership'),
            t.Literal('expiry'),
            t.Literal('inventory'),
            t.Literal('payment'),
            t.Literal('security'),
            t.Literal('marketing'),
          ]),
        ),
      }),
    },
  )
  .post(
    '/templates',
    async ({ body, store }: any) => {
      const template = await notificationService.createNotificationTemplate(store.siteId, body);

      return {
        success: true,
        data: template,
      };
    },
    {
      body: NotificationTemplateSchema,
    },
  )
  // จัดการการตั้งค่า
  .get('/settings', async ({ store }: any) => {
    const settings = await notificationService.getNotificationSettings(store.siteId, store.userId);

    return {
      success: true,
      data: settings,
    };
  })
  .put(
    '/settings',
    async ({ body, store }: any) => {
      const settings = await notificationService.updateNotificationSettings(store.siteId, store.userId, body);

      return {
        success: true,
        data: settings,
      };
    },
    {
      body: t.Object({
        preferences: t.Optional(
          t.Object({
            orderUpdates: t.Optional(t.Boolean()),
            productAlerts: t.Optional(t.Boolean()),
            promotions: t.Optional(t.Boolean()),
            systemMessages: t.Optional(t.Boolean()),
            chatMessages: t.Optional(t.Boolean()),
            affiliateUpdates: t.Optional(t.Boolean()),
            topupAlerts: t.Optional(t.Boolean()),
            membershipUpdates: t.Optional(t.Boolean()),
            expiryWarnings: t.Optional(t.Boolean()),
            inventoryAlerts: t.Optional(t.Boolean()),
            paymentNotifications: t.Optional(t.Boolean()),
            securityAlerts: t.Optional(t.Boolean()),
            marketingMessages: t.Optional(t.Boolean()),
          }),
        ),
        channels: t.Optional(
          t.Object({
            inApp: t.Optional(t.Boolean()),
            email: t.Optional(t.Boolean()),
            push: t.Optional(t.Boolean()),
            sms: t.Optional(t.Boolean()),
          }),
        ),
        quietHours: t.Optional(
          t.Object({
            enabled: t.Optional(t.Boolean()),
            startTime: t.Optional(t.String()),
            endTime: t.Optional(t.String()),
            timezone: t.Optional(t.String()),
          }),
        ),
      }),
    },
  )
  // ส่งการแจ้งเตือนจำนวนมาก
  .post(
    '/bulk',
    async ({ body, store }: any) => {
      const { recipients, notificationData } = body;

      const result = await notificationService.sendBulkNotifications(store.siteId, recipients, notificationData);

      return {
        success: true,
        data: result,
      };
    },
    {
      body: BulkNotificationSchema,
    },
  )
  // === API สำหรับการแจ้งเตือนเฉพาะ ===

  // แจ้งเตือนเติมเงิน
  .post(
    '/topup',
    async ({ body, store }: any) => {
      const { userId, amount, balance } = body;

      const result = await notificationService.notifyTopup(store.siteId, userId, amount, balance);

      return {
        success: true,
        data: result,
      };
    },
    {
      body: TopupNotificationSchema,
    },
  )
  // แจ้งเตือนสมาชิกใหม่
  .post(
    '/new-member',
    async ({ body, store }: any) => {
      const { adminUsers, customerName, customerId } = body;

      const result = await notificationService.notifyNewMember(store.siteId, adminUsers, customerName, customerId);

      return {
        success: true,
        data: result,
      };
    },
    {
      body: MembershipNotificationSchema,
    },
  )
  // แจ้งเตือนวันหมดอายุ
  .post(
    '/expiry',
    async ({ body, store }: any) => {
      const { userId, expiryDate, daysLeft } = body;

      const result = await notificationService.notifyExpiry(store.siteId, userId, new Date(expiryDate), daysLeft);

      return {
        success: true,
        data: result,
      };
    },
    {
      body: ExpiryNotificationSchema,
    },
  )
  // แจ้งเตือนสต็อกต่ำ
  .post(
    '/low-stock',
    async ({ body, store }: any) => {
      const { adminUsers, productName, productId, stockLevel, threshold } = body;

      const result = await notificationService.notifyLowStock(
        store.siteId,
        adminUsers,
        productName,
        productId,
        stockLevel,
        threshold,
      );

      return {
        success: true,
        data: result,
      };
    },
    {
      body: LowStockNotificationSchema,
    },
  )
  // แจ้งเตือนสินค้าใหม่
  .post(
    '/new-product',
    async ({ body, store }: any) => {
      const { customerIds, productName, productId, productImage } = body;

      const result = await notificationService.notifyNewProduct(
        store.siteId,
        customerIds,
        productName,
        productId,
        productImage,
      );

      return {
        success: true,
        data: result,
      };
    },
    {
      body: NewProductNotificationSchema,
    },
  )
  // แจ้งเตือนมีคำสั่งซื้อ
  .post(
    '/order-purchased',
    async ({ body, store }: any) => {
      const { adminUsers, customerName, orderId, amount } = body;

      const result = await notificationService.notifyOrderPurchased(
        store.siteId,
        adminUsers,
        customerName,
        orderId,
        amount,
      );

      return {
        success: true,
        data: result,
      };
    },
    {
      body: OrderPurchasedNotificationSchema,
    },
  );
