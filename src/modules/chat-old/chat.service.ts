import { HttpError } from '@/core/utils/error';
import { Customer } from '@/modules/customer/customer.model';
import { User } from '@/modules/user/user.model';
import { customAlphabet } from 'nanoid';
import { ChatMessage, ChatRoom, ChatSession } from './chat.model';

const generateRoomId = () => {
  const nanoid = customAlphabet('0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz', 16)();
  return nanoid;
};

const generateSessionId = () => {
  const nanoid = customAlphabet('0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz', 24)();
  return nanoid;
};

// Chat Service
export async function createChatRoom(siteId: string, participants: any[], settings: any = {}) {
  try {
    // ตรวจสอบว่าผู้เข้าร่วมมีอยู่จริง
    for (const participant of participants) {
      if (participant.userType === 'user') {
        const user = await User.findById(participant.userId);
        if (!user) {
          throw new HttpError(404, `ไม่พบ user: ${participant.userId}`);
        }
        participant.name = user.name;
        participant.avatar = user.avatar;
      }
      else if (participant.userType === 'customer') {
        const customer = await Customer.findById(participant.userId);
        if (!customer) {
          throw new HttpError(404, `ไม่พบ customer: ${participant.userId}`);
        }
        participant.name = customer.name;
        participant.avatar = customer.avatar;
      }
    }

    const roomId = generateRoomId();

    const chatRoom = await ChatRoom.create({
      siteId,
      roomId,
      participants,
      ...settings,
    });

    return chatRoom;
  }
  catch (err: any) {
    console.error('Error in createChatRoom:', err);
    if (err instanceof HttpError) throw err;
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะสร้าง chat room');
  }
}

export async function getChatRoom(siteId: string, roomId: string) {
  try {
    const chatRoom = await (ChatRoom as any).findByRoomId(siteId, roomId);
    if (!chatRoom) {
      throw new HttpError(404, 'ไม่พบ chat room');
    }
    return chatRoom;
  }
  catch (err: any) {
    console.error('Error in getChatRoom:', err);
    if (err instanceof HttpError) throw err;
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึง chat room');
  }
}

export async function getUserChatRooms(siteId: string, userId: string) {
  try {
    const chatRooms = await (ChatRoom as any).findByParticipant(siteId, userId);
    return chatRooms;
  }
  catch (err: any) {
    console.error('Error in getUserChatRooms:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึง chat rooms');
  }
}

export async function updateChatRoom(siteId: string, roomId: string, updates: any) {
  try {
    const chatRoom = await ChatRoom.findOneAndUpdate(
      { siteId, roomId },
      { $set: updates },
      { new: true, runValidators: true },
    );

    if (!chatRoom) {
      throw new HttpError(404, 'ไม่พบ chat room');
    }

    return chatRoom;
  }
  catch (err: any) {
    console.error('Error in updateChatRoom:', err);
    if (err instanceof HttpError) throw err;
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะอัปเดต chat room');
  }
}

export async function closeChatRoom(siteId: string, roomId: string) {
  try {
    const chatRoom = await ChatRoom.findOneAndUpdate({ siteId, roomId }, { $set: { status: 'closed' } }, { new: true });

    if (!chatRoom) {
      throw new HttpError(404, 'ไม่พบ chat room');
    }

    return chatRoom;
  }
  catch (err: any) {
    console.error('Error in closeChatRoom:', err);
    if (err instanceof HttpError) throw err;
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะปิด chat room');
  }
}

export async function sendMessage(siteId: string, roomId: string, messageData: any) {
  try {
    // ตรวจสอบว่า chat room มีอยู่และ active
    const chatRoom = await (ChatRoom as any).findByRoomId(siteId, roomId);
    if (!chatRoom) {
      throw new HttpError(404, 'ไม่พบ chat room');
    }
    if (chatRoom.status !== 'active') {
      throw new HttpError(400, 'Chat room ไม่ได้เปิดใช้งาน');
    }

    // ตรวจสอบว่าผู้ส่งเป็นผู้เข้าร่วมในห้องหรือไม่
    const isParticipant = chatRoom.participants.some((p: any) => p.userId === messageData.senderId);
    if (!isParticipant) {
      throw new HttpError(403, 'คุณไม่มีสิทธิ์ส่งข้อความในห้องนี้');
    }

    const message = await ChatMessage.create({
      siteId,
      roomId,
      ...messageData,
    });

    // อัปเดตสถิติห้อง
    await ChatRoom.findByIdAndUpdate(chatRoom._id, {
      $inc: { 'stats.totalMessages': 1 },
      $set: { 'stats.lastMessageAt': new Date() },
    });

    return message;
  }
  catch (err: any) {
    console.error('Error in sendMessage:', err);
    if (err instanceof HttpError) throw err;
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะส่งข้อความ');
  }
}

export async function getMessages(siteId: string, roomId: string, page: number = 1, limit: number = 50) {
  try {
    const skip = (page - 1) * limit;
    const messages = await (ChatMessage as any).findByRoom(siteId, roomId, limit).skip(skip).sort({ createdAt: 1 });

    const total = await ChatMessage.countDocuments({ siteId, roomId });

    return {
      messages,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    };
  }
  catch (err: any) {
    console.error('Error in getMessages:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงข้อความ');
  }
}

export async function markMessageAsRead(siteId: string, roomId: string, messageId: string, userId: string) {
  try {
    const message = await ChatMessage.findOneAndUpdate(
      { siteId, roomId, _id: messageId },
      {
        $set: { isRead: true },
        $addToSet: { 'metadata.readBy': userId },
      },
      { new: true },
    );

    if (!message) {
      throw new HttpError(404, 'ไม่พบข้อความ');
    }

    return message;
  }
  catch (err: any) {
    console.error('Error in markMessageAsRead:', err);
    if (err instanceof HttpError) throw err;
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะทำเครื่องหมายข้อความ');
  }
}

export async function getUnreadCount(siteId: string, roomId: string, userId: string) {
  try {
    const count = await (ChatMessage as any).getUnreadCount(siteId, roomId, userId);
    return count;
  }
  catch (err: any) {
    console.error('Error in getUnreadCount:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงจำนวนข้อความที่ยังไม่ได้อ่าน');
  }
}

export async function createChatSession(
  siteId: string,
  roomId: string,
  userId: string,
  userType: string,
  sessionData: any,
) {
  try {
    // ปิด session เก่าของผู้ใช้นี้
    await ChatSession.updateMany({ siteId, userId, isActive: true }, { $set: { isActive: false } });

    const sessionId = generateSessionId();

    const session = await ChatSession.create({
      siteId,
      roomId,
      userId,
      userType,
      sessionId,
      ...sessionData,
    });

    // อัปเดตสถานะ online ของผู้ใช้
    await ChatRoom.updateMany(
      { siteId, 'participants.userId': userId },
      {
        $set: {
          'participants.$.isOnline': true,
          'participants.$.lastSeen': new Date(),
        },
      },
    );

    return session;
  }
  catch (err: any) {
    console.error('Error in createChatSession:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะสร้าง chat session');
  }
}

export async function updateSessionActivity(sessionId: string) {
  try {
    const session = await (ChatSession as any).updateLastActivity(sessionId);
    return session;
  }
  catch (err: any) {
    console.error('Error in updateSessionActivity:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะอัปเดต session activity');
  }
}

export async function closeChatSession(sessionId: string) {
  try {
    const session = await ChatSession.findOneAndUpdate({ sessionId }, { $set: { isActive: false } }, { new: true });

    if (session) {
      // อัปเดตสถานะ offline ของผู้ใช้
      await ChatRoom.updateMany(
        { siteId: session.siteId, 'participants.userId': session.userId },
        {
          $set: {
            'participants.$.isOnline': false,
            'participants.$.lastSeen': new Date(),
          },
        },
      );
    }

    return session;
  }
  catch (err: any) {
    console.error('Error in closeChatSession:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะปิด chat session');
  }
}

export async function getOnlineUsers(siteId: string, roomId: string) {
  try {
    const chatRoom = await (ChatRoom as any).findByRoomId(siteId, roomId);
    if (!chatRoom) {
      throw new HttpError(404, 'ไม่พบ chat room');
    }

    const onlineUsers = chatRoom.participants.filter((p: any) => p.isOnline);
    return onlineUsers;
  }
  catch (err: any) {
    console.error('Error in getOnlineUsers:', err);
    if (err instanceof HttpError) throw err;
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะดึงผู้ใช้ออนไลน์');
  }
}

export async function editMessage(siteId: string, messageId: string, userId: string, newContent: string) {
  try {
    const message = await ChatMessage.findOne({ siteId, _id: messageId, senderId: userId });
    if (!message) {
      throw new HttpError(404, 'ไม่พบข้อความหรือคุณไม่มีสิทธิ์แก้ไข');
    }

    const updatedMessage = await ChatMessage.findByIdAndUpdate(
      messageId,
      {
        $set: {
          content: newContent,
          'metadata.editedAt': new Date(),
        },
      },
      { new: true },
    );

    return updatedMessage;
  }
  catch (err: any) {
    console.error('Error in editMessage:', err);
    if (err instanceof HttpError) throw err;
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะแก้ไขข้อความ');
  }
}

export async function deleteMessage(siteId: string, messageId: string, userId: string) {
  try {
    const message = await ChatMessage.findOne({ siteId, _id: messageId, senderId: userId });
    if (!message) {
      throw new HttpError(404, 'ไม่พบข้อความหรือคุณไม่มีสิทธิ์ลบ');
    }

    const deletedMessage = await ChatMessage.findByIdAndUpdate(
      messageId,
      { $set: { 'metadata.deletedAt': new Date() } },
      { new: true },
    );

    return deletedMessage;
  }
  catch (err: any) {
    console.error('Error in deleteMessage:', err);
    if (err instanceof HttpError) throw err;
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะลบข้อความ');
  }
}

export async function searchMessages(
  siteId: string,
  roomId: string,
  query: string,
  page: number = 1,
  limit: number = 20,
) {
  try {
    const skip = (page - 1) * limit;
    const messages = await ChatMessage.find({
      siteId,
      roomId,
      content: { $regex: query, $options: 'i' },
      'metadata.deletedAt': { $exists: false },
    })
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await ChatMessage.countDocuments({
      siteId,
      roomId,
      content: { $regex: query, $options: 'i' },
      'metadata.deletedAt': { $exists: false },
    });

    return {
      messages,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    };
  }
  catch (err: any) {
    console.error('Error in searchMessages:', err);
    throw new HttpError(500, 'เกิดข้อผิดพลาดขณะค้นหาข้อความ');
  }
}
