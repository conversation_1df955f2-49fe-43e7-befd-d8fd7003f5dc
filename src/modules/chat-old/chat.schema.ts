import { t } from 'elysia';

// General Chat Schemas
export const createChatRoomSchema = t.Object({
  participants: t.<PERSON>y(
    t.Object({
      userId: t.String({ minLength: 1, error: 'รหัสผู้ใช้ต้องไม่ว่าง' }),
      userType: t.Union([t.Literal('user'), t.Literal('customer'), t.Literal('support')], {
        error: 'ประเภทผู้ใช้ไม่ถูกต้อง',
      }),
      name: t.Optional(t.String({ error: 'ชื่อผู้ใช้ต้องไม่ว่าง' })),
      avatar: t.<PERSON>tional(t.String({ error: 'รูปภาพผู้ใช้ต้องไม่ว่าง' })),
    }),
    { error: 'ผู้เข้าร่วมห้องการสนทนาต้องมีอย่างน้อยหนึ่งคน' },
  ),
  options: t.Optional(
    t.Object({
      type: t.Optional(
        t.Union([t.Literal('user_customer'), t.Literal('user_support'), t.Literal('customer_support')], {
          error: 'ประเภทการสนทนาต้องเป็นหนึ่งในประเภทที่กำหนด',
        }),
      ),
      subject: t.Optional(t.String({ error: 'หัวข้อต้องไม่ว่าง' })),
      category: t.Optional(t.String({ error: 'หมวดหมู่ต้องไม่ว่าง' })),
      priority: t.Optional(
        t.Union([t.Literal('low'), t.Literal('medium'), t.Literal('high')], {
          error: 'ความสำคัญต้องเป็นหนึ่งในความสำคัญที่กำหนด',
        }),
      ),
    }),
  ),
});

export const sendMessageSchema = t.Object({
  senderType: t.Union([t.Literal('user'), t.Literal('customer'), t.Literal('support')], {
    error: 'ประเภทผู้ส่งข้อความไม่ถูกต้อง',
  }),
  content: t.String({ minLength: 1, error: 'ข้อความต้องไม่ว่าง' }),
  options: t.Optional(
    t.Object({
      messageType: t.Optional(
        t.Union([t.Literal('text'), t.Literal('image'), t.Literal('file'), t.Literal('system')], {
          error: 'ประเภทข้อความไม่ถูกต้อง',
        }),
      ),
      attachments: t.Optional(
        t.Array(
          t.Object({
            fileName: t.String({ error: 'ชื่อไฟล์ต้องไม่ว่าง' }),
            fileUrl: t.String({ error: 'ลิงก์ไฟล์ต้องไม่ว่าง' }),
            fileSize: t.Number({ error: 'ขนาดไฟล์ต้องเป็นตัวเลข' }),
            fileType: t.String({ error: 'ประเภทไฟล์ต้องไม่ว่าง' }),
          }),
        ),
      ),
      replyTo: t.Optional(t.String({ error: 'ข้อความที่ตอบกลับต้องไม่ว่าง' })),
    }),
  ),
});

export const createSessionSchema = t.Object({
  roomId: t.String({ minLength: 1, error: 'รหัสห้องการสนทนาต้องไม่ว่าง' }),
  userType: t.Union([t.Literal('user'), t.Literal('customer'), t.Literal('support')], {
    error: 'ประเภทผู้ใช้ไม่ถูกต้อง',
  }),
  metadata: t.Optional(
    t.Object({
      userAgent: t.Optional(t.String({ error: 'ข้อมูลผู้ใช้เบราว์เซอร์ต้องไม่ว่าง' })),
      ipAddress: t.Optional(t.String({ error: 'ที่อยู่ IP ต้องไม่ว่าง' })),
    }),
  ),
});

export const chatQuerySchema = t.Object({
  page: t.Optional(t.Number({ minimum: 1, error: 'หน้าต้องเป็นตัวเลขที่มากกว่าหรือเท่ากับ 1' })),
  limit: t.Optional(t.Number({ minimum: 1, maximum: 100, error: 'จำนวนข้อมูลต้องอยู่ในช่วง 1-100' })),
  status: t.Optional(
    t.Union([t.Literal('active'), t.Literal('closed'), t.Literal('archived')], {
      error: 'สถานะห้องการสนทนาต้องเป็นหนึ่งในสถานะที่กำหนด',
    }),
  ),
});

// LiveChat Schemas
export const createLiveChatSessionSchema = t.Object({
  customerName: t.String({ minLength: 1, error: 'ชื่อลูกค้าต้องไม่ว่าง' }),
  description: t.String({ minLength: 1, error: 'คำอธิบายต้องไม่ว่าง' }),
  options: t.Optional(
    t.Object({
      customerEmail: t.Optional(t.String({ format: 'email', error: 'อีเมลลูกค้าต้องเป็นรูปแบบอีเมลที่ถูกต้อง' })),
      priority: t.Optional(
        t.Union([t.Literal('low'), t.Literal('medium'), t.Literal('high'), t.Literal('urgent')], {
          error: 'ความสำคัญต้องเป็นหนึ่งในความสำคัญที่กำหนด',
        }),
      ),
      category: t.Optional(
        t.Union(
          [
            t.Literal('general'),
            t.Literal('sales'),
            t.Literal('support'),
            t.Literal('technical'),
            t.Literal('billing'),
          ],
          { error: 'หมวดหมู่ต้องเป็นหนึ่งในหมวดหมู่ที่กำหนด' },
        ),
      ),
      subject: t.Optional(t.String({ error: 'หัวข้อต้องไม่ว่าง' })),
      metadata: t.Optional(
        t.Object({
          userAgent: t.Optional(t.String({ error: 'ข้อมูลผู้ใช้เบราว์เซอร์ต้องไม่ว่าง' })),
          ipAddress: t.Optional(t.String({ error: 'ที่อยู่ IP ต้องไม่ว่าง' })),
          pageUrl: t.Optional(t.String({ error: 'ลิงก์หน้าเว็บต้องไม่ว่าง' })),
          referrer: t.Optional(t.String({ error: 'ลิงก์อ้างอิงต้องไม่ว่าง' })),
          deviceType: t.Optional(t.String({ error: 'ประเภทอุปกรณ์ต้องไม่ว่าง' })),
          location: t.Optional(t.String({ error: 'สถานที่ต้องไม่ว่าง' })),
        }),
      ),
    }),
  ),
});

export const sendLiveChatMessageSchema = t.Object({
  content: t.String({ minLength: 1, error: 'ข้อความต้องไม่ว่าง' }),
  senderName: t.Optional(t.String({ error: 'ชื่อผู้ส่งข้อความต้องไม่ว่าง' })),
  options: t.Optional(
    t.Object({
      messageType: t.Optional(
        t.Union([t.Literal('text'), t.Literal('image'), t.Literal('file'), t.Literal('system')], {
          error: 'ประเภทข้อความไม่ถูกต้อง',
        }),
      ),
      attachments: t.Optional(
        t.Array(
          t.Object({
            fileName: t.String({ error: 'ชื่อไฟล์ต้องไม่ว่าง' }),
            fileUrl: t.String({ error: 'ลิงก์ไฟล์ต้องไม่ว่าง' }),
            fileSize: t.Number({ error: 'ขนาดไฟล์ต้องเป็นตัวเลข' }),
            fileType: t.String({ error: 'ประเภทไฟล์ต้องไม่ว่าง' }),
          }),
        ),
      ),
    }),
  ),
});

export const assignAgentSchema = t.Object({
  agentId: t.String({ minLength: 1, error: 'รหัสผู้แนะนำต้องไม่ว่าง' }),
  agentName: t.String({ minLength: 1, error: 'ชื่อผู้แนะนำต้องไม่ว่าง' }),
});

export const rateSessionSchema = t.Object({
  rating: t.Number({ minimum: 1, maximum: 5, error: 'คะแนนต้องอยู่ในช่วง 1-5' }),
  feedback: t.Optional(t.String({ error: 'ข้อความความคิดเห็นต้องไม่ว่าง' })),
});

// ChatBot Schemas
export const createChatBotSchema = t.Object({
  name: t.String({ minLength: 1, error: 'ชื่อบอทต้องไม่ว่าง' }),
  description: t.Optional(t.String({ error: 'คำอธิบายบอทต้องไม่ว่าง' })),
  settings: t.Object({
    welcomeMessage: t.Optional(t.String({ error: 'ข้อความต้องไม่ว่าง' })),
    autoResponse: t.Optional(t.Boolean({ error: 'การตอบอัตโนมัติต้องเป็นค่าตรรกะ' })),
    transferToHuman: t.Optional(t.Boolean({ error: 'การโอนผู้ใช้ต้องเป็นค่าตรรกะ' })),
    transferThreshold: t.Optional(t.Number({ minimum: 0, maximum: 1, error: 'ความน่าจะเป็นต้องอยู่ในช่วง 0-1' })),
    operatingHours: t.Optional(
      t.Object({
        enabled: t.Boolean({ error: 'การทำงานตามเวลาต้องเป็นค่าตรรกะ' }),
        startTime: t.String({ error: 'เวลาเริ่มต้นต้องไม่ว่าง' }),
        endTime: t.String({ error: 'เวลาสิ้นสุดต้องไม่ว่าง' }),
        timezone: t.Optional(t.String({ error: 'เขตเวลาต้องไม่ว่าง' })),
        offlineMessage: t.Optional(t.String({ error: 'ข้อความออฟไลน์ต้องไม่ว่าง' })),
      }),
    ),
  }),
  responses: t.Optional(
    t.Array(
      t.Object({
        trigger: t.String({ minLength: 1, error: 'ข้อความสัญญาณต้องไม่ว่าง' }),
        response: t.String({ minLength: 1, error: 'ข้อความตอบกลับต้องไม่ว่าง' }),
        confidence: t.Optional(t.Number({ minimum: 0, maximum: 1, error: 'ความมั่นใจต้องอยู่ในช่วง 0-1' })),
        category: t.Optional(t.String({ error: 'หมวดหมู่ต้องไม่ว่าง' })),
        followUpQuestions: t.Optional(t.Array(t.String({ error: 'คำถามต่อท้ายต้องไม่ว่าง' }))),
      }),
    ),
  ),
});

export const updateChatBotSchema = t.Object({
  name: t.Optional(t.String({ minLength: 1, error: 'ชื่อบอทต้องไม่ว่าง' })),
  description: t.Optional(t.String({ error: 'คำอธิบายบอทต้องไม่ว่าง' })),
  isActive: t.Optional(t.Boolean({ error: 'สถานะบอทต้องเป็นค่าตรรกะ' })),
  settings: t.Optional(
    t.Object({
      welcomeMessage: t.Optional(t.String({ error: 'ข้อความต้องไม่ว่าง' })),
      autoResponse: t.Optional(t.Boolean({ error: 'การตอบอัตโนมัติต้องเป็นค่าตรรกะ' })),
      transferToHuman: t.Optional(t.Boolean({ error: 'การโอนผู้ใช้ต้องเป็นค่าตรรกะ' })),
      transferThreshold: t.Optional(t.Number({ minimum: 0, maximum: 1, error: 'ความน่าจะเป็นต้องอยู่ในช่วง 0-1' })),
      operatingHours: t.Optional(
        t.Object({
          enabled: t.Optional(t.Boolean({ error: 'การทำงานตามเวลาต้องเป็นค่าตรรกะ' })),
          startTime: t.Optional(t.String({ error: 'เวลาเริ่มต้นต้องไม่ว่าง' })),
          endTime: t.Optional(t.String({ error: 'เวลาสิ้นสุดต้องไม่ว่าง' })),
          timezone: t.Optional(t.String({ error: 'เขตเวลาต้องไม่ว่าง' })),
          offlineMessage: t.Optional(t.String({ error: 'ข้อความออฟไลน์ต้องไม่ว่าง' })),
        }),
      ),
    }),
  ),
  responses: t.Optional(
    t.Array(
      t.Object({
        trigger: t.String({ minLength: 1, error: 'ข้อความสัญญาณต้องไม่ว่าง' }),
        response: t.String({ minLength: 1, error: 'ข้อความตอบกลับต้องไม่ว่าง' }),
        confidence: t.Optional(t.Number({ minimum: 0, maximum: 1, error: 'ความมั่นใจต้องอยู่ในช่วง 0-1' })),
        category: t.Optional(t.String({ error: 'หมวดหมู่ต้องไม่ว่าง' })),
        followUpQuestions: t.Optional(t.Array(t.String({ error: 'คำถามต่อท้ายต้องไม่ว่าง' }))),
      }),
    ),
  ),
});

// FAQ Schemas
export const createFAQSchema = t.Object({
  category: t.String({ minLength: 1, error: 'หมวดหมู่ต้องไม่ว่าง' }),
  question: t.String({ minLength: 1, error: 'คำถามต้องไม่ว่าง' }),
  answer: t.String({ minLength: 1, error: 'คำตอบต้องไม่ว่าง' }),
  tags: t.Optional(t.Array(t.String({ error: 'แท็กต้องไม่ว่าง' }))),
});

export const updateFAQSchema = t.Object({
  category: t.Optional(t.String({ minLength: 1, error: 'หมวดหมู่ต้องไม่ว่าง' })),
  question: t.Optional(t.String({ minLength: 1, error: 'คำถามต้องไม่ว่าง' })),
  answer: t.Optional(t.String({ minLength: 1, error: 'คำตอบต้องไม่ว่าง' })),
  tags: t.Optional(t.Array(t.String({ error: 'แท็กต้องไม่ว่าง' }))),
  isActive: t.Optional(t.Boolean({ error: 'สถานะคำถามที่ถูกต้อง' })),
});

export const faqQuerySchema = t.Object({
  category: t.Optional(t.String({ error: 'หมวดหมู่ต้องไม่ว่าง' })),
  q: t.Optional(t.String({ error: 'คำค้นหาต้องไม่ว่าง' })),
});

export const liveChatQuerySchema = t.Object({
  status: t.Optional(
    t.Union([t.Literal('waiting'), t.Literal('active'), t.Literal('resolved'), t.Literal('closed')], {
      error: 'สถานะการสนทนาลิฟเชต์ต้องเป็นหนึ่งในสถานะที่กำหนด',
    }),
  ),
  priority: t.Optional(
    t.Union([t.Literal('low'), t.Literal('medium'), t.Literal('high'), t.Literal('urgent')], {
      error: 'ความสำคัญต้องเป็นหนึ่งในความสำคัญที่กำหนด',
    }),
  ),
  category: t.Optional(
    t.Union(
      [t.Literal('general'), t.Literal('sales'), t.Literal('support'), t.Literal('technical'), t.Literal('billing')],
      { error: 'หมวดหมู่การสนทนาลิฟเชต์ต้องเป็นหนึ่งในหมวดหมู่ที่กำหนด' },
    ),
  ),
  agentId: t.Optional(t.String({ error: 'รหัสผู้แนะนำต้องไม่ว่าง' })),
  page: t.Optional(t.Number({ minimum: 1, error: 'หน้าต้องเป็นตัวเลขที่มากกว่าหรือเท่ากับ 1' })),
  limit: t.Optional(t.Number({ minimum: 1, maximum: 100, error: 'จำนวนข้อมูลต้องอยู่ในช่วง 1-100' })),
});
