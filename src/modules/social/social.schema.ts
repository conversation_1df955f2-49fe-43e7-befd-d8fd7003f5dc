import { t } from 'elysia';

// Social Commerce Schemas
export const SocialPostSchema = t.Object({
  type: t.Union(
    [
      t.Literal('product', { error: 'ต้องเป็น product เท่านั้น' }),
      t.Literal('review', { error: 'ต้องเป็น review เท่านั้น' }),
      t.Literal('lifestyle', { error: 'ต้องเป็น lifestyle เท่านั้น' }),
      t.Literal('promotion', { error: 'ต้องเป็น promotion เท่านั้น' }),
      t.Literal('story', { error: 'ต้องเป็น story เท่านั้น' }),
    ],
    { error: 'ประเภทโพสต์ไม่ถูกต้อง' },
  ),
  title: t.String({ error: 'title ต้องเป็นข้อความ' }),
  content: t.String({ error: 'content ต้องเป็นข้อความ' }),
  images: t.Optional(
    t.Array(
      t.Object({
        url: t.String({ error: 'url ต้องเป็นข้อความ' }),
        caption: t.Optional(t.String({ error: 'caption ต้องเป็นข้อความ' })),
        order: t.Number({ error: 'order ต้องเป็นตัวเลข' }),
      }),
      { error: 'images ต้องเป็น array ของ object' },
    ),
  ),
  products: t.Optional(
    t.Array(
      t.Object({
        productId: t.String({ error: 'productId ต้องเป็นข้อความ' }),
        position: t.Number({ error: 'position ต้องเป็นตัวเลข' }),
      }),
      { error: 'products ต้องเป็น array ของ object' },
    ),
  ),
  hashtags: t.Array(t.String({ error: 'hashtags ต้องเป็นข้อความ' }), { error: 'hashtags ต้องเป็น array ของข้อความ' }),
  location: t.Optional(t.String({ error: 'location ต้องเป็นข้อความ' })),
  isSponsored: t.Optional(t.Boolean({ error: 'isSponsored ต้องเป็น true หรือ false เท่านั้น' })),
  scheduledAt: t.Optional(t.String({ error: 'scheduledAt ต้องเป็นข้อความ' })),
});

export const SocialPostResponseSchema = t.Object({
  success: t.Boolean(),
  data: t.Object({
    _id: t.String(),
    siteId: t.String(),
    userId: t.String(),
    userType: t.String(),
    type: t.String(),
    title: t.String(),
    content: t.String(),
    images: t.Optional(
      t.Array(
        t.Object({
          url: t.String(),
          caption: t.Optional(t.String()),
          order: t.Number(),
        }),
      ),
    ),
    products: t.Optional(
      t.Array(
        t.Object({
          productId: t.String(),
          position: t.Number(),
        }),
      ),
    ),
    hashtags: t.Array(t.String()),
    location: t.Optional(t.String()),
    isSponsored: t.Boolean(),
    status: t.String(),
    engagement: t.Object({
      likes: t.Number(),
      comments: t.Number(),
      shares: t.Number(),
      views: t.Number(),
      clicks: t.Number(),
    }),
    scheduledAt: t.Optional(t.String()),
    publishedAt: t.Optional(t.String()),
    createdAt: t.String(),
    updatedAt: t.String(),
  }),
});

export const SocialCommentSchema = t.Object({
  content: t.String(),
  parentCommentId: t.Optional(t.String()),
});

export const SocialCommentResponseSchema = t.Object({
  success: t.Boolean(),
  data: t.Object({
    _id: t.String(),
    siteId: t.String(),
    postId: t.String(),
    userId: t.String(),
    userType: t.String(),
    content: t.String(),
    parentCommentId: t.Optional(t.String()),
    likes: t.Number(),
    replies: t.Number(),
    status: t.String(),
    createdAt: t.String(),
    updatedAt: t.String(),
  }),
});

export const SocialShareSchema = t.Object({
  platform: t.Union(
    [
      t.Literal('facebook', { error: 'ต้องเป็น facebook เท่านั้น' }),
      t.Literal('twitter', { error: 'ต้องเป็น twitter เท่านั้น' }),
      t.Literal('instagram', { error: 'ต้องเป็น instagram เท่านั้น' }),
      t.Literal('linkedin', { error: 'ต้องเป็น linkedin เท่านั้น' }),
      t.Literal('pinterest', { error: 'ต้องเป็น pinterest เท่านั้น' }),
      t.Literal('whatsapp', { error: 'ต้องเป็น whatsapp เท่านั้น' }),
      t.Literal('telegram', { error: 'ต้องเป็น telegram เท่านั้น' }),
    ],
    { error: 'ระบบการแชร์ไม่ถูกต้อง' },
  ),
  url: t.Optional(t.String({ error: 'url ต้องเป็นข้อความ' })),
  message: t.Optional(t.String({ error: 'message ต้องเป็นข้อความ' })),
});

export const SocialShareResponseSchema = t.Object({
  success: t.Boolean(),
  data: t.Object({
    _id: t.String(),
    siteId: t.String(),
    postId: t.String(),
    userId: t.String(),
    platform: t.String(),
    url: t.Optional(t.String()),
    message: t.Optional(t.String()),
    shareCount: t.Number(),
    clickCount: t.Number(),
    createdAt: t.String(),
    updatedAt: t.String(),
  }),
});

export const SocialInfluencerSchema = t.Object({
  name: t.String({ error: 'ชื่อผู้ใช้ต้องเป็นข้อความ' }),
  username: t.String({ error: 'ชื่อผู้ใช้ต้องเป็นข้อความ' }),
  bio: t.Optional(t.String({ error: 'bio ต้องเป็นข้อความ' })),
  avatar: t.Optional(t.String({ error: 'avatar ต้องเป็นข้อความ' })),
  platforms: t.Array(
    t.Object({
      name: t.String({ error: 'ชื่อระบบต้องเป็นข้อความ' }),
      username: t.String({ error: 'ชื่อผู้ใช้ระบบต้องเป็นข้อความ' }),
      followers: t.Number({ error: 'followers ต้องเป็นตัวเลข' }),
      engagement: t.Number({ error: 'engagement ต้องเป็นตัวเลข' }),
    }),
    { error: 'platforms ต้องเป็น array ของ object' },
  ),
  categories: t.Array(t.String({ error: 'categories ต้องเป็นข้อความ' }), {
    error: 'categories ต้องเป็น array ของข้อความ',
  }),
  contactInfo: t.Object({
    email: t.Optional(t.String({ error: 'email ต้องเป็นข้อความ' })),
    phone: t.Optional(t.String({ error: 'phone ต้องเป็นข้อความ' })),
    website: t.Optional(t.String({ error: 'website ต้องเป็นข้อความ' })),
  }),
  rates: t.Object({
    post: t.Number({ error: 'post ต้องเป็นตัวเลข' }),
    story: t.Number({ error: 'story ต้องเป็นตัวเลข' }),
    video: t.Number({ error: 'video ต้องเป็นตัวเลข' }),
    collaboration: t.Number({ error: 'collaboration ต้องเป็นตัวเลข' }),
  }),
});

export const SocialInfluencerResponseSchema = t.Object({
  success: t.Boolean(),
  data: t.Object({
    _id: t.String(),
    siteId: t.String(),
    name: t.String(),
    username: t.String(),
    bio: t.Optional(t.String()),
    avatar: t.Optional(t.String()),
    platforms: t.Array(
      t.Object({
        name: t.String(),
        username: t.String(),
        followers: t.Number(),
        engagement: t.Number(),
      }),
    ),
    categories: t.Array(t.String()),
    contactInfo: t.Object({
      email: t.Optional(t.String()),
      phone: t.Optional(t.String()),
      website: t.Optional(t.String()),
    }),
    rates: t.Object({
      post: t.Number(),
      story: t.Number(),
      video: t.Number(),
      collaboration: t.Number(),
    }),
    isActive: t.Boolean(),
    isVerified: t.Boolean(),
    stats: t.Object({
      totalPosts: t.Number(),
      totalEngagement: t.Number(),
      averageEngagement: t.Number(),
      totalReach: t.Number(),
    }),
    createdAt: t.String(),
    updatedAt: t.String(),
  }),
});

export const SocialCampaignSchema = t.Object({
  name: t.String({ error: 'ชื่อคอมแพนน์ต้องเป็นข้อความ' }),
  description: t.String({ error: 'คำอธิบายต้องเป็นข้อความ' }),
  objective: t.Union(
    [
      t.Literal('awareness', { error: 'ต้องเป็น awareness เท่านั้น' }),
      t.Literal('engagement', { error: 'ต้องเป็น engagement เท่านั้น' }),
      t.Literal('traffic', { error: 'ต้องเป็น traffic เท่านั้น' }),
      t.Literal('conversions', { error: 'ต้องเป็น conversions เท่านั้น' }),
      t.Literal('sales', { error: 'ต้องเป็น sales เท่านั้น' }),
    ],
    { error: 'objective ไม่ถูกต้อง' },
  ),
  platforms: t.Array(t.String({ error: 'platforms ต้องเป็นข้อความ' }), {
    error: 'platforms ต้องเป็น array ของข้อความ',
  }),
  influencers: t.Array(t.String({ error: 'influencers ต้องเป็นข้อความ' }), {
    error: 'influencers ต้องเป็น array ของข้อความ',
  }),
  budget: t.Number({ error: 'budget ต้องเป็นตัวเลข' }),
  startDate: t.String({ error: 'startDate ต้องเป็นข้อความ' }),
  endDate: t.String({ error: 'endDate ต้องเป็นข้อความ' }),
  hashtags: t.Array(t.String({ error: 'hashtags ต้องเป็นข้อความ' }), { error: 'hashtags ต้องเป็น array ของข้อความ' }),
  requirements: t.Object({
    postCount: t.Number({ error: 'postCount ต้องเป็นตัวเลข' }),
    storyCount: t.Number({ error: 'storyCount ต้องเป็นตัวเลข' }),
    videoCount: t.Number({ error: 'videoCount ต้องเป็นตัวเลข' }),
    mentions: t.Array(t.String({ error: 'mentions ต้องเป็นข้อความ' }), { error: 'mentions ต้องเป็น array ของข้อความ' }),
    callToAction: t.String({ error: 'callToAction ต้องเป็นข้อความ' }),
  }),
});

export const SocialCampaignResponseSchema = t.Object({
  success: t.Boolean(),
  data: t.Object({
    _id: t.String(),
    siteId: t.String(),
    name: t.String(),
    description: t.String(),
    objective: t.String(),
    platforms: t.Array(t.String()),
    influencers: t.Array(t.String()),
    budget: t.Number(),
    startDate: t.String(),
    endDate: t.String(),
    hashtags: t.Array(t.String()),
    requirements: t.Object({
      postCount: t.Number(),
      storyCount: t.Number(),
      videoCount: t.Number(),
      mentions: t.Array(t.String()),
      callToAction: t.String(),
    }),
    status: t.String(),
    stats: t.Object({
      totalPosts: t.Number(),
      totalEngagement: t.Number(),
      totalReach: t.Number(),
      totalClicks: t.Number(),
      totalConversions: t.Number(),
      totalSales: t.Number(),
    }),
    createdAt: t.String(),
    updatedAt: t.String(),
  }),
});

export const SocialPostListResponseSchema = t.Object({
  success: t.Boolean(),
  data: t.Object({
    posts: t.Array(
      t.Object({
        _id: t.String(),
        siteId: t.String(),
        userId: t.String(),
        userType: t.String(),
        type: t.String(),
        title: t.String(),
        content: t.String(),
        images: t.Optional(
          t.Array(
            t.Object({
              url: t.String(),
              caption: t.Optional(t.String()),
              order: t.Number(),
            }),
          ),
        ),
        products: t.Optional(
          t.Array(
            t.Object({
              productId: t.String(),
              position: t.Number(),
            }),
          ),
        ),
        hashtags: t.Array(t.String()),
        location: t.Optional(t.String()),
        isSponsored: t.Boolean(),
        status: t.String(),
        engagement: t.Object({
          likes: t.Number(),
          comments: t.Number(),
          shares: t.Number(),
          views: t.Number(),
          clicks: t.Number(),
        }),
        scheduledAt: t.Optional(t.String()),
        publishedAt: t.Optional(t.String()),
        createdAt: t.String(),
        updatedAt: t.String(),
      }),
    ),
    pagination: t.Object({
      page: t.Number(),
      limit: t.Number(),
      total: t.Number(),
      pages: t.Number(),
    }),
  }),
});

export const SocialInfluencerListResponseSchema = t.Object({
  success: t.Boolean(),
  data: t.Array(
    t.Object({
      _id: t.String(),
      siteId: t.String(),
      name: t.String(),
      username: t.String(),
      bio: t.Optional(t.String()),
      avatar: t.Optional(t.String()),
      platforms: t.Array(
        t.Object({
          name: t.String(),
          username: t.String(),
          followers: t.Number(),
          engagement: t.Number(),
        }),
      ),
      categories: t.Array(t.String()),
      contactInfo: t.Object({
        email: t.Optional(t.String()),
        phone: t.Optional(t.String()),
        website: t.Optional(t.String()),
      }),
      rates: t.Object({
        post: t.Number(),
        story: t.Number(),
        video: t.Number(),
        collaboration: t.Number(),
      }),
      isActive: t.Boolean(),
      isVerified: t.Boolean(),
      stats: t.Object({
        totalPosts: t.Number(),
        totalEngagement: t.Number(),
        averageEngagement: t.Number(),
        totalReach: t.Number(),
      }),
      createdAt: t.String(),
      updatedAt: t.String(),
    }),
  ),
});

export const SocialCampaignListResponseSchema = t.Object({
  success: t.Boolean(),
  data: t.Array(
    t.Object({
      _id: t.String(),
      siteId: t.String(),
      name: t.String(),
      description: t.String(),
      objective: t.String(),
      platforms: t.Array(t.String()),
      influencers: t.Array(t.String()),
      budget: t.Number(),
      startDate: t.String(),
      endDate: t.String(),
      hashtags: t.Array(t.String()),
      requirements: t.Object({
        postCount: t.Number(),
        storyCount: t.Number(),
        videoCount: t.Number(),
        mentions: t.Array(t.String()),
        callToAction: t.String(),
      }),
      status: t.String(),
      stats: t.Object({
        totalPosts: t.Number(),
        totalEngagement: t.Number(),
        totalReach: t.Number(),
        totalClicks: t.Number(),
        totalConversions: t.Number(),
        totalSales: t.Number(),
      }),
      createdAt: t.String(),
      updatedAt: t.String(),
    }),
  ),
});

export const SocialStatsResponseSchema = t.Object({
  success: t.Boolean(),
  data: t.Array(
    t.Object({
      _id: t.String(),
      count: t.Number(),
      totalLikes: t.Number(),
      totalComments: t.Number(),
      totalShares: t.Number(),
      totalViews: t.Number(),
      totalClicks: t.Number(),
    }),
  ),
});

export const InfluencerStatsResponseSchema = t.Object({
  success: t.Boolean(),
  data: t.Object({
    totalPosts: t.Number(),
    totalLikes: t.Number(),
    totalComments: t.Number(),
    totalShares: t.Number(),
    averageEngagement: t.Number(),
  }),
});
