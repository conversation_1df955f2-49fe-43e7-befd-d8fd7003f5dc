import { checkSite } from '@/core/middleware/checkSite';
import { checkUser } from '@/core/middleware/checkUser';
import { logger } from '@/core/utils/logger';
import { Elysia } from 'elysia';
import {
  createMenuItemSchema,
  createPageSchema,
  menuQuerySchema,
  pageQuerySchema,
  reorderItemsSchema,
  updateMenuItemSchema,
  updatePageSchema,
} from './menu.schema';
import { MenuService } from './menu.service';

const menuService = new MenuService();

export const menuRoutes = new Elysia({ prefix: '/menu' })
  .use(checkUser)
  .use(checkSite)
  // ===== MENU ITEM ROUTES =====

  // สร้างรายการเมนู
  .post(
    '/items',
    async ({ body, user, site }) => {
      try {
        if (user.role !== 'admin') {
          throw new Error('ไม่มีสิทธิ์ในการดำเนินการ');
        }

        const menuItem = await menuService.createMenuItem({
          ...body,
          siteId: site._id,
        });

        return {
          success: true,
          message: 'สร้างรายการเมนูสำเร็จ',
          data: menuItem,
        };
      }
      catch (error: any) {
        logger.error('เกิดข้อผิดพลาดในการสร้างรายการเมนู:', error);
        return {
          success: false,
          message: error.message || 'เกิดข้อผิดพลาดในการสร้างรายการเมนู',
        };
      }
    },
    {
      body: createMenuItemSchema,
    },
  )
  // ดึงรายการเมนู
  .get(
    '/items',
    async ({ query, site }) => {
      try {
        const result = await menuService.getMenuItems({
          ...query,
          siteId: site._id,
        });

        return {
          success: true,
          message: 'ดึงรายการเมนูสำเร็จ',
          data: result,
        };
      }
      catch (error: any) {
        logger.error('เกิดข้อผิดพลาดในการดึงรายการเมนู:', error);
        return {
          success: false,
          message: error.message || 'เกิดข้อผิดพลาดในการดึงรายการเมนู',
        };
      }
    },
    {
      query: menuQuerySchema,
    },
  )
  // ดึงโครงสร้างเมนู
  .get('/tree/:location?', async ({ params, site }) => {
    try {
      const location = params.location as 'header' | 'footer' | 'mobile' | undefined;
      const menuTree = await menuService.getMenuTree(site._id, location);

      return {
        success: true,
        message: 'ดึงโครงสร้างเมนูสำเร็จ',
        data: menuTree,
      };
    }
    catch (error: any) {
      logger.error('เกิดข้อผิดพลาดในการดึงโครงสร้างเมนู:', error);
      return {
        success: false,
        message: error.message || 'เกิดข้อผิดพลาดในการดึงโครงสร้างเมนู',
      };
    }
  })
  // อัพเดทรายการเมนู
  .patch(
    '/items/:id',
    async ({ params, body, user, site }) => {
      try {
        if (user.role !== 'admin') {
          throw new Error('ไม่มีสิทธิ์ในการดำเนินการ');
        }

        const menuItem = await menuService.updateMenuItem(params.id, site._id, body);

        if (!menuItem) {
          return {
            success: false,
            message: 'ไม่พบรายการเมนูที่ระบุ',
          };
        }

        return {
          success: true,
          message: 'อัพเดทรายการเมนูสำเร็จ',
          data: menuItem,
        };
      }
      catch (error: any) {
        logger.error('เกิดข้อผิดพลาดในการอัพเดทรายการเมนู:', error);
        return {
          success: false,
          message: error.message || 'เกิดข้อผิดพลาดในการอัพเดทรายการเมนู',
        };
      }
    },
    {
      body: updateMenuItemSchema,
    },
  )
  // ลบรายการเมนู
  .delete('/items/:id', async ({ params, user, site }) => {
    try {
      if (user.role !== 'admin') {
        throw new Error('ไม่มีสิทธิ์ในการดำเนินการ');
      }

      await menuService.deleteMenuItem(params.id, site._id);

      return {
        success: true,
        message: 'ลบรายการเมนูสำเร็จ',
      };
    }
    catch (error: any) {
      logger.error('เกิดข้อผิดพลาดในการลบรายการเมนู:', error);
      return {
        success: false,
        message: error.message || 'เกิดข้อผิดพลาดในการลบรายการเมนู',
      };
    }
  })
  // จัดเรียงลำดับเมนู
  .patch(
    '/items/reorder',
    async ({ body, user }) => {
      try {
        if (user.role !== 'admin') {
          throw new Error('ไม่มีสิทธิ์ในการดำเนินการ');
        }

        await menuService.reorderMenuItems(body.items);

        return {
          success: true,
          message: 'จัดเรียงลำดับเมนูสำเร็จ',
        };
      }
      catch (error: any) {
        logger.error('เกิดข้อผิดพลาดในการจัดเรียงลำดับเมนู:', error);
        return {
          success: false,
          message: error.message || 'เกิดข้อผิดพลาดในการจัดเรียงลำดับเมนู',
        };
      }
    },
    {
      body: reorderItemsSchema,
    },
  )
  // ===== PAGE ROUTES =====

  // สร้างหน้าเว็บ
  .post(
    '/pages',
    async ({ body, user, site }) => {
      try {
        if (user.role !== 'admin') {
          throw new Error('ไม่มีสิทธิ์ในการดำเนินการ');
        }

        const page = await menuService.createPage({
          ...body,
          siteId: site._id,
          author: user._id,
        });

        return {
          success: true,
          message: 'สร้างหน้าเว็บสำเร็จ',
          data: page,
        };
      }
      catch (error: any) {
        logger.error('เกิดข้อผิดพลาดในการสร้างหน้าเว็บ:', error);
        return {
          success: false,
          message: error.message || 'เกิดข้อผิดพลาดในการสร้างหน้าเว็บ',
        };
      }
    },
    {
      body: createPageSchema,
    },
  )
  // ดึงรายการหน้าเว็บ
  .get(
    '/pages',
    async ({ query, site }) => {
      try {
        const result = await menuService.getPages({
          ...query,
          siteId: site._id,
        });

        return {
          success: true,
          message: 'ดึงรายการหน้าเว็บสำเร็จ',
          data: result,
        };
      }
      catch (error: any) {
        logger.error('เกิดข้อผิดพลาดในการดึงรายการหน้าเว็บ:', error);
        return {
          success: false,
          message: error.message || 'เกิดข้อผิดพลาดในการดึงรายการหน้าเว็บ',
        };
      }
    },
    {
      query: pageQuerySchema,
    },
  )
  // ดึงหน้าเว็บตาม ID
  .get('/pages/:id', async ({ params, site }) => {
    try {
      const page = await menuService.getPageById(params.id, site._id);

      if (!page) {
        return {
          success: false,
          message: 'ไม่พบหน้าเว็บที่ระบุ',
        };
      }

      return {
        success: true,
        message: 'ดึงข้อมูลหน้าเว็บสำเร็จ',
        data: page,
      };
    }
    catch (error: any) {
      logger.error('เกิดข้อผิดพลาดในการดึงข้อมูลหน้าเว็บ:', error);
      return {
        success: false,
        message: error.message || 'เกิดข้อผิดพลาดในการดึงข้อมูลหน้าเว็บ',
      };
    }
  })
  // ดึงหน้าเว็บตาม slug (สำหรับแสดงผล)
  .get('/pages/slug/:slug', async ({ params, site }) => {
    try {
      const page = await menuService.getPageBySlug(site._id, params.slug);

      if (!page) {
        return {
          success: false,
          message: 'ไม่พบหน้าเว็บที่ระบุ',
        };
      }

      return {
        success: true,
        message: 'ดึงข้อมูลหน้าเว็บสำเร็จ',
        data: page,
      };
    }
    catch (error: any) {
      logger.error('เกิดข้อผิดพลาดในการดึงข้อมูลหน้าเว็บ:', error);
      return {
        success: false,
        message: error.message || 'เกิดข้อผิดพลาดในการดึงข้อมูลหน้าเว็บ',
      };
    }
  })
  // อัพเดทหน้าเว็บ
  .patch(
    '/pages/:id',
    async ({ params, body, user, site }) => {
      try {
        if (user.role !== 'admin') {
          throw new Error('ไม่มีสิทธิ์ในการดำเนินการ');
        }

        const page = await menuService.updatePage(params.id, site._id, body);

        if (!page) {
          return {
            success: false,
            message: 'ไม่พบหน้าเว็บที่ระบุ',
          };
        }

        return {
          success: true,
          message: 'อัพเดทหน้าเว็บสำเร็จ',
          data: page,
        };
      }
      catch (error: any) {
        logger.error('เกิดข้อผิดพลาดในการอัพเดทหน้าเว็บ:', error);
        return {
          success: false,
          message: error.message || 'เกิดข้อผิดพลาดในการอัพเดทหน้าเว็บ',
        };
      }
    },
    {
      body: updatePageSchema,
    },
  )
  // ลบหน้าเว็บ
  .delete('/pages/:id', async ({ params, user, site }) => {
    try {
      if (user.role !== 'admin') {
        throw new Error('ไม่มีสิทธิ์ในการดำเนินการ');
      }

      await menuService.deletePage(params.id, site._id);

      return {
        success: true,
        message: 'ลบหน้าเว็บสำเร็จ',
      };
    }
    catch (error: any) {
      logger.error('เกิดข้อผิดพลาดในการลบหน้าเว็บ:', error);
      return {
        success: false,
        message: error.message || 'เกิดข้อผิดพลาดในการลบหน้าเว็บ',
      };
    }
  })
  // สร้าง sitemap
  .get('/sitemap', async ({ site }) => {
    try {
      const sitemap = await menuService.generateSitemap(site._id);

      return {
        success: true,
        message: 'สร้าง sitemap สำเร็จ',
        data: sitemap,
      };
    }
    catch (error: any) {
      logger.error('เกิดข้อผิดพลาดในการสร้าง sitemap:', error);
      return {
        success: false,
        message: error.message || 'เกิดข้อผิดพลาดในการสร้าง sitemap',
      };
    }
  });
