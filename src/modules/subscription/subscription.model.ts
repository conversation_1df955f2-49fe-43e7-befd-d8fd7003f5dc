import mongoose, { type Document, Schema } from 'mongoose';
import { customAlphabet } from 'nanoid';

const generateId = () => {
  const timestamp = Date.now().toString(36);
  const nanoid = customAlphabet('0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz', 12)();
  return `${timestamp}${nanoid}`;
};

// Site Package Subscription - การสมัครแพ็คเกจสำหรับ site
export interface ISubscription extends Document {
  _id: string;
  siteId: string;
  userId: string; // site owner
  packageType: 'daily' | 'weekly' | 'monthly' | 'yearly' | 'permanent';
  status: 'active' | 'paused' | 'cancelled' | 'expired';
  autoRenew: boolean;
  startDate: Date;
  endDate?: Date;
  nextRenewalDate?: Date;
  lastRenewalDate?: Date;
  pricing: {
    amount: number;
    currency: string;
    originalPrice: number;
    discount?: number;
    discountType?: 'percentage' | 'fixed';
  };
  billing: {
    paymentMethod: string;
    paymentReference?: string;
    nextBillingDate?: Date;
    lastBillingDate?: Date;
    billingHistory: Array<{
      date: Date;
      amount: number;
      status: 'success' | 'failed' | 'pending';
      reference?: string;
      failureReason?: string;
    }>;
  };
  renewalHistory: Array<{
    renewedAt: Date;
    packageType: string;
    daysAdded: number;
    amount: number;
    method: 'auto' | 'manual';
    previousExpiry: Date;
    newExpiry: Date;
  }>;
  notifications: {
    expiryWarning: boolean;
    renewalSuccess: boolean;
    renewalFailure: boolean;
    daysBeforeExpiry: number;
  };
  stats: {
    totalRenewals: number;
    totalSpent: number;
    averageRenewalAmount: number;
    longestActiveStreak: number; // วันที่ active ต่อเนื่องนานที่สุด
    currentActiveStreak: number; // วันที่ active ต่อเนื่องปัจจุบัน
  };
  createdAt: Date;
  updatedAt: Date;
}

// Site Package Discount - ส่วนลดสำหรับแพ็คเกจ
export interface ISubscriptionDiscount extends Document {
  _id: string;
  code: string;
  name: string;
  description: string;
  type: 'percentage' | 'fixed';
  value: number;
  packageTypes: string[]; // แพ็คเกจที่ใช้ได้
  minAmount?: number;
  maxDiscount?: number;
  usageLimit?: number;
  usedCount: number;
  validFrom: Date;
  validTo: Date;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Site Package Notification - การแจ้งเตือนเกี่ยวกับแพ็คเกจ
export interface ISubscriptionNotification extends Document {
  _id: string;
  siteId: string;
  userId: string;
  subscriptionId?: string;
  type: 'expiry_warning' | 'renewal_success' | 'renewal_failure' | 'auto_renewal_disabled' | 'payment_failed';
  title: string;
  message: string;
  data?: any;
  isRead: boolean;
  sentAt: Date;
  createdAt: Date;
  updatedAt: Date;
}

const SubscriptionSchema = new Schema<ISubscription>(
  {
    _id: { type: String, default: generateId },
    siteId: { type: String, required: true, index: true },
    userId: { type: String, required: true, index: true }, // site owner
    packageType: {
      type: String,
      required: true,
      enum: ['daily', 'weekly', 'monthly', 'yearly', 'permanent'],
    },
    status: {
      type: String,
      required: true,
      enum: ['active', 'paused', 'cancelled', 'expired'],
      default: 'active',
    },
    autoRenew: { type: Boolean, default: false },
    startDate: { type: Date, required: true },
    endDate: { type: Date },
    nextRenewalDate: { type: Date },
    lastRenewalDate: { type: Date },
    pricing: {
      amount: { type: Number, required: true },
      currency: { type: String, default: 'THB' },
      originalPrice: { type: Number, required: true },
      discount: { type: Number, default: 0 },
      discountType: {
        type: String,
        enum: ['percentage', 'fixed'],
        default: 'fixed',
      },
    },
    billing: {
      paymentMethod: { type: String, required: true },
      paymentReference: { type: String },
      nextBillingDate: { type: Date },
      lastBillingDate: { type: Date },
      billingHistory: [
        {
          date: { type: Date, required: true },
          amount: { type: Number, required: true },
          status: {
            type: String,
            enum: ['success', 'failed', 'pending'],
            required: true,
          },
          reference: { type: String },
          failureReason: { type: String },
        },
      ],
    },
    renewalHistory: [
      {
        renewedAt: { type: Date, required: true },
        packageType: { type: String, required: true },
        daysAdded: { type: Number, required: true },
        amount: { type: Number, required: true },
        method: {
          type: String,
          enum: ['auto', 'manual'],
          required: true,
        },
        previousExpiry: { type: Date, required: true },
        newExpiry: { type: Date, required: true },
      },
    ],
    notifications: {
      expiryWarning: { type: Boolean, default: true },
      renewalSuccess: { type: Boolean, default: true },
      renewalFailure: { type: Boolean, default: true },
      daysBeforeExpiry: { type: Number, default: 7 },
    },
    stats: {
      totalRenewals: { type: Number, default: 0 },
      totalSpent: { type: Number, default: 0 },
      averageRenewalAmount: { type: Number, default: 0 },
      longestActiveStreak: { type: Number, default: 0 },
      currentActiveStreak: { type: Number, default: 0 },
    },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

const SubscriptionDiscountSchema = new Schema<ISubscriptionDiscount>(
  {
    _id: { type: String, default: generateId },
    code: { type: String, required: true, unique: true, uppercase: true },
    name: { type: String, required: true },
    description: { type: String, required: true },
    type: {
      type: String,
      required: true,
      enum: ['percentage', 'fixed'],
    },
    value: { type: Number, required: true },
    packageTypes: [
      {
        type: String,
        enum: ['daily', 'weekly', 'monthly', 'yearly', 'permanent'],
      },
    ],
    minAmount: { type: Number },
    maxDiscount: { type: Number },
    usageLimit: { type: Number },
    usedCount: { type: Number, default: 0 },
    validFrom: { type: Date, required: true },
    validTo: { type: Date, required: true },
    isActive: { type: Boolean, default: true },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

const SubscriptionNotificationSchema = new Schema<ISubscriptionNotification>(
  {
    _id: { type: String, default: generateId },
    siteId: { type: String, required: true, index: true },
    userId: { type: String, required: true, index: true },
    subscriptionId: { type: String, index: true },
    type: {
      type: String,
      required: true,
      enum: ['expiry_warning', 'renewal_success', 'renewal_failure', 'auto_renewal_disabled', 'payment_failed'],
    },
    title: { type: String, required: true },
    message: { type: String, required: true },
    data: { type: Schema.Types.Mixed },
    isRead: { type: Boolean, default: false },
    sentAt: { type: Date, required: true },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

// Indexes
// SubscriptionSchema.index({ siteId: 1, userId: 1 });
// SubscriptionSchema.index({ siteId: 1, status: 1 });
// SubscriptionSchema.index({ nextRenewalDate: 1 });
// SubscriptionSchema.index({ 'billing.nextBillingDate': 1 });
// SubscriptionSchema.index({ packageType: 1 });
// SubscriptionSchema.index({ autoRenew: 1, status: 1 });

// SubscriptionDiscountSchema.index({ code: 1 }, { unique: true });
// SubscriptionDiscountSchema.index({ isActive: 1, validFrom: 1, validTo: 1 });
// SubscriptionDiscountSchema.index({ packageTypes: 1 });

// SubscriptionNotificationSchema.index({ siteId: 1, userId: 1 });
// SubscriptionNotificationSchema.index({ siteId: 1, isRead: 1 });
// SubscriptionNotificationSchema.index({ type: 1 });

// Static methods
SubscriptionSchema.statics.findBySite = async function(siteId: string) {
  return this.find({ siteId }).sort({ createdAt: -1 });
};

SubscriptionSchema.statics.findByOwner = async function(userId: string) {
  return this.find({ userId }).sort({ createdAt: -1 });
};

SubscriptionSchema.statics.findActive = async function(siteId?: string) {
  const filter: any = { status: 'active' };
  if (siteId) filter.siteId = siteId;
  return this.find(filter);
};

SubscriptionSchema.statics.findExpiring = async function(days: number = 7) {
  const futureDate = new Date();
  futureDate.setDate(futureDate.getDate() + days);

  return this.find({
    status: 'active',
    nextRenewalDate: { $lte: futureDate, $gte: new Date() },
  });
};

SubscriptionSchema.statics.findAutoRenewable = async function() {
  const now = new Date();
  return this.find({
    status: 'active',
    autoRenew: true,
    nextRenewalDate: { $lte: now },
  });
};

SubscriptionDiscountSchema.statics.findValidDiscount = async function(code: string, packageType: string) {
  const now = new Date();
  return this.findOne({
    code: code.toUpperCase(),
    isActive: true,
    validFrom: { $lte: now },
    validTo: { $gte: now },
    packageTypes: packageType,
    $expr: {
      $or: [{ $eq: ['$usageLimit', null] }, { $lt: ['$usedCount', '$usageLimit'] }],
    },
  });
};

SubscriptionNotificationSchema.statics.findUnread = async function(siteId: string, userId: string) {
  return this.find({ siteId, userId, isRead: false }).sort({ createdAt: -1 });
};

export const Subscription = mongoose.model<ISubscription>('Subscription', SubscriptionSchema);
export const SubscriptionDiscount = mongoose.model<ISubscriptionDiscount>(
  'SubscriptionDiscount',
  SubscriptionDiscountSchema,
);
export const SubscriptionNotification = mongoose.model<ISubscriptionNotification>(
  'SubscriptionNotification',
  SubscriptionNotificationSchema,
);
