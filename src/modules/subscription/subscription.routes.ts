import { requireSiteOwner, requireUserAuth } from '@/core/middleware';
import { Elysia, t } from 'elysia';
import * as subscriptionService from './subscription.service';

export const subscriptionRoutes = new Elysia({ prefix: '/subscription' })
  // Public routes - ข้อมูลแพ็คเกจ
  .get('/packages', async () => {
    const packages = subscriptionService.getPackageInfo();
    return {
      success: true,
      message: 'ดึงข้อมูลแพ็คเกจสำเร็จ',
      statusMessage: 'สำเร็จ!',
      timestamp: new Date().toISOString(),
      data: packages,
    };
  })
  // ตรวจสอบรหัสส่วนลด
  .post(
    '/validate-discount',
    async ({ _body }) => {
      // TODO: Implement discount validation
      return {
        success: true,
        message: 'ตรวจสอบรหัสส่วนลดสำเร็จ',
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
        data: { valid: false, message: 'รหัสส่วนลดไม่ถูกต้อง' },
      };
    },
    {
      body: t.Object({
        discountCode: t.String({ minLength: 1 }),
      }),
    },
  )
  // Protected routes - ต้อง login
  .use(requireUserAuth)
  // ดึงรายการ subscription ของ user
  .get(
    '/my-subscriptions',
    async ({ query, store }: any) => {
      const { page, limit, status, siteId } = query;
      const result = await subscriptionService.getUserSubscriptions((store as any).user._id, {
        page: page ? Number(page) : undefined,
        limit: limit ? Number(limit) : undefined,
        status,
        siteId,
      });

      return {
        success: true,
        message: 'ดึงรายการ subscription สำเร็จ',
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
        data: result,
      };
    },
    {
      query: t.Object({
        page: t.Optional(t.String()),
        limit: t.Optional(t.String()),
        status: t.Optional(t.String()),
        siteId: t.Optional(t.String()),
      }),
    },
  )
  // ดึงการแจ้งเตือน
  .get(
    '/notifications',
    async ({ query, store }: any) => {
      const { page, limit, unreadOnly } = query;
      const result = await subscriptionService.getNotifications(
        '', // siteId - ถ้าต้องการแจ้งเตือนทั้งหมด
        (store as any).user._id,
        {
          page: page ? Number(page) : undefined,
          limit: limit ? Number(limit) : undefined,
          unreadOnly: unreadOnly === 'true',
        },
      );

      return {
        success: true,
        message: 'ดึงการแจ้งเตือนสำเร็จ',
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
        data: result,
      };
    },
    {
      query: t.Object({
        page: t.Optional(t.String()),
        limit: t.Optional(t.String()),
        unreadOnly: t.Optional(t.String()),
      }),
    },
  )
  // อ่านการแจ้งเตือน
  .put(
    '/notifications/:notificationId/read',
    async ({ params, store }: any) => {
      const { notificationId } = params;
      const notification = await subscriptionService.markNotificationAsRead(notificationId, (store as any).user._id);

      return {
        success: true,
        message: 'อ่านการแจ้งเตือนสำเร็จ',
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
        data: notification,
      };
    },
    {
      params: t.Object({ notificationId: t.String() }),
    },
  )
  // Site-specific routes - ต้องเป็น site owner
  .use(requireSiteOwner)
  // สร้าง subscription สำหรับ site
  .post(
    '/sites/:siteId/subscribe',
    async ({ params, body, store }: any) => {
      const { siteId } = params;
      const { packageType, autoRenew, paymentMethod, discountCode } = body;

      const subscription = await subscriptionService.createSiteSubscription(
        siteId,
        (store as any).user._id,
        packageType as any,
        { autoRenew, paymentMethod, discountCode },
      );

      return {
        success: true,
        message: 'สมัคร subscription สำเร็จ',
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
        data: subscription,
      };
    },
    {
      params: t.Object({ siteId: t.String() }),
      body: t.Object({
        packageType: t.String(),
        autoRenew: t.Optional(t.Boolean()),
        paymentMethod: t.String(),
        discountCode: t.Optional(t.String()),
      }),
    },
  )
  // ดึงรายการ subscription ของ site
  .get(
    '/sites/:siteId/subscriptions',
    async ({ params, query, store }: any) => {
      const { siteId } = params;
      const { page, limit, status } = query;

      const result = await subscriptionService.getUserSubscriptions((store as any).user._id, {
        page: page ? Number(page) : undefined,
        limit: limit ? Number(limit) : undefined,
        status,
        siteId,
      });

      return {
        success: true,
        message: 'ดึงรายการ subscription ของไซต์สำเร็จ',
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
        data: result,
      };
    },
    {
      params: t.Object({ siteId: t.String() }),
      query: t.Object({
        page: t.Optional(t.String()),
        limit: t.Optional(t.String()),
        status: t.Optional(t.String()),
      }),
    },
  )
  // เปิด/ปิด auto renew
  .put(
    '/sites/:siteId/subscriptions/:subscriptionId/auto-renew',
    async ({ params, body, store }: any) => {
      const { siteId, subscriptionId } = params;
      const { autoRenew } = body;

      const subscription = await subscriptionService.toggleAutoRenew(
        siteId,
        subscriptionId,
        (store as any).user._id,
        autoRenew,
      );

      return {
        success: true,
        message: `${autoRenew ? 'เปิด' : 'ปิด'}การต่ออายุอัตโนมัติสำเร็จ`,
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
        data: subscription,
      };
    },
    {
      params: t.Object({ siteId: t.String(), subscriptionId: t.String() }),
      body: t.Object({
        autoRenew: t.Boolean(),
      }),
    },
  )
  // ยกเลิก subscription
  .put(
    '/sites/:siteId/subscriptions/:subscriptionId/cancel',
    async ({ params, store }: any) => {
      const { siteId, subscriptionId } = params;

      const subscription = await subscriptionService.cancelSubscription(
        siteId,
        subscriptionId,
        (store as any).user._id,
      );

      return {
        success: true,
        message: 'ยกเลิก subscription สำเร็จ',
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
        data: subscription,
      };
    },
    {
      params: t.Object({ siteId: t.String(), subscriptionId: t.String() }),
    },
  )
  // ต่ออายุ subscription แบบ manual
  .post(
    '/sites/:siteId/subscriptions/:subscriptionId/renew',
    async ({ params }) => {
      const { subscriptionId } = params;

      const subscription = await subscriptionService.renewSubscription(subscriptionId);

      return {
        success: true,
        message: 'ต่ออายุ subscription สำเร็จ',
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
        data: subscription,
      };
    },
    {
      params: t.Object({ siteId: t.String(), subscriptionId: t.String() }),
    },
  )
  // ดึงการแจ้งเตือนของ site
  .get(
    '/sites/:siteId/notifications',
    async ({ params, query, store }: any) => {
      const { siteId } = params;
      const { page, limit, unreadOnly } = query;

      const result = await subscriptionService.getNotifications(siteId, (store as any).user._id, {
        page: page ? Number(page) : undefined,
        limit: limit ? Number(limit) : undefined,
        unreadOnly: unreadOnly === 'true',
      });

      return {
        success: true,
        message: 'ดึงการแจ้งเตือนของไซต์สำเร็จ',
        statusMessage: 'สำเร็จ!',
        timestamp: new Date().toISOString(),
        data: result,
      };
    },
    {
      params: t.Object({ siteId: t.String() }),
      query: t.Object({
        page: t.Optional(t.String()),
        limit: t.Optional(t.String()),
        unreadOnly: t.Optional(t.String()),
      }),
    },
  );
