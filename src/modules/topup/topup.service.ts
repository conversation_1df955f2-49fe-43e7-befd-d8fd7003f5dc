import { logger } from '@/core/utils/logger';
import { Customer } from '@/modules/customer/customer.model';
import { User } from '@/modules/user/user.model';
import { Topup } from './topup.model';

export class TopupService {
  // สร้างคำขอเติมเงิน
  async createTopup(data: {
    targetType: 'user' | 'customer';
    targetId: string;
    pointType: 'moneyPoint' | 'goldPoint';
    amount: number;
    description?: string;
    siteId?: string;
  }): Promise<ITopup> {
    // ตรวจสอบว่า target มีอยู่จริง
    if (data.targetType === 'user') {
      const user = await User.findById(data.targetId);
      if (!user) {
        throw new Error('ไม่พบผู้ใช้ที่ระบุ');
      }
    }
    else if (data.targetType === 'customer') {
      if (!data.siteId) {
        throw new Error('กรุณาระบุ siteId สำหรับ customer');
      }
      const customer = await Customer.findOne({
        _id: data.targetId,
        siteId: data.siteId,
      });
      if (!customer) {
        throw new Error('ไม่พบสมาชิกที่ระบุ');
      }
    }
    const topup = new Topup({
      targetType: data.targetType,
      targetId: data.targetId,
      pointType: data.pointType,
      amount: data.amount,
      description: data.description || `เติม${data.pointType} จำนวน ${data.amount} แต้ม`,
      siteId: data.siteId,
      status: 'pending',
    });
    await topup.save();
    logger.info(`สร้างคำขอเติมเงิน: ${topup._id} สำหรับ ${data.targetType}:${data.targetId}`);
    return topup;
  }

  // อนุมัติและดำเนินการเติมเงิน
  async processTopup(topupId: string, action: 'approve' | 'reject', processedBy: string): Promise<ITopup> {
    const topup = await Topup.findById(topupId);
    if (!topup) {
      throw new Error('ไม่พบคำขอเติมเงิน');
    }
    if (topup.status !== 'pending') {
      throw new Error('คำขอเติมเงินนี้ได้ดำเนินการแล้ว');
    }
    if (action === 'approve') {
      await this.executeTopup(topup);
      topup.status = 'completed';
    }
    else {
      topup.status = 'cancelled';
    }
    topup.processedBy = processedBy;
    topup.processedAt = new Date();
    await topup.save();
    logger.info(`ดำเนินการเติมเงิน ${topupId}: ${action} โดย ${processedBy}`);
    return topup;
  }

  // ดำเนินการเติมเงินจริง
  private async executeTopup(topup: ITopup): Promise<void> {
    if (topup.targetType === 'user') {
      const updateField = `$inc.${topup.pointType}`;
      await User.findByIdAndUpdate(topup.targetId, { [updateField]: topup.amount });
    }
    else if (topup.targetType === 'customer') {
      const updateField = `$inc.${topup.pointType}`;
      await Customer.findOneAndUpdate({ _id: topup.targetId, siteId: topup.siteId }, { [updateField]: topup.amount });
    }
    logger.info(`เติม${topup.pointType} จำนวน ${topup.amount} ให้ ${topup.targetType}:${topup.targetId} สำเร็จ`);
  }

  // ดึงรายการเติมเงิน
  async getTopups(query: {
    targetType?: 'user' | 'customer';
    targetId?: string;
    pointType?: 'moneyPoint' | 'goldPoint';
    status?: 'pending' | 'completed' | 'failed' | 'cancelled';
    siteId?: string;
    page?: number;
    limit?: number;
  }): Promise<{ topups: ITopup[]; total: number; page: number; totalPages: number; }> {
    const page = query.page || 1;
    const limit = query.limit || 20;
    const skip = (page - 1) * limit;
    const filter: any = {};
    if (query.targetType) filter.targetType = query.targetType;
    if (query.targetId) filter.targetId = query.targetId;
    if (query.pointType) filter.pointType = query.pointType;
    if (query.status) filter.status = query.status;
    if (query.siteId) filter.siteId = query.siteId;
    const [topups, total] = await Promise.all([
      Topup.find(filter).sort({ createdAt: -1 }).skip(skip).limit(limit).lean(),
      Topup.countDocuments(filter),
    ]);
    return {
      topups: topups as ITopup[],
      total,
      page,
      totalPages: Math.ceil(total / limit),
    };
  }

  // ดึงคำขอเติมเงินตาม ID
  async getTopupById(topupId: string): Promise<ITopup | null> {
    return (await Topup.findById(topupId).lean()) as ITopup | null;
  }

  // ดึงประวัติการเติมเงินของ user/customer
  async getTopupHistory(targetType: 'user' | 'customer', targetId: string, siteId?: string): Promise<ITopup[]> {
    const filter: any = { targetType, targetId };
    if (siteId) filter.siteId = siteId;
    return (await Topup.find(filter).sort({ createdAt: -1 }).lean()) as ITopup[];
  }

  // ดึงสถิติการเติมเงิน
  async getTopupStats(siteId?: string): Promise<{
    totalTopups: number;
    pendingTopups: number;
    completedTopups: number;
    totalMoneyPoints: number;
    totalGoldPoints: number;
  }> {
    const filter: any = {};
    if (siteId) filter.siteId = siteId;
    const [stats] = await Topup.aggregate([
      { $match: filter },
      {
        $group: {
          _id: null,
          totalTopups: { $sum: 1 },
          pendingTopups: {
            $sum: { $cond: [{ $eq: ['$status', 'pending'] }, 1, 0] },
          },
          completedTopups: {
            $sum: { $cond: [{ $eq: ['$status', 'completed'] }, 1, 0] },
          },
          totalMoneyPoints: {
            $sum: {
              $cond: [
                {
                  $and: [{ $eq: ['$pointType', 'moneyPoint'] }, { $eq: ['$status', 'completed'] }],
                },
                '$amount',
                0,
              ],
            },
          },
          totalGoldPoints: {
            $sum: {
              $cond: [
                {
                  $and: [{ $eq: ['$pointType', 'goldPoint'] }, { $eq: ['$status', 'completed'] }],
                },
                '$amount',
                0,
              ],
            },
          },
        },
      },
    ]);
    return (
      stats || {
        totalTopups: 0,
        pendingTopups: 0,
        completedTopups: 0,
        totalMoneyPoints: 0,
        totalGoldPoints: 0,
      }
    );
  }
}
