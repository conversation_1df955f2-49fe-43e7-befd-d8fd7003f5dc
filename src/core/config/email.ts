import nodemailer from 'nodemailer';
import { config } from './environment';

// สร้าง transporter สำหรับส่งอีเมล
export const transporter = nodemailer.createTransport({
  host: config.emailHost,
  port: config.emailPort,
  secure: config.emailSecure,
  auth: {
    user: config.emailUser,
    pass: config.emailPassword,
  },
  debug: true,
});

// ตรวจสอบการเชื่อมต่อ
transporter.verify(error => {
  if (error) {
    console.error('Email configuration error:', error);
    console.error('Email config:', {
      host: config.emailHost,
      port: config.emailPort,
      secure: config.emailSecure,
      user: config.emailUser,
      from: config.emailFrom,
    });
  }
  else {
    console.log('Email server is ready to send messages');
  }
});

// ฟังก์ชันสำหรับส่งอีเมล
export const sendEmail = async (to: string, subject: string, html: string) => {
  const mailOptions = {
    from: config.emailFrom,
    to,
    subject,
    html,
  };

  try {
    console.log('Attempting to send email to:', to);
    const info = await transporter.sendMail(mailOptions);
    console.log('Email sent successfully:', info.messageId);
    return info;
  }
  catch (error) {
    console.error('Error sending email:', error);
    if (error instanceof Error) {
      throw Error(`ไม่สามารถส่งอีเมลได้: ${error.message}`);
    }
    throw Error('ไม่สามารถส่งอีเมลได้: เกิดข้อผิดพลาดที่ไม่ทราบสาเหตุ');
  }
};

// ฟังก์ชันสำหรับสร้าง HTML สำหรับอีเมลรีเซ็ตรหัสผ่าน
export const createResetPasswordEmail = (resetLink: string) => `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>รีเซ็ตรหัสผ่าน</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Prompt:wght@300;400;500;600&display=swap');
        
        body {
            font-family: 'Prompt', sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 20px auto;
            padding: 0;
            background-color: #ffffff;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
            border-radius: 10px 10px 0 0;
        }
        .logo {
            width: 120px;
            height: auto;
            margin-bottom: 20px;
        }
        .content {
            padding: 40px 30px;
            background-color: #ffffff;
        }
        .button {
            display: inline-block;
            background-color: #007bff; 
            border: 1px solid #007bff; 
            box-shadow: 0 4px 10px rgba(0, 123, 255, 0.25);
            color: white;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 25px;
            margin: 20px 0;
            font-weight: 500;
            transition: transform 0.2s, background-color 0.2s, border-color 0.2s;
        }
        .button:hover {
            transform: translateY(-2px);
            background-color: #0056b3;
            border-color: #0050a6;
        }
        .footer {
            text-align: center;
            padding: 20px;
            color: #666;
            font-size: 12px;
            background-color: #f8f9fa;
            border-radius: 0 0 10px 10px;
        }
        .warning {
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
            color: #856404;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }
        .social-links {
            margin-top: 20px;
        }
        .social-links a {
            color: #4A90E2;
            text-decoration: none;
            margin: 0 10px;
        }
        .divider {
            height: 1px;
            background-color: #e0e0e0;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <img src="https://placehold.co/120/45/EBF0F5/333333?text=โลโก้&font=Prompt" alt="โลโก้บริษัท" class="logo" style="border:1px solid #ddd; padding:2px;">
            <h1>รีเซ็ตรหัสผ่าน</h1>
        </div>
        <div class="content">
            <p>สวัสดีครับ/ค่ะ,</p>
            <p>เราได้รับคำขอรีเซ็ตรหัสผ่านสำหรับบัญชีของคุณ กรุณาคลิกที่ปุ่มด้านล่างเพื่อตั้งรหัสผ่านใหม่:</p>
            
            <div style="text-align: center;">
                <a href="${resetLink}" class="button">รีเซ็ตรหัสผ่าน</a>
            </div>

            <div class="warning">
                <strong>หมายเหตุ:</strong> ลิงก์นี้จะหมดอายุใน 1 ชั่วโมง เพื่อความปลอดภัยของบัญชีของคุณ
            </div>

            <p>หากคุณไม่ได้ขอรีเซ็ตรหัสผ่าน กรุณาละเลยอีเมลนี้</p>
            
            <div class="divider"></div>
            
            <p>หากมีข้อสงสัยหรือต้องการความช่วยเหลือเพิ่มเติม กรุณาติดต่อทีมสนับสนุนของเรา</p>
        </div>
        <div class="footer">
            <div class="social-links">
                <a href="#">Facebook</a> |
                <a href="#">Twitter</a> |
                <a href="#">Instagram</a>
            </div>
            <p>อีเมลนี้ถูกส่งโดยระบบอัตโนมัติ กรุณาอย่าตอบกลับอีเมลนี้</p>
            <p>&copy; ${new Date().getFullYear()} [ชื่อบริษัทของคุณที่นี่]. All rights reserved.</p>
        </div>
    </div>
</body>
</html>
`;

// ฟังก์ชันสำหรับสร้าง HTML สำหรับอีเมลยืนยัน
export const createVerificationEmail = (verificationLink: string) => `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ยืนยันอีเมล</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Prompt:wght@300;400;500;600&display=swap');
        
        body {
            font-family: 'Prompt', sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 20px auto;
            padding: 0;
            background-color: #ffffff;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
            border-radius: 10px 10px 0 0;
        }
        .logo {
            width: 120px;
            height: auto;
            margin-bottom: 20px;
        }
        .content {
            padding: 40px 30px;
            background-color: #ffffff;
        }
        .button {
            display: inline-block;
            background-color: #28a745; 
            border: 1px solid #28a745; 
            box-shadow: 0 4px 10px rgba(40, 167, 69, 0.25);
            color: white;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 25px;
            margin: 20px 0;
            font-weight: 500;
            transition: transform 0.2s, background-color 0.2s, border-color 0.2s;
        }
        .button:hover {
            transform: translateY(-2px);
            background-color: #1e7e34;
            border-color: #1c7430;
        }
        .footer {
            text-align: center;
            padding: 20px;
            color: #666;
            font-size: 12px;
            background-color: #f8f9fa;
            border-radius: 0 0 10px 10px;
        }
        .warning {
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
            color: #856404;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }
        .social-links {
            margin-top: 20px;
        }
        .social-links a {
            color: #28a745;
            text-decoration: none;
            margin: 0 10px;
        }
        .divider {
            height: 1px;
            background-color: #e0e0e0;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <img src="https://placehold.co/120/45/EBF0F5/333333?text=โลโก้&font=Prompt" alt="โลโก้บริษัท" class="logo" style="border:1px solid #ddd; padding:2px;">
            <h1>ยืนยันอีเมล</h1>
        </div>
        <div class="content">
            <p>สวัสดีครับ/ค่ะ,</p>
            <p>ขอบคุณที่ลงทะเบียนกับเรา! เพื่อให้สามารถใช้งานบัญชีของคุณได้อย่างสมบูรณ์ กรุณาคลิกที่ปุ่มด้านล่างเพื่อยืนยันอีเมลของคุณ:</p>
            
            <div style="text-align: center;">
                <a href="${verificationLink}" class="button">ยืนยันอีเมล</a>
            </div>

            <div class="warning">
                <strong>หมายเหตุ:</strong> ลิงก์นี้จะหมดอายุใน 24 ชั่วโมง
            </div>

            <p>หากคุณไม่ได้ลงทะเบียนกับเรา กรุณาละเลยอีเมลนี้</p>
            
            <div class="divider"></div>
            
            <p>หากมีข้อสงสัยหรือต้องการความช่วยเหลือเพิ่มเติม กรุณาติดต่อทีมสนับสนุนของเรา</p>
        </div>
        <div class="footer">
            <div class="social-links">
                <a href="#">Facebook</a> |
                <a href="#">Twitter</a> |
                <a href="#">Instagram</a>
            </div>
            <p>อีเมลนี้ถูกส่งโดยระบบอัตโนมัติ กรุณาอย่าตอบกลับอีเมลนี้</p>
            <p>&copy; ${new Date().getFullYear()} [ชื่อบริษัทของคุณที่นี่]. All rights reserved.</p>
        </div>
    </div>
</body>
</html>
`;
