import { logger } from '@/core/utils/logger';
import { Elysia } from 'elysia';

interface RecaptchaConfig {
  secretKey: string;
  siteKey?: string;
  minScore?: number; // สำหรับ v3 (0.0 - 1.0)
  action?: string; // สำหรับ v3
}

interface RecaptchaResponse {
  success: boolean;
  score?: number;
  action?: string;
  challenge_ts?: string;
  hostname?: string;
  'error-codes'?: string[];
}

/**
 * ✅ Standalone reCAPTCHA verification function
 */
async function verifyRecaptchaToken(token: string, config: RecaptchaConfig, remoteIP?: string): Promise<boolean> {
  if (!token) {
    logger.warn('No reCAPTCHA token provided', {
      component: 'recaptcha',
      action: 'verification',
    });
    return false;
  }

  try {
    // สร้าง form data สำหรับ Google reCAPTCHA API
    const formData = new FormData();
    formData.append('secret', config.secretKey);
    formData.append('response', token);

    if (remoteIP) {
      formData.append('remoteip', remoteIP);
    }

    // เรียก Google reCAPTCHA verification API
    const response = await fetch('https://www.google.com/recaptcha/api/siteverify', {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      logger.error('Google reCAPTCHA API error', {
        component: 'recaptcha',
        action: 'api_call',
        status: response.status,
        statusText: response.statusText,
      });
      return false;
    }

    const result: RecaptchaResponse = await response.json();

    // Log verification result
    logger.info('reCAPTCHA verification completed', {
      component: 'recaptcha',
      action: 'verification',
      success: result.success,
      score: result.score,
      recaptchaAction: result.action,
      hostname: result.hostname,
      errorCodes: result['error-codes'],
    });

    if (!result.success) {
      logger.warn('reCAPTCHA verification failed', {
        component: 'recaptcha',
        action: 'verification',
        errorCodes: result['error-codes'],
        hostname: result.hostname,
      });
      return false;
    }

    // สำหรับ v3 - ตรวจสอบ score และ action
    if (result.score !== undefined) {
      const minScore = config.minScore ?? 0.5;
      if (result.score < minScore) {
        logger.warn('reCAPTCHA score too low', {
          component: 'recaptcha',
          action: 'score_check',
          score: result.score,
          minScore,
          recaptchaAction: result.action,
        });
        return false;
      }

      if (config.action && result.action !== config.action) {
        logger.warn('reCAPTCHA action mismatch', {
          component: 'recaptcha',
          action: 'action_check',
          expected: config.action,
          received: result.action,
        });
        return false;
      }
    }

    return true;
  }
  catch (error) {
    logger.error('Error during reCAPTCHA verification', {
      component: 'recaptcha',
      action: 'verification',
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
    });
    return false;
  }
}

/**
 * ✅ Google reCAPTCHA Plugin สำหรับ Elysia
 * - รองรับ reCAPTCHA v2 และ v3
 * - ใช้เป็น middleware หรือ standalone function
 * - Consistent error handling
 */
export const recaptchaPlugin = (config: RecaptchaConfig) => {
  return new Elysia().derive(() => {
    return {
      verifyRecaptcha: async (token: string, remoteIP?: string): Promise<boolean> => {
        return await verifyRecaptchaToken(token, config, remoteIP);
      },
    };
  });
};

/**
 * ✅ reCAPTCHA Middleware - ใช้สำหรับ protect routes
 */
export const recaptchaMiddleware = (config: RecaptchaConfig) => {
  return new Elysia().derive(({ request }) => {
    return {
      verifyRecaptchaFromRequest: async (): Promise<boolean> => {
        const formData = await request.formData();
        const token = formData.get('g-recaptcha-response') as string;
        const remoteIP = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown';

        return await verifyRecaptchaToken(token, config, remoteIP);
      },
    };
  });
};

/**
 * ✅ Standalone reCAPTCHA verification function
 */
export async function verifyRecaptcha(token: string, remoteIP?: string): Promise<boolean> {
  const secretKey = Bun.env.RECAPTCHA_SECRET_KEY;

  if (!secretKey) {
    logger.error('RECAPTCHA_SECRET_KEY not configured', {
      component: 'recaptcha',
      action: 'config_check',
    });
    return false;
  }

  const config: RecaptchaConfig = {
    secretKey,
    minScore: 0.5,
    action: 'submit',
  };

  return await verifyRecaptchaToken(token, config, remoteIP);
}
