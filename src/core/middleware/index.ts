// Export checkUser with explicit naming to avoid conflicts
export {
  checkRoleSite,
  checkSiteAccess,
  checkUser,
  checkUserRole,
  getResultUser,
  isSiteAdmin,
  isSiteOwner,
  type RoleCheckOptions,
  type RoleCheckResult,
  userPlugin,
} from './checkUser';

// Export checkPermission from checkUser explicitly (preferred implementation)
export { checkPermission } from './checkUser';

// Export checkSite
export * from './checkSite';

// Export checkMember with explicit naming to avoid conflicts
export {
  checkCustomer,
  checkSiteCustomer,
  type CustomerCheckOptions,
  type CustomerCheckResult,
  customerPlugin,
  isSiteCustomerWithRole,
} from './checkMember';

// Export isSiteCustomer from checkMember explicitly (preferred implementation)
export { isSiteCustomer } from './checkMember';

// Export new unified auth system
export * from './unified-auth';

// Export services
export * from '../services/access-control.service';
export * from '../services/jwt.service';

// Export schemas
export * from '../schemas/response.schema';

// Export auth plugins (legacy)
export * from '../plugins/auth';
