import { t } from 'elysia';

// Standard API Response Schema
export const standardResponseSchema = t.Object({
  success: t.<PERSON>(),
  message: t.String(),
  statusMessage: t.String(),
  timestamp: t.String(),
  data: t.Optional(t.Any()),
});

// Error Response Schema
export const errorResponseSchema = t.Object({
  success: t.Literal(false),
  message: t.String(),
  statusMessage: t.String(),
  timestamp: t.String(),
  error: t.Optional(t.Any()),
});

// Success Response Schema
export const successResponseSchema = t.Object({
  success: t.Literal(true),
  message: t.String(),
  statusMessage: t.String(),
  timestamp: t.String(),
  data: t.Any(),
});

// Pagination Response Schema
export const paginationResponseSchema = t.Object({
  success: t.Literal(true),
  message: t.String(),
  statusMessage: t.String(),
  timestamp: t.String(),
  data: t.Object({
    items: t.Array(t.Any()),
    pagination: t.Object({
      page: t.Number(),
      limit: t.Number(),
      total: t.Number(),
      totalPages: t.Number(),
      hasNext: t.Boolean(),
      hasPrev: t.Boolean(),
    }),
  }),
});
