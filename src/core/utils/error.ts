import Elysia from 'elysia';

export class HttpError extends Error {
  public statusCode: number;
  public errorData: Record<string, unknown> | unknown[];

  constructor(statusCode: number, message: string, errorData?: Record<string, unknown> | unknown[]) {
    super(message);
    this.statusCode = statusCode;
    this.errorData = errorData;
  }
}

export const httpErrorDecorator = new Elysia({
  name: 'elysia-http-error-decorator',
}).decorate('HttpError', HttpError);

interface HttpErrorConstructor {
  customFormatter?: (error: HttpError) => Record<string, unknown> | string;
  returnStringOnly?: boolean;
}

export const httpError = (
  params: HttpErrorConstructor = {
    customFormatter: undefined,
    returnStringOnly: false,
  },
) =>
  new Elysia({ name: 'elysia-http-error' })
    .error({
      ELYSIA_HTTP_ERROR: HttpError,
    })
    .onError({ as: 'global' }, ({ code, error, set }) => {
      if (code === 'ELYSIA_HTTP_ERROR') {
        set.status = error.statusCode;
        if (params.customFormatter) {
          return params.customFormatter(error);
        }
        if (params.returnStringOnly) {
          return error.message;
        }
        return {
          error: true,
          code: error.statusCode,
          message: error.message,
          data: error.errorData,
        };
      }
    });
