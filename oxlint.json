{"$schema": "https://raw.githubusercontent.com/oxc-project/oxc/main/crates/oxc_linter/src/schemas/oxlint.json", "rules": {"typescript": {"no-unused-vars": "off", "no-explicit-any": "off", "no-non-null-assertion": "warn"}, "eslint": {"prefer-const": "error", "no-unused-vars": "off", "no-undef": "off", "no-const-assign": "error", "no-var": "error", "no-duplicate-imports": "warn", "no-unreachable": "warn", "no-empty": "warn", "no-console": "off"}, "import": {"no-duplicates": "warn"}}, "env": {"node": true, "es2022": true}, "globals": {"console": "readonly", "process": "readonly", "Buffer": "readonly", "__dirname": "readonly", "__filename": "readonly", "global": "readonly", "setTimeout": "readonly", "setInterval": "readonly", "clearTimeout": "readonly", "clearInterval": "readonly"}, "ignore_patterns": ["node_modules/**", "build/**", "dist/**", "coverage/**", "logs/**", "public/uploads/**", "dashboard-sveltekit/**", "*.config.js", "bun.lockb"], "plugins": []}