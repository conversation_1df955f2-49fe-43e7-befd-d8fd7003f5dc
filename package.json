{"name": "backend-elysia", "version": "1.0.50", "type": "module", "scripts": {"test": "bun test", "build": "bun build --compile --minify --target bun --outfile server ./src/index.ts --external sharp --external canvas --external bcrypt", "start": "bun run src/index.ts", "dev": "bun run --watch src/index.ts", "format": "dprint fmt", "format:check": "dprint check", "lint": "oxlint --fix", "lint:check": "ox<PERSON>", "check": "npm run lint && npm run format", "lint:fix": "oxlint --fix && dprint fmt", "clean:unused": "node scripts/remove-unused.js fix", "clean:imports": "node scripts/remove-unused.js imports", "clean:report": "node scripts/remove-unused.js report", "ts:analyze": "bun run scripts/ts-unused-cleaner.ts analyze", "ts:report": "bun run scripts/ts-unused-cleaner.ts report", "clean:simple": "node scripts/safe-unused-cleaner.js", "clean:imports-only": "node scripts/simple-unused-cleaner.js imports", "quick-clean": "./scripts/quick-clean.sh", "quick-clean:imports": "./scripts/quick-clean.sh imports", "quick-clean:check": "./scripts/quick-clean.sh check", "clean:effective": "node scripts/effective-unused-cleaner.js all", "clean:effective-imports": "node scripts/effective-unused-cleaner.js imports", "status": "node scripts/cleanup-status.js quick", "status:full": "node scripts/cleanup-status.js full", "clean:aggressive": "node scripts/aggressive-unused-cleaner.js", "clean:safe": "node scripts/safe-unused-cleaner.js"}, "dependencies": {"@elysiajs/bearer": "^1.3.0", "@elysiajs/cors": "^1.3.3", "@elysiajs/jwt": "^1.3.2", "@elysiajs/server-timing": "^1.3.0", "@elysiajs/static": "^1.3.0", "@tqman/nice-logger": "^1.1.1", "cloudinary": "^2.7.0", "elysia": "^1.3.8", "elysia-fault": "^1.0.7", "elysia-helmet": "^3.0.0", "elysia-rate-limit": "^4.4.0", "fastest-levenshtein": "^1.0.16", "glob": "^11.0.3", "ioredis": "^5.7.0", "jose": "^6.0.12", "logestic": "^1.2.4", "logixlysia": "^5.1.0", "mongoose": "^8.17.0", "nanoid": "^5.1.5", "node-cron": "^4.2.1", "nodemailer": "^7.0.5", "sharp": "^0.34.3"}, "devDependencies": {"@types/nodemailer": "^6.4.17", "bun-types": "^1.2.19", "chalk": "^5.4.1", "dprint": "^0.50.1", "oxlint": "^1.9.0"}, "module": "src/index.js"}