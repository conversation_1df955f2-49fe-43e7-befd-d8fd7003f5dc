# Authentication & Refresh Token Troubleshooting Guide

## ปัญหาที่พบและการแก้ไข

### 1. ระบบ Refresh Token ไม่ทำงาน

#### ปัญหา:

- Token หมดอายุแล้วไม่ refresh อัตโนมัติ
- User ต้องล็อกอินใหม่บ่อยเกินไป
- Cross-tab sync ไม่ทำงาน

#### สาเหตุ:

- Rate limiting เข้มเกินไป (10 นาที -> ลดเหลือ 2-5 นาที)
- ไม่ได้ clear cookies เมื่อ token หมดอายุ
- ไม่ได้ตรวจสอบ refresh token จาก cookies

#### การแก้ไข:

```typescript
// 1. ลด rate limiting ใน auth store
const MIN_TIME_BEFORE_REFRESH = 5 * 60 * 1000; // 5 นาที
const MIN_TIME_BEFORE_MANUAL_REFRESH = 2 * 60 * 1000; // 2 นาที

// 2. ตรวจสอบ refresh token จาก cookies
const refreshToken = this._refreshToken || this.getRefreshTokenFromCookies();

// 3. Clear auth state เมื่อ refresh ไม่สำเร็จ
if (!result.success) {
  this.clearAuthState();
  this.broadcastAuthEvent('signout');
}
```

### 2. Hooks Server ไม่ Clear Cookies

#### ปัญหา:

- Invalid tokens ยังคงอยู่ใน cookies
- ทำให้เกิด infinite loop ของการ refresh

#### การแก้ไข:

```typescript
// Clear cookies เมื่อ token invalid
event.cookies.delete('auth_token', { path: '/' });
event.cookies.delete('refreshToken', { path: '/' });
event.cookies.delete('session_id', { path: '/' });
```

### 3. API Client ยังใช้ localStorage

#### ปัญหา:

- API client ยังคิดว่า token อยู่ใน localStorage
- แต่ระบบเปลี่ยนไปใช้ cookies แล้ว

#### การแก้ไข:

```typescript
// เปลี่ยนจาก localStorage เป็น cookies
export function getToken(): string | null {
  if (typeof window !== 'undefined') {
    const cookies = document.cookie.split(';').reduce((acc, cookie) => {
      const [key, value] = cookie.trim().split('=');
      acc[key] = value;
      return acc;
    }, {} as Record<string, string>);

    return cookies['auth_token'] || null;
  }
  return null;
}
```

## การทดสอบระบบ

### 1. หน้าทดสอบ Authentication

เข้าไปที่ `/dashboard/test-auth` เพื่อทดสอบ:

- **Token Health Check**: ตรวจสอบสุขภาพ token
- **Manual Refresh**: บังคับ refresh token
- **Smart Refresh**: ใช้ระบบ smart refresh
- **Get Current User**: ดึงข้อมูล user จาก API
- **Refresh User Data**: รีเฟรชข้อมูล user

### 2. การตรวจสอบ Console Logs

```javascript
// ดู logs ใน browser console
console.log('AuthStore: Token refreshed successfully');
console.log('AuthStore: Token refresh failed');

// ดู logs ใน server console
console.log('Protected Layout Server: Authentication successful');
console.log('Hooks: Token refreshed successfully');
```

### 3. การตรวจสอบ Cookies

```javascript
// ตรวจสอบ cookies ใน browser
document.cookie.split(';').forEach(cookie => {
  console.log(cookie.trim());
});

// ควรเห็น:
// auth_token=...
// refreshToken=...
// session_id=...
```

## Best Practices

### 1. Token Management

- ใช้ httpOnly cookies สำหรับ security
- ตั้ง expiry time ที่เหมาะสม
- Rotate refresh tokens เป็นระยะ

### 2. Error Handling

- Clear invalid tokens ทันที
- แสดง error message ที่เข้าใจง่าย
- Log security events สำหรับ monitoring

### 3. User Experience

- Auto-refresh ใน background
- Cross-tab synchronization
- Graceful fallback เมื่อ offline

## การ Monitor และ Debug

### 1. Server Logs

```bash
# ดู logs ใน production
tail -f logs/auth.log | grep "token_refresh"
```

### 2. Client Monitoring

```javascript
// เพิ่ม monitoring ใน production
window.addEventListener('unhandledrejection', event => {
  if (event.reason?.message?.includes('token')) {
    console.error('Token error:', event.reason);
    // Send to monitoring service
  }
});
```

### 3. Performance Monitoring

```javascript
// วัดเวลาการ refresh token
const start = performance.now();
await authStore.refreshToken();
const duration = performance.now() - start;
console.log(`Token refresh took ${duration}ms`);
```

## Common Issues และ Solutions

### Issue: "Token refresh rate limited"

**Solution**: ลด `MIN_TIME_BEFORE_REFRESH` หรือใช้ `smartRefresh()`

### Issue: "No refresh token available"

**Solution**: ตรวจสอบ cookies และ clear invalid tokens

### Issue: "Cross-tab sync not working"

**Solution**: ตรวจสอบ BroadcastChannel และ localStorage events

### Issue: "Infinite refresh loop"

**Solution**: เพิ่ม rate limiting และ clear invalid tokens

## Security Considerations

1. **HttpOnly Cookies**: ป้องกัน XSS attacks
2. **Secure Flag**: ใช้ HTTPS ใน production
3. **SameSite**: ป้องกัน CSRF attacks
4. **Token Rotation**: เปลี่ยน refresh token เป็นระยะ
5. **Rate Limiting**: ป้องกัน brute force attacks

## Performance Optimization

1. **Token Caching**: Cache user data ใน memory
2. **Smart Refresh**: Refresh เฉพาะเมื่อจำเป็น
3. **Background Refresh**: Refresh ใน background
4. **Batch Requests**: รวม API calls เมื่อเป็นไปได้
