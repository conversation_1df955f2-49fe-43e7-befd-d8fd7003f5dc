import adapter from '@sveltejs/adapter-node';

/** @type {import('@sveltejs/kit').Config} */
const config = {
  kit: {
    adapter: adapter(),
  },
  compilerOptions: {
    runes: true,
  },
  // // ปรับแต่งพฤติกรรมการ prefetch
  // prerender: {
  //   // ควบคุมการ prerender หน้าเว็บ (สร้างไฟล์ static ล่วงหน้า)
  //   enabled: true, // เปิดใช้งาน prerender (default: true)
  //   entries: ['*'], // '*' หมายถึง prerender ทุกหน้า หรือระบุหน้าเฉพาะเช่น ['/about', '/contact']
  // },
  // csp: {
  //   mode: 'auto', // Content Security Policy เพื่อความปลอดภัย
  // },
  // // ปรับแต่งการโหลดข้อมูลทั่วไป
  // modulePreloading: {
  //   enabled: false, // ปิดการโหลดโมดูลล่วงหน้า (ถ้าต้องการลดการโหลดทรัพยากร)
  // },
  // // ปิด prefetch ทั่วทั้งโปรเจกต์ (ถ้าต้องการ)
  // prefetch: false, // ปิด prefetch สำหรับทุกหน้า (default: true)
};

export default config;
