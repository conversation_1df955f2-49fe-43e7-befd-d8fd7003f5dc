{"$schema": "https://raw.githubusercontent.com/oxc-project/oxc/main/crates/oxc_linter/src/schemas/oxlint.json", "rules": {"typescript": {"no-unused-vars": "warn", "no-explicit-any": "off", "no-non-null-assertion": "warn"}, "eslint": {"prefer-const": "warn", "no-const-assign": "warn", "no-undef": "warn", "no-unused-vars": "off", "no-var": "error", "no-duplicate-imports": "warn", "no-unreachable": "warn", "no-empty": "warn", "no-console": "off"}, "import": {"no-duplicates": "warn"}}, "env": {"browser": true, "es2022": true, "node": true}, "globals": {"console": "readonly", "window": "readonly", "document": "readonly", "Response": "readonly", "Request": "readonly", "fetch": "readonly", "URL": "readonly", "URLSearchParams": "readonly", "$$Generic": "readonly", "$$Props": "readonly", "$$Events": "readonly", "$$Slots": "readonly"}, "ignore_patterns": ["build/**", ".svelte-kit/**", "dist/**", "node_modules/**", "static/**", "coverage/**", "**/*.config.js", "**/*.config.ts"], "plugins": []}