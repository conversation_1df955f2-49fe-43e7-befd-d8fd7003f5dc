# 🔄 Hybrid Approach Guide - SvelteKit Route API + Service Pattern

## 📋 สรุปการปรับปรุง

เราได้ปรับปรุงโปรเจ็คจาก **Service Pattern** เป็น **Hybrid Approach** ที่รวม:

- ✅ **SvelteKit Route API** - สำหรับ form handling และ validation
- ✅ **Service Pattern** - สำหรับ business logic และ API calls
- ✅ **Native Fetch** - แทน ofetch เพื่อลด dependency

## 🏗️ Architecture Overview

```
Client Form → use:enhance → Route API → Service Class → Backend API
```

### ข้อดีของ Hybrid Approach:

1. **SvelteKit Native** - ใช้ `use:enhance`, `fail()`, progressive enhancement
2. **Reusable Logic** - Service classes สามารถใช้ซ้ำได้
3. **Type Safety** - TypeScript support เต็มรูปแบบ
4. **Error Handling** - Consistent error handling pattern
5. **Testing** - ง่ายต่อการ test แยกส่วน

## 📁 File Structure

```
src/
├── routes/
│   ├── (public)/
│   │   └── signin/
│   │       ├── +page.server.ts    # Route API + Service
│   │       └── +page.svelte       # Client + use:enhance
│   └── (protected)/
│       └── dashboard/
│           └── profile/
│               ├── +page.server.ts # Route API + Service
│               └── +page.svelte    # Client + use:enhance
├── lib/
│   ├── services/
│   │   ├── auth.ts                # Auth Service
│   │   ├── user.ts                # User Service
│   │   └── base.ts                # Base Service
│   └── api/
│       └── client.ts              # API Client (Native Fetch)
```

## 🔧 Implementation Examples

### 1. Route API (Server-side)

```typescript
// +page.server.ts
import { userService } from '$lib/services/user';
import { fail } from '@sveltejs/kit';
import type { Actions } from './$types';

export const actions: Actions = {
  updateProfile: async ({ request, locals }) => {
    try {
      const data = await request.formData();

      // Route-level validation
      const profileData = {
        firstName: data.get('firstName')?.toString()?.trim(),
        lastName: data.get('lastName')?.toString()?.trim(),
      };

      if (!profileData.firstName && !profileData.lastName) {
        return fail(400, {
          message: 'กรุณากรอกข้อมูลอย่างน้อย 1 ฟิลด์',
          type: 'profile',
        });
      }

      // Call service for business logic
      const result = await userService.updateProfile(
        profileData,
        locals.token!,
      );

      if (!result.success) {
        return fail(400, {
          message: result.error,
          type: 'profile',
        });
      }

      return {
        success: true,
        user: result.data,
        message: 'อัปเดตโปรไฟล์สำเร็จ',
        type: 'profile',
      };
    }
    catch (error) {
      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการอัปเดตโปรไฟล์',
        type: 'profile',
      });
    }
  },
};
```

### 2. Service Class (Business Logic)

```typescript
// services/user.ts
import { BaseService } from './base';

export class UserService extends BaseService {
  async updateProfile(
    data: UpdateProfileData,
    token: string,
  ): Promise<ApiResponse<User>> {
    // Service-level validation
    const validationError = validateUpdateProfileData(data);
    if (validationError) {
      return { success: false, error: validationError };
    }

    // Business logic + API call
    return this.handleRequest(async () => {
      return this.makeAuthenticatedRequest<{ data: User; }>(
        '/user/profile',
        token,
        {
          method: 'PUT',
          body: data,
        },
      );
    }, 'อัปเดตโปรไฟล์สำเร็จ');
  }
}
```

### 3. Client Component (use:enhance)

```svelte
<!-- +page.svelte -->
<script lang="ts">
	import { enhance } from '$app/forms';

	let isLoading = $state(false);

	function handleFormResult(result: any) {
		isLoading = false;

		if (result.type === 'success') {
			// Handle success
			showSuccess(result.data?.message);
		} else if (result.type === 'failure') {
			// Handle error
			showError(result.data?.message);
		}
	}
</script>

<form
	method="POST"
	action="?/updateProfile"
	use:enhance={() => {
		isLoading = true;
		return async ({ result }) => {
			handleFormResult(result);
		};
	}}
>
	<input name="firstName" bind:value={firstName} />
	<button type="submit" disabled={isLoading}>
		{isLoading ? 'กำลังบันทึก...' : 'บันทึก'}
	</button>
</form>
```

## 🎯 Best Practices

### 1. **Validation Strategy**

- **Route Level**: Basic validation (required fields, format)
- **Service Level**: Business logic validation
- **Client Level**: UX validation (real-time feedback)

### 2. **Error Handling**

```typescript
// Route API - ใช้ fail() สำหรับ SvelteKit
return fail(400, { message: 'Error message', type: 'field_name' });

// Service - ใช้ ApiResponse pattern
return { success: false, error: 'Error message' };

// Client - ใช้ use:enhance result
if (result.type === 'failure') {
  showError(result.data?.message);
}
```

### 3. **Type Safety**

```typescript
// Define clear interfaces
interface UpdateProfileData {
  firstName?: string;
  lastName?: string;
}

// Use typed responses
Promise<ApiResponse<User>>;
```

### 4. **Progressive Enhancement**

- Form ทำงานได้แม้ไม่มี JavaScript
- `use:enhance` เพิ่ม UX เมื่อมี JavaScript
- Loading states และ error handling

## 🧪 Testing Strategy

### 1. **Service Testing**

```typescript
// Easy to test business logic
const result = await userService.updateProfile(mockData, mockToken);
expect(result.success).toBe(true);
```

### 2. **Route API Testing**

```typescript
// Test SvelteKit actions
const result = await actions.updateProfile({
  request: mockRequest,
  locals: mockLocals,
});
```

### 3. **Integration Testing**

- Test complete flow: Client → Route → Service → API

## 📊 Performance Benefits

1. **Reduced Bundle Size** - ไม่ต้อง ship service classes ไป client
2. **Better Caching** - SvelteKit handle caching automatically
3. **SSR Support** - Form ทำงานได้ทั้ง server และ client
4. **Progressive Enhancement** - ทำงานได้แม้ไม่มี JavaScript

## 🔄 Migration Checklist

- [x] ✅ เปลี่ยนจาก ofetch เป็น native fetch
- [x] ✅ ปรับปรุง API client ให้ใช้ class-based pattern
- [x] ✅ อัปเดต BaseService ให้รองรับ API client ใหม่
- [x] ✅ ปรับปรุง signin route ให้ใช้ fail() แทน ResponseHelper
- [x] ✅ ปรับปรุง profile route ให้ใช้ Hybrid Approach
- [x] ✅ เพิ่ม validation ใน route level
- [x] ✅ ปรับปรุง error handling ให้สอดคล้องกัน
- [x] ✅ ทดสอบ use:enhance ใน client components

## 🚀 Next Steps

1. **ขยายไปยัง routes อื่นๆ** - categories, products, orders
2. **เพิ่ม middleware** - rate limiting, validation
3. **ปรับปรุง error handling** - centralized error management
4. **เพิ่ม testing** - unit tests และ integration tests
5. **Performance optimization** - caching, lazy loading

## 📚 Resources

- [SvelteKit Form Actions](https://kit.svelte.dev/docs/form-actions)
- [Progressive Enhancement](https://kit.svelte.dev/docs/form-actions#progressive-enhancement)
- [TypeScript Support](https://kit.svelte.dev/docs/types)
- [Error Handling](https://kit.svelte.dev/docs/errors)

---

**สรุป**: Hybrid Approach ให้เราได้ประโยชน์จากทั้ง SvelteKit native features และ Service pattern ที่มีอยู่แล้ว ทำให้โค้ดมี maintainability ดี type safety สูง และ user experience ที่ดีขึ้น
