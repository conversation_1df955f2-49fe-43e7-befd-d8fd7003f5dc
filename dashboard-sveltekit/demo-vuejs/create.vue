<script setup lang="ts">
import { computed, reactive, ref } from 'vue';
import { useRoute } from 'vue-router';
import { useVuelidate } from '@vuelidate/core';
import { required, minLength, maxLength, minValue, helpers } from '@vuelidate/validators';

definePageMeta({
  layout: 'dashboard',
  middleware: ['auth'],
});

// Get siteId from route params
const route = useRoute();
const siteId = computed(() => route.params.siteId as string);

// Form data based on productSchema
const form = reactive({
  // Basic info
  name: '',
  type: 'physical' as 'physical' | 'digital' | 'service' | 'subscription',
  saleChannel: 'both' as 'online' | 'offline' | 'both',

  // Pricing
  price: 0,
  compareAtPrice: undefined as number | undefined,
  costPrice: undefined as number | undefined,

  // Inventory
  stock: undefined as number | undefined,
  trackStock: true,
  allowBackorder: false,

  // Category and description
  categoryId: '',
  description: '',
  shortDescription: '',

  // Tags and images
  tags: [] as string[],
  images: [] as Array<{
    url: string;
    alt?: string;
    position?: number;
    isMain?: boolean;
  }>,

  // Variants - Updated to use option sets
  hasVariants: false,
  selectedOptionSets: [] as string[], // IDs of selected option sets
  variants: [] as Array<{
    name: string;
    sku?: string;
    price?: number;
    stock?: number;
    attributes: Record<string, string>;
  }>,

  // Digital assets (for digital products)
  digitalAssets: [] as Array<{
    name: string;
    url: string;
    type: string;
    size?: number;
  }>,

  // Shipping
  shipping: {
    weight: undefined as number | undefined,
    dimensions: {
      length: undefined as number | undefined,
      width: undefined as number | undefined,
      height: undefined as number | undefined,
    },
    requiresShipping: true,
    shippingClass: '',
  },

  // SEO
  seoTitle: '',
  seoDescription: '',

  // Status
  featured: false,
  isActive: true,

  // Custom fields
  customFields: {} as Record<string, any>,
});

// Validation rules based on productSchema
const rules = computed(() => ({
  name: {
    required: helpers.withMessage('ชื่อสินค้าต้องไม่ว่าง', required),
    minLength: helpers.withMessage('ชื่อสินค้าต้องมีอย่างน้อย 1 ตัวอักษร', minLength(1)),
    maxLength: helpers.withMessage('ชื่อสินค้าต้องไม่เกิน 200 ตัวอักษร', maxLength(200)),
  },
  type: {
    required: helpers.withMessage('ต้องเลือกประเภทสินค้า', required),
  },
  saleChannel: {
    required: helpers.withMessage('ต้องเลือกช่องทางการขาย', required),
  },
  price: {
    required: helpers.withMessage('ราคาสินค้าต้องไม่ว่าง', required),
    minValue: helpers.withMessage('ราคาสินค้าต้องไม่ติดลบ', minValue(0)),
  },
  compareAtPrice: {
    minValue: helpers.withMessage('ราคาปกติต้องไม่ติดลบ', minValue(0)),
  },
  costPrice: {
    minValue: helpers.withMessage('ราคาทุนต้องไม่ติดลบ', minValue(0)),
  },
  stock: {
    minValue: helpers.withMessage('จำนวนสต็อกต้องไม่ติดลบ', minValue(0)),
  },
  description: {
    maxLength: helpers.withMessage('รายละเอียดสินค้าต้องไม่เกิน 5000 ตัวอักษร', maxLength(5000)),
  },
  shortDescription: {
    maxLength: helpers.withMessage('รายละเอียดสั้นต้องไม่เกิน 500 ตัวอักษร', maxLength(500)),
  },
  seoTitle: {
    maxLength: helpers.withMessage('SEO title ต้องไม่เกิน 60 ตัวอักษร', maxLength(60)),
  },
  seoDescription: {
    maxLength: helpers.withMessage('SEO description ต้องไม่เกิน 160 ตัวอักษร', maxLength(160)),
  },
  'shipping.weight': {
    minValue: helpers.withMessage('น้ำหนักต้องไม่ติดลบ', minValue(0)),
  },
  'shipping.dimensions.length': {
    minValue: helpers.withMessage('ความยาวต้องไม่ติดลบ', minValue(0)),
  },
  'shipping.dimensions.width': {
    minValue: helpers.withMessage('ความกว้างต้องไม่ติดลบ', minValue(0)),
  },
  'shipping.dimensions.height': {
    minValue: helpers.withMessage('ความสูงต้องไม่ติดลบ', minValue(0)),
  },
}));

// Initialize Vuelidate
const v$ = useVuelidate(rules, form);

const loading = ref(false);

// Categories - Updated to use hierarchical structure
const categories = ref([
  { id: '', label: 'ไม่เลือกหมวดหมู่' },
  { id: '1', label: 'อิเล็กทรอนิกส์' },
  { id: '2', label: '├─ โทรศัพท์มือถือ' },
  { id: '3', label: '├─ คอมพิวเตอร์' },
  { id: '4', label: 'เสื้อผ้า' },
  { id: '5', label: '├─ เสื้อผ้าผู้ชาย' },
  { id: '6', label: '├─ เสื้อผ้าผู้หญิง' },
  { id: '7', label: 'หนังสือ' },
  { id: '8', label: 'บ้านและสวน' },
  { id: '9', label: 'กีฬา' },
]);

// Product type options
const productTypes = ref([
  { value: 'physical', label: 'สินค้าจริง' },
  { value: 'digital', label: 'สินค้าดิจิทัล' },
  { value: 'service', label: 'บริการ' },
  { value: 'subscription', label: 'สมาชิก' },
]);

// Sale channel options
const saleChannels = ref([
  { value: 'online', label: 'ออนไลน์' },
  { value: 'offline', label: 'ออฟไลน์' },
  { value: 'both', label: 'ทั้งสองช่องทาง' },
]);

// Status options
const statuses = ref([
  { value: true, label: 'เปิดขาย' },
  { value: false, label: 'ปิดขาย' },
]);

// Mock option sets data - in real app, fetch from API
const availableOptionSets = ref([
  {
    id: '1',
    name: 'ขนาดเสื้อผ้า',
    type: 'single',
    options: [
      { value: 'xs', label: 'XS' },
      { value: 's', label: 'S' },
      { value: 'm', label: 'M' },
      { value: 'l', label: 'L' },
      { value: 'xl', label: 'XL' },
      { value: 'xxl', label: 'XXL' },
    ],
  },
  {
    id: '2',
    name: 'สีสินค้า',
    type: 'color',
    options: [
      { value: 'red', label: 'แดง', color: '#FF0000' },
      { value: 'blue', label: 'น้ำเงิน', color: '#0000FF' },
      { value: 'green', label: 'เขียว', color: '#00FF00' },
      { value: 'black', label: 'ดำ', color: '#000000' },
      { value: 'white', label: 'ขาว', color: '#FFFFFF' },
    ],
  },
  {
    id: '3',
    name: 'วัสดุ',
    type: 'single',
    options: [
      { value: 'cotton', label: 'ผ้าฝ้าย' },
      { value: 'polyester', label: 'โพลีเอสเตอร์' },
      { value: 'silk', label: 'ผ้าไหม' },
      { value: 'wool', label: 'ผ้าขนสัตว์' },
    ],
  },
]);

// Get selected option sets
const selectedOptionSetsData = computed(() => {
  return form.selectedOptionSets.map(id =>
    availableOptionSets.value.find(set => set.id === id)
  ).filter(Boolean);
});

// Add option set
const addOptionSet = (optionSetId: string) => {
  if (!form.selectedOptionSets.includes(optionSetId)) {
    form.selectedOptionSets.push(optionSetId);
    generateVariants();
  }
};

// Remove option set
const removeOptionSet = (optionSetId: string) => {
  form.selectedOptionSets = form.selectedOptionSets.filter(id => id !== optionSetId);
  generateVariants();
};

// Generate variants based on selected option sets
const generateVariants = () => {
  if (form.selectedOptionSets.length === 0) {
    form.variants = [];
    return;
  }

  const optionSets = selectedOptionSetsData.value;
  if (optionSets.length === 0) return;

  // Generate all combinations
  const combinations = generateCombinations(optionSets);

  form.variants = combinations.map((combination, index) => {
    const name = combination.map(opt => opt.label).join(' / ');
    const attributes: Record<string, string> = {};

    combination.forEach((opt, idx) => {
      const optionSet = optionSets[idx];
      if (optionSet) {
        attributes[optionSet.name] = opt.value;
      }
    });

    return {
      name,
      sku: `${form.name ? form.name.replace(/\s+/g, '-').toLowerCase() : 'product'}-${combination.map(opt => opt.value).join('-')}`,
      price: form.price,
      stock: 0,
      attributes,
    };
  });
};

// Helper function to generate combinations
const generateCombinations = (optionSets: any[]): any[] => {
  if (optionSets.length === 0) return [];
  if (optionSets.length === 1) return optionSets[0].options.map((opt: any) => [opt]);

  const [first, ...rest] = optionSets;
  const restCombinations = generateCombinations(rest);

  const combinations: any[] = [];
  for (const option of first.options) {
    for (const restCombination of restCombinations) {
      combinations.push([option, ...restCombination]);
    }
  }

  return combinations;
};

// Handle form submission
const handleSubmit = async () => {
  // Validate form using Vuelidate
  const isValid = await v$.value.$validate();
  if (!isValid) {
    useToast().add({
      title: 'ข้อผิดพลาด',
      description: 'กรุณาตรวจสอบข้อมูลที่กรอก',
      color: 'error',
    });
    return;
  }

  loading.value = true;
  try {
    // แปลงข้อมูลให้ตรงกับ productSchema
    const productData = {
      name: form.name,
      type: form.type,
      saleChannel: form.saleChannel,
      price: form.price,
      compareAtPrice: form.compareAtPrice,
      costPrice: form.costPrice,
      stock: form.stock,
      trackStock: form.trackStock,
      allowBackorder: form.allowBackorder,
      categoryId: form.categoryId,
      description: form.description,
      shortDescription: form.shortDescription,
      tags: form.tags,
      images: form.images,
      hasVariants: form.hasVariants,
      variants: form.variants,
      selectedOptionSets: form.selectedOptionSets,
      digitalAssets: form.digitalAssets,
      shipping: form.shipping,
      seoTitle: form.seoTitle,
      seoDescription: form.seoDescription,
      featured: form.featured,
      isActive: form.isActive,
      customFields: form.customFields,
    };

    // เรียก API สร้างสินค้า (mock)
    console.log('Creating product:', productData);

    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));

    useToast().add({
      title: 'สำเร็จ',
      description: 'เพิ่มสินค้าเรียบร้อยแล้ว',
      color: 'success',
    });

    // Navigate back to products page
    await navigateTo(`/dashboard/${siteId.value}/products`);
  } catch (error: any) {
    console.error('Error creating product:', error);
    useToast().add({
      title: 'ข้อผิดพลาด',
      description: error.message || 'ไม่สามารถเพิ่มสินค้าได้',
      color: 'error',
    });
  } finally {
    loading.value = false;
  }
};

// Handle cancel
const handleCancel = () => {
  navigateTo(`/dashboard/${siteId.value}/products`);
};

// Add tag
const addTag = (tag: string) => {
  if (tag && !form.tags.includes(tag)) {
    form.tags.push(tag);
  }
};

// Remove tag
const removeTag = (tag: string) => {
  form.tags = form.tags.filter(t => t !== tag);
};

// Calculate profit margin
const profitMargin = computed(() => {
  if (form.price > 0 && form.costPrice && form.costPrice > 0) {
    return (((form.price - form.costPrice) / form.price) * 100).toFixed(2);
  }
  return '0';
});

// Calculate discount percentage
const discountPercentage = computed(() => {
  if (form.compareAtPrice && form.compareAtPrice > 0 && form.price > 0) {
    return (
      ((form.compareAtPrice - form.price) / form.compareAtPrice) *
      100
    ).toFixed(2);
  }
  return '0';
});

// Add variant manually
const addVariant = () => {
  form.variants.push({
    name: '',
    sku: '',
    price: form.price,
    stock: 0,
    attributes: {},
  });
};

// Remove variant
const removeVariant = (index: number) => {
  form.variants.splice(index, 1);
};

// Navigate to options management
const goToOptionsManagement = () => {
  navigateTo(`/dashboard/${siteId.value}/options`);
};

const tagInput = ref<any>(null);

function handleAddTag() {
  const inputEl = tagInput.value?.input as HTMLInputElement | undefined;
  if (inputEl && inputEl.value) {
    addTag(inputEl.value);
    inputEl.value = '';
  }
}

// Quick Actions modal state
const showUploadModal = ref(false);
const showCopyModal = ref(false);
const showStatsModal = ref(false);
const showAddCategoryModal = ref(false);
const newCategoryInput = ref('');

function handleQuickAction(action: string) {
  if (action === 'upload') showUploadModal.value = true;
  else if (action === 'copy') showCopyModal.value = true;
  else if (action === 'stats') showStatsModal.value = true;
}

function handleAddCategory() {
  if (newCategoryInput.value && !categories.value.includes(newCategoryInput.value)) {
    categories.value.push(newCategoryInput.value);
    form.categoryId = newCategoryInput.value;
    newCategoryInput.value = '';
    showAddCategoryModal.value = false;
  }
}

// Add digital asset
const addDigitalAsset = () => {
  form.digitalAssets.push({
    name: '',
    url: '',
    type: '',
    size: 0,
  });
};

// Remove digital asset
const removeDigitalAsset = (index: number) => {
  form.digitalAssets.splice(index, 1);
};

const uploadedImage = ref<string | null>(null);
function onUploadImage(e: Event) {
  const file = (e.target as HTMLInputElement).files?.[0];
  if (file) {
    const reader = new FileReader();
    reader.onload = ev => {
      uploadedImage.value = ev.target?.result as string;
    };
    reader.readAsDataURL(file);
  }
}
function confirmUploadImage() {
  // mock: เพิ่ม url ไปใน images array
  if (uploadedImage.value) {
    form.images.push({
      url: uploadedImage.value,
      alt: '',
      position: form.images.length,
      isMain: form.images.length === 0,
    });
    showUploadModal.value = false;
    uploadedImage.value = null;
  }
}

// สำหรับคัดลอกสินค้า
const copySearch = ref('');
const copyCandidates = computed(() => {
  // mock: filter จาก products ที่มีในระบบ (ควรดึงจาก API จริง)
  return [
    { _id: '1', name: 'สินค้า A', description: '...', price: 100 },
    { _id: '2', name: 'สินค้า B', description: '...', price: 200 },
  ].filter(p => p.name.includes(copySearch.value));
});
function copyProduct(item: any) {
  form.name = item.name;
  form.description = item.description;
  form.price = item.price;
  showCopyModal.value = false;
}

// mock สถิติ
const mockStats = reactive({
  sales: 12,
  revenue: 3500,
  views: 120,
  rating: 4.7,
});
</script>

<template>
  <div class="space-y-8">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <div>
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          เพิ่มสินค้าใหม่
        </h1>
        <p class="text-gray-600 dark:text-gray-400">
          เพิ่มสินค้าใหม่เข้าสู่ร้านค้า
        </p>
      </div>
      <div class="flex items-center gap-3">
        <UButton @click="handleCancel" variant="outline" size="sm" class="hover:bg-gray-100 dark:hover:bg-gray-800">
          <Icon name="i-heroicons-arrow-left" class="w-4 h-4 mr-2" />
          ย้อนกลับ
        </UButton>
        <UButton @click="handleSubmit" :loading="loading"
          class="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300">
          <Icon name="i-heroicons-check" class="w-5 h-5 mr-2" />
          เพิ่มสินค้า
        </UButton>
      </div>
    </div>

    <!-- Form -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <!-- Main Form -->
      <div class="lg:col-span-2 space-y-6">
        <!-- Basic Information -->
        <UCard class="overflow-hidden">
          <template #header>
            <div class="flex items-center gap-3">
              <div class="p-2 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg">
                <Icon name="i-heroicons-information-circle" class="w-5 h-5 text-white" />
              </div>
              <h2 class="text-xl font-semibold text-gray-900 dark:text-white">ข้อมูลพื้นฐาน</h2>
            </div>
          </template>

          <div class="space-y-6">
            <!-- Product Name -->
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                ชื่อสินค้า <span class="text-red-500">*</span>
              </label>
              <UInput v-model="form.name" placeholder="กรอกชื่อสินค้า"
                :error="v$.name.$error ? v$.name.$errors[0].$message : ''" class="w-full" />
            </div>

            <!-- Product Type and Sale Channel -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  ประเภทสินค้า <span class="text-red-500">*</span>
                </label>
                <USelect v-model="form.type" :items="productTypes" option-attribute="label" value-attribute="value"
                  placeholder="เลือกประเภทสินค้า" :error="v$.type.$error ? v$.type.$errors[0].$message : ''"
                  class="w-full" />
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  ช่องทางการขาย <span class="text-red-500">*</span>
                </label>
                <USelect v-model="form.saleChannel" :items="saleChannels" option-attribute="label"
                  value-attribute="value" placeholder="เลือกช่องทางการขาย"
                  :error="v$.saleChannel.$error ? v$.saleChannel.$errors[0].$message : ''" class="w-full" />
              </div>
            </div>

            <!-- Description -->
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                รายละเอียดสินค้า
              </label>
              <UTextarea v-model="form.description" placeholder="อธิบายรายละเอียดสินค้า (ไม่เกิน 5000 ตัวอักษร)"
                :error="v$.description.$error ? v$.description.$errors[0].$message : ''" :rows="4" class="w-full" />
            </div>

            <!-- Short Description -->
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                รายละเอียดสั้น
              </label>
              <UTextarea v-model="form.shortDescription" placeholder="รายละเอียดสั้นของสินค้า (ไม่เกิน 500 ตัวอักษร)"
                :error="v$.shortDescription.$error ? v$.shortDescription.$errors[0].$message : ''" :rows="2"
                class="w-full" />
            </div>

            <!-- Category -->
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                หมวดหมู่
              </label>
              <USelect v-model="form.categoryId" :items="categories" placeholder="เลือกหรือเพิ่มหมวดหมู่ใหม่"
                class="w-full" />
              <UButton @click="showAddCategoryModal = true" variant="outline" size="sm" class="mt-2">
                เพิ่มหมวดหมู่ใหม่
              </UButton>
            </div>
          </div>
        </UCard>

        <!-- Pricing -->
        <UCard class="overflow-hidden">
          <template #header>
            <div class="flex items-center gap-3">
              <div class="p-2 bg-gradient-to-r from-green-500 to-green-600 rounded-lg">
                <Icon name="i-heroicons-currency-dollar" class="w-5 h-5 text-white" />
              </div>
              <h2 class="text-xl font-semibold text-gray-900 dark:text-white">ราคาและต้นทุน</h2>
            </div>
          </template>

          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                ราคาขาย <span class="text-red-500">*</span>
              </label>
              <UInput v-model="form.price" type="number" placeholder="0.00"
                :error="v$.price.$error ? v$.price.$errors[0].$message : ''" class="w-full" />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                ราคาเปรียบเทียบ
              </label>
              <UInput v-model="form.compareAtPrice" type="number" placeholder="0.00"
                :error="v$.compareAtPrice.$error ? v$.compareAtPrice.$errors[0].$message : ''" class="w-full" />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                ต้นทุน
              </label>
              <UInput v-model="form.costPrice" type="number" placeholder="0.00"
                :error="v$.costPrice.$error ? v$.costPrice.$errors[0].$message : ''" class="w-full" />
            </div>
          </div>

          <!-- Price Summary -->
          <div class="mt-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <p class="text-gray-600 dark:text-gray-400">ส่วนลด</p>
                <p class="font-semibold text-green-600">{{ discountPercentage }}%</p>
              </div>
              <div>
                <p class="text-gray-600 dark:text-gray-400">กำไร</p>
                <p class="font-semibold text-blue-600">{{ profitMargin }}%</p>
              </div>
              <div>
                <p class="text-gray-600 dark:text-gray-400">ต้นทุนรวม</p>
                <p class="font-semibold text-gray-900 dark:text-white">{{ form.costPrice || 0 }} บาท</p>
              </div>
              <div>
                <p class="text-gray-600 dark:text-gray-400">ราคาขาย</p>
                <p class="font-semibold text-gray-900 dark:text-white">{{ form.price }} บาท</p>
              </div>
            </div>
          </div>
        </UCard>
        <!-- Inventory -->
        <UCard class="overflow-hidden">
          <template #header>
            <div class="flex items-center gap-3">
              <div class="p-2 bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg">
                <Icon name="i-heroicons-cube" class="w-5 h-5 text-white" />
              </div>
              <h2 class="text-xl font-semibold text-gray-900 dark:text-white">คลังสินค้า</h2>
            </div>
          </template>

          <div class="space-y-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  จำนวนในคลัง
                </label>
                <UInput v-model="form.stock" type="number" placeholder="0"
                  :error="v$.stock.$error ? v$.stock.$errors[0].$message : ''" class="w-full" />
              </div>

              <div class="flex items-center space-x-4">
                <UCheckbox v-model="form.trackStock" label="ติดตามสต็อก" />
                <UCheckbox v-model="form.allowBackorder" label="อนุญาตสั่งซื้อเมื่อหมด" />
              </div>
            </div>

            <!-- Shipping Info (for physical products) -->
            <div v-if="form.type === 'physical'" class="space-y-4">
              <h3 class="text-lg font-medium text-gray-900 dark:text-white">ข้อมูลการจัดส่ง</h3>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    น้ำหนัก (กรัม)
                  </label>
                  <UInput v-model="form.shipping.weight" type="number" placeholder="0"
                    :error="v$['shipping.weight'].$error ? v$['shipping.weight'].$errors[0].$message : ''"
                    class="w-full" />
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    คลาสการจัดส่ง
                  </label>
                  <UInput v-model="form.shipping.shippingClass" placeholder="เช่น standard, express" class="w-full" />
                </div>
              </div>

              <!-- Dimensions -->
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-4">
                  ขนาด (เซนติเมตร)
                </label>
                <div class="grid grid-cols-3 gap-4">
                  <div>
                    <label class="block text-xs text-gray-600 dark:text-gray-400 mb-1">ยาว</label>
                    <UInput v-model="form.shipping.dimensions.length" type="number" placeholder="0"
                      :error="v$['shipping.dimensions.length'].$error ? v$['shipping.dimensions.length'].$errors[0].$message : ''"
                      class="w-full" />
                  </div>
                  <div>
                    <label class="block text-xs text-gray-600 dark:text-gray-400 mb-1">กว้าง</label>
                    <UInput v-model="form.shipping.dimensions.width" type="number" placeholder="0"
                      :error="v$['shipping.dimensions.width'].$error ? v$['shipping.dimensions.width'].$errors[0].$message : ''"
                      class="w-full" />
                  </div>
                  <div>
                    <label class="block text-xs text-gray-600 dark:text-gray-400 mb-1">สูง</label>
                    <UInput v-model="form.shipping.dimensions.height" type="number" placeholder="0"
                      :error="v$['shipping.dimensions.height'].$error ? v$['shipping.dimensions.height'].$errors[0].$message : ''"
                      class="w-full" />
                  </div>
                </div>
              </div>

              <UCheckbox v-model="form.shipping.requiresShipping" label="ต้องการการจัดส่ง" />
            </div>
          </div>
        </UCard>

        <!-- Digital Assets (for digital products) -->
        <UCard v-if="form.type === 'digital'" class="overflow-hidden">
          <template #header>
            <div class="flex items-center gap-3">
              <div class="p-2 bg-gradient-to-r from-cyan-500 to-cyan-600 rounded-lg">
                <Icon name="i-heroicons-cloud-arrow-down" class="w-5 h-5 text-white" />
              </div>
              <h2 class="text-xl font-semibold text-gray-900 dark:text-white">ไฟล์ดิจิทัล</h2>
            </div>
          </template>

          <div class="space-y-4">
            <div class="flex justify-between items-center">
              <p class="text-sm text-gray-600 dark:text-gray-400">จัดการไฟล์ดิจิทัลที่ลูกค้าจะได้รับ</p>
              <UButton @click="addDigitalAsset" size="sm" variant="outline">
                <Icon name="i-heroicons-plus" class="w-4 h-4 mr-2" />
                เพิ่มไฟล์
              </UButton>
            </div>

            <div v-for="(asset, index) in form.digitalAssets" :key="index"
              class="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    ชื่อไฟล์
                  </label>
                  <UInput v-model="asset.name" placeholder="ชื่อไฟล์" class="w-full" />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    ประเภทไฟล์
                  </label>
                  <UInput v-model="asset.type" placeholder="เช่น PDF, ZIP, MP4" class="w-full" />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    URL ไฟล์
                  </label>
                  <UInput v-model="asset.url" placeholder="https://..." class="w-full" />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    ขนาดไฟล์ (MB)
                  </label>
                  <UInput v-model="asset.size" type="number" placeholder="0" class="w-full" />
                </div>
              </div>
              <div class="mt-4 flex justify-end">
                <UButton @click="removeDigitalAsset(index)" size="sm" color="red" variant="outline">
                  <Icon name="i-heroicons-trash" class="w-4 h-4 mr-2" />
                  ลบ
                </UButton>
              </div>
            </div>
          </div>
        </UCard>

        <!-- Variants -->
        <UCard class="overflow-hidden">
          <template #header>
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-3">
                <div class="p-2 bg-gradient-to-r from-pink-500 to-pink-600 rounded-lg">
                  <Icon name="i-heroicons-squares-2x2" class="w-5 h-5 text-white" />
                </div>
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white">ตัวเลือกสินค้า</h2>
              </div>
              <UButton @click="goToOptionsManagement" variant="outline" size="sm">
                <Icon name="i-heroicons-cog-6-tooth" class="w-4 h-4 mr-2" />
                จัดการตัวเลือก
              </UButton>
            </div>
          </template>

          <div class="space-y-6">
            <UCheckbox v-model="form.hasVariants" label="สินค้านี้มีตัวเลือก (เช่น สี, ขนาด)" />

            <div v-if="form.hasVariants" class="space-y-6">
              <!-- Option Sets Selection -->
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  เลือกชุดตัวเลือก
                </label>
                <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
                  เลือกชุดตัวเลือกที่ต้องการใช้กับสินค้านี้ ระบบจะสร้างตัวเลือกสินค้าให้อัตโนมัติ
                </p>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div v-for="optionSet in availableOptionSets" :key="optionSet.id" class="relative">
                    <input :id="`option-set-${optionSet.id}`" type="checkbox" :value="optionSet.id"
                      :checked="form.selectedOptionSets.includes(optionSet.id)" @change="(e) => {
                        if (e.target.checked) {
                          addOptionSet(optionSet.id);
                        } else {
                          removeOptionSet(optionSet.id);
                        }
                      }" class="sr-only" />
                    <label :for="`option-set-${optionSet.id}`"
                      class="flex flex-col p-4 border-2 rounded-lg cursor-pointer transition-all duration-200" :class="form.selectedOptionSets.includes(optionSet.id)
                        ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                        : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'">
                      <div class="flex items-center gap-2 mb-2">
                        <Icon :name="optionSet.type === 'color' ? 'i-heroicons-swatch' :
                          optionSet.type === 'image' ? 'i-heroicons-photo' :
                            'i-heroicons-list-bullet'" class="w-5 h-5"
                          :class="form.selectedOptionSets.includes(optionSet.id) ? 'text-blue-600' : 'text-gray-400'" />
                        <span class="font-medium"
                          :class="form.selectedOptionSets.includes(optionSet.id) ? 'text-blue-900 dark:text-blue-100' : 'text-gray-900 dark:text-white'">
                          {{ optionSet.name }}
                        </span>
                      </div>

                      <!-- Options Preview -->
                      <div class="flex flex-wrap gap-1 mb-2">
                        <template v-if="optionSet.type === 'color'">
                          <div v-for="option in optionSet.options.slice(0, 4)" :key="option.value"
                            class="w-4 h-4 rounded-full border border-gray-300"
                            :style="{ backgroundColor: option.color }" :title="option.label"></div>
                        </template>
                        <template v-else>
                          <UBadge v-for="option in optionSet.options.slice(0, 3)" :key="option.value" variant="subtle"
                            size="xs">
                            {{ option.label }}
                          </UBadge>
                        </template>
                        <UBadge v-if="optionSet.options.length > (optionSet.type === 'color' ? 4 : 3)" variant="outline"
                          size="xs">
                          +{{ optionSet.options.length - (optionSet.type === 'color' ? 4 : 3) }}
                        </UBadge>
                      </div>

                      <p class="text-xs text-gray-500">
                        {{ optionSet.options.length }} ตัวเลือก
                      </p>
                    </label>
                  </div>
                </div>

                <!-- No option sets available -->
                <div v-if="availableOptionSets.length === 0" class="text-center py-8">
                  <div
                    class="mx-auto w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-4">
                    <Icon name="i-heroicons-squares-2x2" class="w-8 h-8 text-gray-400" />
                  </div>
                  <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
                    ยังไม่มีชุดตัวเลือก
                  </h3>
                  <p class="text-gray-600 dark:text-gray-400 mb-4">
                    สร้างชุดตัวเลือกก่อนเพื่อใช้กับสินค้า
                  </p>
                  <UButton @click="goToOptionsManagement" variant="outline">
                    <Icon name="i-heroicons-plus" class="w-4 h-4 mr-2" />
                    สร้างชุดตัวเลือก
                  </UButton>
                </div>
              </div>

              <!-- Generated Variants Preview -->
              <div v-if="form.variants.length > 0">
                <div class="flex justify-between items-center mb-4">
                  <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                    ตัวเลือกที่สร้างขึ้น ({{ form.variants.length }} รายการ)
                  </h3>
                  <UButton @click="addVariant" size="sm" variant="outline">
                    <Icon name="i-heroicons-plus" class="w-4 h-4 mr-2" />
                    เพิ่มตัวเลือกเอง
                  </UButton>
                </div>

                <div class="space-y-4 max-h-96 overflow-y-auto">
                  <div v-for="(variant, index) in form.variants" :key="index"
                    class="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                      <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          ชื่อตัวเลือก
                        </label>
                        <UInput v-model="variant.name" placeholder="เช่น สีแดง ขนาด M" class="w-full" />
                      </div>
                      <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          SKU
                        </label>
                        <UInput v-model="variant.sku" placeholder="SKU" class="w-full" />
                      </div>
                      <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          ราคา
                        </label>
                        <UInput v-model="variant.price" type="number" placeholder="0" class="w-full" />
                      </div>
                      <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          สต็อก
                        </label>
                        <UInput v-model="variant.stock" type="number" placeholder="0" class="w-full" />
                      </div>
                    </div>

                    <!-- Variant Attributes Display -->
                    <div v-if="Object.keys(variant.attributes).length > 0" class="mt-4">
                      <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        คุณสมบัติ
                      </label>
                      <div class="flex flex-wrap gap-2">
                        <UBadge v-for="(value, key) in variant.attributes" :key="key" variant="subtle" size="sm">
                          {{ key }}: {{ value }}
                        </UBadge>
                      </div>
                    </div>

                    <div class="mt-4 flex justify-end">
                      <UButton @click="removeVariant(index)" size="sm" color="red" variant="outline">
                        <Icon name="i-heroicons-trash" class="w-4 h-4 mr-2" />
                        ลบ
                      </UButton>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Empty variants state -->
              <div v-else-if="form.selectedOptionSets.length === 0" class="text-center py-8">
                <div
                  class="mx-auto w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-4">
                  <Icon name="i-heroicons-squares-2x2" class="w-8 h-8 text-gray-400" />
                </div>
                <p class="text-gray-600 dark:text-gray-400 mb-4">เลือกชุดตัวเลือกเพื่อสร้างตัวเลือกสินค้า</p>
              </div>
            </div>
          </div>
        </UCard>

        <!-- Tags -->
        <UCard class="overflow-hidden">
          <template #header>
            <div class="flex items-center gap-3">
              <div class="p-2 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg">
                <Icon name="i-heroicons-tag" class="w-5 h-5 text-white" />
              </div>
              <h2 class="text-xl font-semibold text-gray-900 dark:text-white">แท็ก</h2>
            </div>
          </template>

          <div class="space-y-4">
            <div class="flex gap-2">
              <UInput ref="tagInput" placeholder="เพิ่มแท็ก" class="flex-1" @keyup.enter="handleAddTag()" />
              <UButton @click="handleAddTag()" variant="outline" size="sm">
                เพิ่ม
              </UButton>
            </div>

            <div v-if="form.tags.length > 0" class="flex flex-wrap gap-2">
              <UBadge v-for="tag in form.tags" :key="tag" color="primary" variant="subtle"
                class="cursor-pointer hover:bg-blue-100 dark:hover:bg-blue-900/20" @click="removeTag(tag)">
                {{ tag }}
                <Icon name="i-heroicons-x-mark" class="w-3 h-3 ml-1" />
              </UBadge>
            </div>
          </div>
        </UCard>
      </div>

      <!-- Settings Sidebar -->
      <div class="space-y-6">
        <!-- Status -->
        <UCard class="overflow-hidden">
          <template #header>
            <div class="flex items-center gap-3">
              <div class="p-2 bg-gradient-to-r from-red-500 to-red-600 rounded-lg">
                <Icon name="i-heroicons-flag" class="w-5 h-5 text-white" />
              </div>
              <h2 class="text-xl font-semibold text-gray-900 dark:text-white">สถานะ</h2>
            </div>
          </template>

          <div class="space-y-4">
            <USelect v-model="form.isActive" :items="statuses" option-attribute="label" value-attribute="value"
              placeholder="เลือกสถานะ" class="w-full" />

            <UCheckbox v-model="form.featured" label="สินค้าแนะนำ" />
          </div>
        </UCard>

        <!-- SEO -->
        <UCard class="overflow-hidden">
          <template #header>
            <div class="flex items-center gap-3">
              <div class="p-2 bg-gradient-to-r from-yellow-500 to-yellow-600 rounded-lg">
                <Icon name="i-heroicons-magnifying-glass" class="w-5 h-5 text-white" />
              </div>
              <h2 class="text-xl font-semibold text-gray-900 dark:text-white">SEO</h2>
            </div>
          </template>

          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                SEO Title
              </label>
              <UInput v-model="form.seoTitle" placeholder="SEO Title (ไม่เกิน 60 ตัวอักษร)"
                :error="v$.seoTitle.$error ? v$.seoTitle.$errors[0].$message : ''" class="w-full" />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                SEO Description
              </label>
              <UTextarea v-model="form.seoDescription" placeholder="SEO Description (ไม่เกิน 160 ตัวอักษร)"
                :error="v$.seoDescription.$error ? v$.seoDescription.$errors[0].$message : ''" :rows="3"
                class="w-full" />
            </div>
          </div>
        </UCard>

        <!-- Quick Actions -->
        <UCard class="overflow-hidden">
          <template #header>
            <div class="flex items-center gap-3">
              <div class="p-2 bg-gradient-to-r from-indigo-500 to-indigo-600 rounded-lg">
                <Icon name="i-heroicons-bolt" class="w-5 h-5 text-white" />
              </div>
              <h2 class="text-xl font-semibold text-gray-900 dark:text-white">การดำเนินการด่วน</h2>
            </div>
          </template>

          <div class="space-y-3">
            <UButton variant="outline" size="sm" class="w-full justify-start" @click="handleQuickAction('upload')">
              <Icon name="i-heroicons-camera" class="w-4 h-4 mr-2" />
              อัปโหลดรูปภาพ
            </UButton>

            <UButton variant="outline" size="sm" class="w-full justify-start" @click="handleQuickAction('copy')">
              <Icon name="i-heroicons-document-duplicate" class="w-4 h-4 mr-2" />
              คัดลอกจากสินค้าอื่น
            </UButton>

            <UButton variant="outline" size="sm" class="w-full justify-start" @click="handleQuickAction('stats')">
              <Icon name="i-heroicons-chart-bar" class="w-4 h-4 mr-2" />
              ดูสถิติสินค้า
            </UButton>
          </div>
        </UCard>

        <!-- Images Preview -->
        <UCard v-if="form.images.length > 0" class="overflow-hidden">
          <template #header>
            <div class="flex items-center gap-3">
              <div class="p-2 bg-gradient-to-r from-teal-500 to-teal-600 rounded-lg">
                <Icon name="i-heroicons-photo" class="w-5 h-5 text-white" />
              </div>
              <h2 class="text-xl font-semibold text-gray-900 dark:text-white">รูปภาพสินค้า</h2>
            </div>
          </template>

          <div class="grid grid-cols-2 gap-2">
            <div v-for="(image, index) in form.images" :key="index" class="relative">
              <img :src="image.url" :alt="image.alt" class="w-full h-20 object-cover rounded" />
              <UButton @click="form.images.splice(index, 1)" size="xs" color="red" variant="solid"
                class="absolute top-1 right-1">
                <Icon name="i-heroicons-x-mark" class="w-3 h-3" />
              </UButton>
            </div>
          </div>
        </UCard>
      </div>
    </div>
  </div>

  <!-- Modals -->
  <UModal v-model:open="showUploadModal" title="อัปโหลดรูปภาพสินค้า"
    description="อัปโหลดไฟล์รูปภาพของสินค้านี้ (รองรับ jpg, png, webp)">
    <template #body>
      <div class="p-4">
        <UFormField label="เลือกรูปภาพ">
          <UInput type="file" accept="image/*" @change="onUploadImage" />
        </UFormField>
        <div v-if="uploadedImage" class="mt-4">
          <img :src="uploadedImage" alt="preview" class="w-32 h-32 object-cover rounded" />
        </div>
      </div>
    </template>
    <template #footer>
      <div class="flex justify-end gap-3">
        <UButton @click="showUploadModal = false" variant="outline">ปิด</UButton>
        <UButton @click="confirmUploadImage" color="primary">บันทึก</UButton>
      </div>
    </template>
  </UModal>

  <UModal v-model:open="showCopyModal" title="คัดลอกจากสินค้าอื่น"
    description="เลือกสินค้าที่ต้องการคัดลอกข้อมูลมาเติมในฟอร์มนี้">
    <template #body>
      <div class="p-4">
        <UFormField label="ค้นหาสินค้า">
          <UInput v-model="copySearch" placeholder="ค้นหาชื่อสินค้า..." />
        </UFormField>
        <div v-if="copyCandidates.length > 0" class="mt-4 space-y-2">
          <div v-for="item in copyCandidates" :key="item._id" class="flex items-center gap-2">
            <span>{{ item.name }}</span>
            <UButton size="xs" @click="copyProduct(item)">คัดลอก</UButton>
          </div>
        </div>
        <div v-else class="text-gray-400 mt-4">ไม่พบสินค้า</div>
      </div>
    </template>
    <template #footer>
      <div class="flex justify-end gap-3">
        <UButton @click="showCopyModal = false" variant="outline">ปิด</UButton>
      </div>
    </template>
  </UModal>

  <UModal v-model:open="showStatsModal" title="ดูสถิติสินค้า" description="ดูตัวอย่างสถิติสินค้าจากระบบ">
    <template #body>
      <div class="p-4">
        <div class="grid grid-cols-2 gap-4">
          <div>
            <div class="text-sm text-gray-500">ยอดขายรวม</div>
            <div class="font-bold text-lg text-blue-600">{{ mockStats.sales }} ชิ้น</div>
          </div>
          <div>
            <div class="text-sm text-gray-500">รายได้รวม</div>
            <div class="font-bold text-lg text-green-600">{{ mockStats.revenue }} บาท</div>
          </div>
          <div>
            <div class="text-sm text-gray-500">ดูหน้า</div>
            <div class="font-bold text-lg text-purple-600">{{ mockStats.views }} ครั้ง</div>
          </div>
          <div>
            <div class="text-sm text-gray-500">คะแนนรีวิว</div>
            <div class="font-bold text-lg text-yellow-600">{{ mockStats.rating }}/5</div>
          </div>
        </div>
      </div>
    </template>
    <template #footer>
      <div class="flex justify-end gap-3">
        <UButton @click="showStatsModal = false" variant="outline">ปิด</UButton>
      </div>
    </template>
  </UModal>

  <!-- Add Category Modal -->
  <UModal v-model:open="showAddCategoryModal" title="เพิ่มหมวดหมู่ใหม่">
    <template #body>
      <div class="space-y-4">
        <UFormField label="ชื่อหมวดหมู่">
          <UInput v-model="newCategoryInput" placeholder="กรอกชื่อหมวดหมู่ใหม่" @keyup.enter="handleAddCategory" />
        </UFormField>
      </div>
    </template>
    <template #footer>
      <div class="flex justify-end gap-3">
        <UButton @click="showAddCategoryModal = false" variant="outline">ยกเลิก</UButton>
        <UButton @click="handleAddCategory" color="primary">เพิ่ม</UButton>
      </div>
    </template>
  </UModal>
</template>

<style scoped>
/* Add smooth animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.space-y-8>* {
  animation: fadeInUp 0.6s ease-out forwards;
}

/* Hover effects for cards */
.group:hover {
  transform: translateY(-2px);
}

/* Gradient text animation */
.bg-gradient-to-r {
  background-size: 200% 200%;
  animation: gradient 3s ease infinite;
}

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}
</style>