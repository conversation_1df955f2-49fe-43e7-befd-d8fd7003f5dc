<script lang="ts">
	import { z } from 'zod';
	import { createReactiveValidator, createValidator } from '$lib/utils/validation';
	import { Input, Button } from '$lib/components/ui';

	// ตัวอย่าง Schema สำหรับ Contact Form
	const contactSchema = z.object({
		name: z.string().min(2, 'ชื่อต้องมีอย่างน้อย 2 ตัวอักษร').max(50, 'ชื่อต้องไม่เกิน 50 ตัวอักษร'),
		email: z.string().email('รูปแบบอีเมลไม่ถูกต้อง'),
		phone: z.string().min(10, 'หมายเลขโทรศัพท์ต้องมีอย่างน้อย 10 หลัก').optional(),
		message: z.string().min(10, 'ข้อความต้องมีอย่างน้อย 10 ตัวอักษร').max(500, 'ข้อความต้องไม่เกิน 500 ตัวอักษร'),
		agreeToTerms: z.boolean().refine(val => val === true, 'กรุณายอมรับเงื่อนไขการใช้งาน')
	});

	type ContactData = z.infer<typeof contactSchema>;

	// ตัวอย่างที่ 1: ใช้ createReactiveValidator (คล้าย vuelidate)
	const initialContactData: ContactData = {
		name: '',
		email: '',
		phone: '',
		message: '',
		agreeToTerms: false
	};

	const v = createReactiveValidator(contactSchema, initialContactData, {
		validateOnInput: true,
		validateOnBlur: true,
		debounceMs: 300,
		showErrorsOnlyAfterTouch: true
	});

	// ตัวอย่างที่ 2: ใช้ createValidator แบบง่าย
	const simpleValidator = createValidator(contactSchema);
	let simpleFormData = $state<ContactData>({
		name: '',
		email: '',
		phone: '',
		message: '',
		agreeToTerms: false
	});
	let simpleErrors = $state<Record<string, string>>({});

	// ฟังก์ชันสำหรับ simple validator
	function validateSimpleField(field: keyof ContactData, value: any) {
		const error = simpleValidator.validateField(field, value);
		if (error) {
			simpleErrors = { ...simpleErrors, [field]: error };
		} else {
			const newErrors = { ...simpleErrors };
			delete newErrors[field];
			simpleErrors = newErrors;
		}
	}

	function handleSimpleSubmit() {
		const allErrors = simpleValidator.validateForm(simpleFormData);
		simpleErrors = allErrors;
		
		if (Object.keys(allErrors).length === 0) {
			console.log('Simple form is valid!', simpleFormData);
			alert('Simple form submitted successfully!');
		} else {
			console.log('Simple form has errors:', allErrors);
		}
	}

	// ฟังก์ชันสำหรับ reactive validator
	function handleReactiveSubmit() {
		const isValid = v.$validate();
		
		if (isValid) {
			console.log('Reactive form is valid!', v.$data);
			alert('Reactive form submitted successfully!');
		} else {
			console.log('Reactive form has errors:', v.$errors);
		}
	}

	// ตัวอย่างที่ 3: การใช้งานแบบ manual validation
	let manualData = $state({
		username: '',
		password: ''
	});
	let manualErrors = $state<Record<string, string>>({});

	const loginSchema = z.object({
		username: z.string().min(3, 'ชื่อผู้ใช้ต้องมีอย่างน้อย 3 ตัวอักษร'),
		password: z.string().min(6, 'รหัสผ่านต้องมีอย่างน้อย 6 ตัวอักษร')
	});

	function validateManualField(field: string, value: string) {
		try {
			if (field === 'username') {
				loginSchema.pick({ username: true }).parse({ username: value });
			} else if (field === 'password') {
				loginSchema.pick({ password: true }).parse({ password: value });
			}
			
			// ถ้า validate ผ่าน ให้ลบ error
			const newErrors = { ...manualErrors };
			delete newErrors[field];
			manualErrors = newErrors;
		} catch (error: any) {
			if (error.errors && error.errors.length > 0) {
				manualErrors = { ...manualErrors, [field]: error.errors[0].message };
			}
		}
	}
</script>

<div class="max-w-4xl mx-auto p-6 space-y-12">
	<h1 class="text-3xl font-bold text-center">ตัวอย่างการใช้งาน Validation</h1>

	<!-- ตัวอย่างที่ 1: Reactive Validator (คล้าย vuelidate) -->
	<div class="card bg-base-100 shadow-xl">
		<div class="card-body">
			<h2 class="card-title text-2xl mb-4">ตัวอย่างที่ 1: Reactive Validator (คล้าย vuelidate)</h2>
			
			<!-- Debug Info -->
			<div class="bg-base-200 p-4 rounded-lg mb-4 text-sm">
				<h3 class="font-semibold mb-2">สถานะ Form:</h3>
				<div class="grid grid-cols-2 gap-2">
					<div>Valid: <span class="badge {v.$valid ? 'badge-success' : 'badge-error'}">{v.$valid}</span></div>
					<div>Invalid: <span class="badge {v.$invalid ? 'badge-error' : 'badge-success'}">{v.$invalid}</span></div>
					<div>Dirty: <span class="badge {v.$dirty ? 'badge-warning' : 'badge-neutral'}">{v.$dirty}</span></div>
					<div>Pristine: <span class="badge {v.$pristine ? 'badge-neutral' : 'badge-warning'}">{v.$pristine}</span></div>
				</div>
			</div>

			<form on:submit|preventDefault={handleReactiveSubmit} class="space-y-4">
				<Input
					label="ชื่อ"
					bind:value={v.fields.name.value}
					onblur={v.fields.name.onBlur}
					validate={v.fields.name.error}
					placeholder="กรอกชื่อของคุณ"
				/>

				<Input
					label="อีเมล"
					type="email"
					bind:value={v.fields.email.value}
					onblur={v.fields.email.onBlur}
					validate={v.fields.email.error}
					placeholder="<EMAIL>"
				/>

				<Input
					label="โทรศัพท์ (ไม่บังคับ)"
					bind:value={v.fields.phone.value}
					onblur={v.fields.phone.onBlur}
					validate={v.fields.phone.error}
					placeholder="0812345678"
				/>

				<div class="form-control">
					<label class="label" for="message">
						<span class="label-text">ข้อความ</span>
					</label>
					<textarea
						id="message"
						bind:value={v.fields.message.value}
						on:blur={v.fields.message.onBlur}
						class="textarea textarea-bordered {v.fields.message.hasError ? 'textarea-error' : ''}"
						placeholder="กรอกข้อความของคุณ"
						rows="4"
					></textarea>
					{#if v.fields.message.error}
						<label class="label">
							<span class="label-text-alt text-error">{v.fields.message.error}</span>
						</label>
					{/if}
				</div>

				<div class="form-control">
					<label class="label cursor-pointer justify-start">
						<input
							type="checkbox"
							bind:checked={v.fields.agreeToTerms.value}
							class="checkbox checkbox-primary mr-2"
						/>
						<span class="label-text">ยอมรับเงื่อนไขการใช้งาน</span>
					</label>
					{#if v.fields.agreeToTerms.error}
						<label class="label">
							<span class="label-text-alt text-error">{v.fields.agreeToTerms.error}</span>
						</label>
					{/if}
				</div>

				<Button type="submit" color="primary" disabled={!v.$valid} class="w-full">
					ส่งข้อมูล (Reactive)
				</Button>
			</form>
		</div>
	</div>

	<!-- ตัวอย่างที่ 2: Simple Validator -->
	<div class="card bg-base-100 shadow-xl">
		<div class="card-body">
			<h2 class="card-title text-2xl mb-4">ตัวอย่างที่ 2: Simple Validator</h2>
			
			<form on:submit|preventDefault={handleSimpleSubmit} class="space-y-4">
				<Input
					label="ชื่อ"
					bind:value={simpleFormData.name}
					oninput={(e) => validateSimpleField('name', e.target.value)}
					validate={simpleErrors.name}
					placeholder="กรอกชื่อของคุณ"
				/>

				<Input
					label="อีเมล"
					type="email"
					bind:value={simpleFormData.email}
					oninput={(e) => validateSimpleField('email', e.target.value)}
					validate={simpleErrors.email}
					placeholder="<EMAIL>"
				/>

				<Button type="submit" color="secondary" class="w-full">
					ส่งข้อมูล (Simple)
				</Button>
			</form>
		</div>
	</div>

	<!-- ตัวอย่างที่ 3: Manual Validation -->
	<div class="card bg-base-100 shadow-xl">
		<div class="card-body">
			<h2 class="card-title text-2xl mb-4">ตัวอย่างที่ 3: Manual Validation</h2>
			
			<form class="space-y-4">
				<Input
					label="ชื่อผู้ใช้"
					bind:value={manualData.username}
					onblur={() => validateManualField('username', manualData.username)}
					validate={manualErrors.username}
					placeholder="กรอกชื่อผู้ใช้"
				/>

				<Input
					label="รหัสผ่าน"
					type="password"
					bind:value={manualData.password}
					onblur={() => validateManualField('password', manualData.password)}
					validate={manualErrors.password}
					placeholder="กรอกรหัสผ่าน"
				/>

				<Button type="button" color="accent" class="w-full">
					เข้าสู่ระบบ (Manual)
				</Button>
			</form>
		</div>
	</div>
</div>
