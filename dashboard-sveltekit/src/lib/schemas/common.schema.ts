// ✅ Common Schema - Zod Validation
import { z } from 'zod';

// ✅ Basic Zod Schemas
export const emailSchema = z.email('รูปแบบอีเมลไม่ถูกต้อง');
export const passwordSchema = z
  .string()
  .min(6, 'รหัสผ่านต้องมีอย่างน้อย 6 ตัวอักษร')
  .max(128, 'รหัสผ่านต้องไม่เกิน 128 ตัวอักษร');
export const nameSchema = z
  .string()
  .min(2, 'ชื่อต้องมีอย่างน้อย 2 ตัวอักษร')
  .max(50, 'ชื่อต้องไม่เกิน 50 ตัวอักษร');
export const phoneSchema = z
  .string()
  .refine(val => /^[0-9+\-\s()]*$/.test(val), {
    message: 'หมายเลขโทรศัพท์ไม่ถูกต้อง',
  })
  .refine(val => val.length <= 20, {
    message: 'หมายเลขโทรศัพท์ต้องไม่เกิน 20 ตัวอักษร',
  })
  .optional();
export const urlSchema = z.string().url({ message: 'รูปแบบ URL ไม่ถูกต้อง' }).optional();

// ✅ Search and Filter Schemas
export const searchSchema = z.object({
  query: z.string().max(100, 'คำค้นหาต้องไม่เกิน 100 ตัวอักษร').optional(),
  page: z.number().int().min(1, 'หน้าต้องมากกว่า 0').optional().default(1),
  limit: z
    .number()
    .int()
    .min(1, 'จำนวนต่อหน้าต้องมากกว่า 0')
    .max(100, 'จำนวนต่อหน้าต้องไม่เกิน 100')
    .optional()
    .default(10),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).optional().default('desc'),
});

// ✅ Pagination Schema
export const paginationSchema = z.object({
  page: z.number().int().min(1).optional().default(1),
  limit: z.number().int().min(1).max(100).optional().default(10),
  total: z.number().int().min(0).optional(),
});

// ✅ File Upload Schema
export const fileUploadSchema = z.object({
  filename: z.string().min(1, 'กรุณาเลือกไฟล์').max(255, 'ชื่อไฟล์ต้องไม่เกิน 255 ตัวอักษร'),
  size: z.number().max(10 * 1024 * 1024, 'ไฟล์ต้องมีขนาดไม่เกิน 10MB'),
  type: z.string().refine(val => /^(image\/|application\/pdf|text\/)/.test(val), {
    message: 'ไฟล์ต้องเป็นรูปภาพ, PDF หรือข้อความเท่านั้น',
  }),
});

// ✅ Address Schema
export const addressSchema = z.object({
  street: z.string().min(1, 'กรุณากรอกที่อยู่').max(200, 'ที่อยู่ต้องไม่เกิน 200 ตัวอักษร'),
  city: z.string().min(1, 'กรุณากรอกเมือง').max(100, 'เมืองต้องไม่เกิน 100 ตัวอักษร'),
  state: z.string().min(1, 'กรุณากรอกจังหวัด').max(100, 'จังหวัดต้องไม่เกิน 100 ตัวอักษร'),
  postalCode: z.string().regex(/^[0-9]{5}$/, 'รหัสไปรษณีย์ต้องเป็นตัวเลข 5 หลัก'),
  country: z.string().min(1, 'กรุณาเลือกประเทศ').max(100, 'ประเทศต้องไม่เกิน 100 ตัวอักษร'),
});

// ✅ Social Media Schema
export const socialMediaSchema = z.object({
  facebook: urlSchema,
  twitter: urlSchema,
  instagram: urlSchema,
  linkedin: urlSchema,
  youtube: urlSchema,
});

// ✅ Settings Schema
export const settingsSchema = z.object({
  notifications: z.object({
    email: z.boolean(),
    push: z.boolean(),
    sms: z.boolean(),
  }),
  privacy: z.object({
    profileVisibility: z.enum(['public', 'private', 'friends'], {
      message: 'Profile Visibility ต้องเป็น public, private, friends',
    }),
    showEmail: z.boolean(),
    showPhone: z.boolean(),
  }),
  theme: z.enum(['light', 'dark', 'auto'], {
    message: 'Theme ต้องเป็น light, dark, หรือ auto',
  }),
});

// ✅ Site Creation Schemas
export const createSiteSchema = z
  .object({
    siteName: z
      .string()
      .min(5, 'ชื่อเว็บไซต์ต้องมีอย่างน้อย 5 ตัวอักษร')
      .max(100, 'ชื่อเว็บไซต์ต้องไม่เกิน 100 ตัวอักษร'),
    typeDomain: z.enum(['subdomain', 'custom'], {
      message: 'กรุณาเลือกประเภทโดเมน',
    }),
    subDomain: z
      .string()
      .min(5, 'ซับโดเมนต้องมีอย่างน้อย 5 ตัวอักษร')
      .max(20, 'ซับโดเมนต้องไม่เกิน 20 ตัวอักษร')
      .regex(/^[a-z0-9-]+$/, 'ซับโดเมนต้องเป็นตัวอักษรภาษาอังกฤษ ตัวเลข และขีดเท่านั้น')
      .optional(),
    mainDomain: z.string().optional(),
    customDomain: z.string().optional(),
    packageType: z.string().min(1, 'กรุณาเลือกแพ็คเกจ'),
  })
  .refine(
    data => {
      if (data.typeDomain === 'subdomain') {
        return data.subDomain && data.mainDomain;
      }
      if (data.typeDomain === 'custom') {
        return data.customDomain;
      }
      return true;
    },
    {
      message: 'กรุณากรอกข้อมูลโดเมนให้ครบถ้วน',
    },
  )
  .refine(
    data => {
      // ตรวจสอบ customDomain เฉพาะเมื่อ typeDomain เป็น custom
      if (data.typeDomain === 'custom' && data.customDomain) {
        return /^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/.test(data.customDomain);
      }
      return true;
    },
    {
      message: 'รูปแบบโดเมนไม่ถูกต้อง',
      path: ['customDomain'],
    },
  );

export const checkDomainSchema = z
  .object({
    typeDomain: z.enum(['subdomain', 'custom'], {
      message: 'กรุณาเลือกประเภทโดเมน',
    }),
    subDomain: z.string().optional(),
    mainDomain: z.string().optional(),
    customDomain: z.string().optional(),
  })
  .refine(
    data => {
      if (data.typeDomain === 'subdomain') {
        return data.subDomain && data.mainDomain;
      }
      if (data.typeDomain === 'custom') {
        return data.customDomain;
      }
      return true;
    },
    {
      message: 'กรุณากรอกข้อมูลโดเมนให้ครบถ้วน',
    },
  );

// ✅ Type inference from schemas
export type SearchData = z.infer<typeof searchSchema>;
export type PaginationData = z.infer<typeof paginationSchema>;
export type FileUploadData = z.infer<typeof fileUploadSchema>;
export type AddressData = z.infer<typeof addressSchema>;
export type SocialMediaData = z.infer<typeof socialMediaSchema>;
export type SettingsData = z.infer<typeof settingsSchema>;
export type CreateSiteData = z.infer<typeof createSiteSchema>;
export type CheckDomainData = z.infer<typeof checkDomainSchema>;

// ✅ Validation Functions using Zod
export function validateSearchForm(data: unknown): {
  success: boolean;
  data?: SearchData;
  errors?: Record<string, string>;
} {
  const result = searchSchema.safeParse(data);
  if (result.success) {
    return { success: true, data: result.data };
  }

  const errors: Record<string, string> = {};
  result.error.issues.forEach(issue => {
    const path = issue.path.join('.');
    if (path) {
      errors[path] = issue.message;
    }
  });

  return { success: false, errors };
}

export function validateFileUploadForm(data: unknown): {
  success: boolean;
  data?: FileUploadData;
  errors?: Record<string, string>;
} {
  const result = fileUploadSchema.safeParse(data);
  if (result.success) {
    return { success: true, data: result.data };
  }

  const errors: Record<string, string> = {};
  result.error.issues.forEach(issue => {
    const path = issue.path.join('.');
    if (path) {
      errors[path] = issue.message;
    }
  });

  return { success: false, errors };
}

export function validateAddressForm(data: unknown): {
  success: boolean;
  data?: AddressData;
  errors?: Record<string, string>;
} {
  const result = addressSchema.safeParse(data);
  if (result.success) {
    return { success: true, data: result.data };
  }

  const errors: Record<string, string> = {};
  result.error.issues.forEach(issue => {
    const path = issue.path.join('.');
    if (path) {
      errors[path] = issue.message;
    }
  });

  return { success: false, errors };
}

export function validateSocialMediaForm(data: unknown): {
  success: boolean;
  data?: SocialMediaData;
  errors?: Record<string, string>;
} {
  const result = socialMediaSchema.safeParse(data);
  if (result.success) {
    return { success: true, data: result.data };
  }

  const errors: Record<string, string> = {};
  result.error.issues.forEach(issue => {
    const path = issue.path.join('.');
    if (path) {
      errors[path] = issue.message;
    }
  });

  return { success: false, errors };
}

export function validateSettingsForm(data: unknown): {
  success: boolean;
  data?: SettingsData;
  errors?: Record<string, string>;
} {
  const result = settingsSchema.safeParse(data);
  if (result.success) {
    return { success: true, data: result.data };
  }

  const errors: Record<string, string> = {};
  result.error.issues.forEach(issue => {
    const path = issue.path.join('.');
    if (path) {
      errors[path] = issue.message;
    }
  });

  return { success: false, errors };
}

export function validateCreateSiteForm(data: unknown): {
  success: boolean;
  data?: CreateSiteData;
  errors?: Record<string, string>;
} {
  const result = createSiteSchema.safeParse(data);
  if (result.success) {
    return { success: true, data: result.data };
  }

  const errors: Record<string, string> = {};
  result.error.issues.forEach(issue => {
    const path = issue.path.join('.');
    if (path) {
      errors[path] = issue.message;
    }
  });

  return { success: false, errors };
}

export function validateCheckDomainForm(data: unknown): {
  success: boolean;
  data?: CheckDomainData;
  errors?: Record<string, string>;
} {
  const result = checkDomainSchema.safeParse(data);
  if (result.success) {
    return { success: true, data: result.data };
  }

  const errors: Record<string, string> = {};
  result.error.issues.forEach(issue => {
    const path = issue.path.join('.');
    if (path) {
      errors[path] = issue.message;
    }
  });

  return { success: false, errors };
}

// ✅ Helper Functions
export function sanitizeStringData<T extends Record<string, any>>(data: T): T {
  const sanitized = { ...data };

  // Trim string fields
  Object.keys(sanitized).forEach(key => {
    if (typeof sanitized[key] === 'string') {
      (sanitized as any)[key] = (sanitized[key] as string).trim();
    }
  });

  return sanitized;
}

export function isValidEmail(email: string): boolean {
  return emailSchema.safeParse(email).success;
}

export function isValidUrl(url: string): boolean {
  return urlSchema.safeParse(url).success;
}

// ✅ Type Guards using Zod
export function isValidSearchData(data: unknown): data is SearchData {
  return searchSchema.safeParse(data).success;
}

export function isValidFileUploadData(data: unknown): data is FileUploadData {
  return fileUploadSchema.safeParse(data).success;
}

export function isValidAddressData(data: unknown): data is AddressData {
  return addressSchema.safeParse(data).success;
}

export function isValidSocialMediaData(data: unknown): data is SocialMediaData {
  return socialMediaSchema.safeParse(data).success;
}

export function isValidSettingsData(data: unknown): data is SettingsData {
  return settingsSchema.safeParse(data).success;
}

export function isValidCreateSiteData(data: unknown): data is CreateSiteData {
  return createSiteSchema.safeParse(data).success;
}

export function isValidCheckDomainData(data: unknown): data is CheckDomainData {
  return checkDomainSchema.safeParse(data).success;
}
