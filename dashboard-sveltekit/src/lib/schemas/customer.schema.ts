// ✅ Customer Schema - Zod Validation
import { z } from 'zod';

// ✅ Zod Schemas
const customerAddressSchema = z
  .object({
    street: z.string().optional(),
    city: z.string().optional(),
    state: z.string().optional(),
    zipCode: z.string().optional(),
    country: z.string().optional(),
  })
  .optional();

export const createCustomerSchema = z.object({
  firstName: z
    .string()
    .min(2, 'ชื่อจริงต้องมีอย่างน้อย 2 ตัวอักษร')
    .max(50, 'ชื่อจริงต้องไม่เกิน 50 ตัวอักษร'),
  lastName: z
    .string()
    .min(2, 'นามสกุลต้องมีอย่างน้อย 2 ตัวอักษร')
    .max(50, 'นามสกุลต้องไม่เกิน 50 ตัวอักษร'),
  email: z.email('รูปแบบอีเมลไม่ถูกต้อง'),
  phone: z.string().max(20, 'เบอร์โทรศัพท์ต้องไม่เกิน 20 ตัวอักษร').optional(),
  address: customerAddressSchema,
  isActive: z.boolean().optional().default(true),
  notes: z.string().max(500, 'หมายเหตุต้องไม่เกิน 500 ตัวอักษร').optional(),
});

export const updateCustomerSchema = createCustomerSchema.partial().extend({
  id: z.string().optional(),
});

// ✅ Type inference from schemas
export type CreateCustomerData = z.infer<typeof createCustomerSchema>;
export type UpdateCustomerData = z.infer<typeof updateCustomerSchema>;

// ✅ Validation Functions using Zod
export function validateCreateCustomerData(data: unknown): {
  success: boolean;
  data?: CreateCustomerData;
  error?: string;
} {
  const result = createCustomerSchema.safeParse(data);
  if (result.success) {
    return { success: true, data: result.data };
  }

  const firstError = result.error.issues[0];
  return { success: false, error: firstError?.message || 'ข้อมูลไม่ถูกต้อง' };
}

export function validateUpdateCustomerData(data: unknown): {
  success: boolean;
  data?: UpdateCustomerData;
  error?: string;
} {
  const result = updateCustomerSchema.safeParse(data);
  if (result.success) {
    return { success: true, data: result.data };
  }

  const firstError = result.error.issues[0];
  return { success: false, error: firstError?.message || 'ข้อมูลไม่ถูกต้อง' };
}

// ✅ Sanitization Functions
export function sanitizeCustomerData(
  data: CreateCustomerData | UpdateCustomerData,
): CreateCustomerData | UpdateCustomerData {
  const sanitized = { ...data };

  if (sanitized.firstName) {
    sanitized.firstName = sanitized.firstName.trim();
  }

  if (sanitized.lastName) {
    sanitized.lastName = sanitized.lastName.trim();
  }

  if (sanitized.email) {
    sanitized.email = sanitized.email.trim().toLowerCase();
  }

  if (sanitized.phone) {
    sanitized.phone = sanitized.phone.trim();
  }

  if (sanitized.notes) {
    sanitized.notes = sanitized.notes.trim();
  }

  if (sanitized.address) {
    sanitized.address = { ...sanitized.address };
    if (sanitized.address.street) {
      sanitized.address.street = sanitized.address.street.trim();
    }
    if (sanitized.address.city) {
      sanitized.address.city = sanitized.address.city.trim();
    }
    if (sanitized.address.state) {
      sanitized.address.state = sanitized.address.state.trim();
    }
    if (sanitized.address.zipCode) {
      sanitized.address.zipCode = sanitized.address.zipCode.trim();
    }
    if (sanitized.address.country) {
      sanitized.address.country = sanitized.address.country.trim();
    }
  }

  return sanitized;
}

// ✅ Type Guards using Zod
export function isValidCustomerData(data: unknown): data is CreateCustomerData {
  return createCustomerSchema.safeParse(data).success;
}

export function isValidUpdateCustomerData(data: unknown): data is UpdateCustomerData {
  return updateCustomerSchema.safeParse(data).success;
}
