import { z } from 'zod';

// ✅ Zod Schemas
export const productImageSchema = z.object({
  url: z.string().url('URL รูปภาพต้องเป็นลิงก์ที่ถูกต้อง'),
  alt: z.string().optional(),
  isPrimary: z.boolean().default(false),
  variantId: z.string().optional(),
});

export const productVariantSchema = z.object({
  _id: z.string().optional(),
  name: z.string().min(1, 'กรุณากรอกชื่อตัวเลือก').max(100, 'ชื่อตัวเลือกต้องไม่เกิน 100 ตัวอักษร'),
  sku: z.string().optional(),
  price: z.number().min(0, 'ราคาต้องไม่น้อยกว่า 0').optional(),
  stock: z.number().int().min(0, 'สต็อกต้องไม่น้อยกว่า 0').max(999999, 'สต็อกต้องไม่เกิน 999,999').optional(),
  attributes: z.record(z.string(), z.string()),
  images: z.array(z.string().url()).optional(),
  isActive: z.boolean().default(true),
});

export const digitalAssetSchema = z.object({
  name: z.string().min(1, 'ชื่อไฟล์ดิจิทัลต้องไม่ว่าง'),
  url: z.string().url('URL ไฟล์ต้องเป็นลิงก์ที่ถูกต้อง'),
  fileSize: z.number().min(0).optional(),
  fileType: z.string().optional(),
  downloadLimit: z.number().min(0).optional(),
  expiryDays: z.number().min(0).optional(),
});

export const shippingSchema = z.object({
  weight: z.number().min(0, 'น้ำหนักต้องไม่น้อยกว่า 0').optional(),
  dimensions: z.object({
    length: z.number().min(0, 'ความยาวต้องไม่น้อยกว่า 0'),
    width: z.number().min(0, 'ความกว้างต้องไม่น้อยกว่า 0'),
    height: z.number().min(0, 'ความสูงต้องไม่น้อยกว่า 0'),
  }).optional(),
  shippingClass: z.string().optional(),
});

export const createProductSchema = z.object({
  name: z
    .string()
    .min(1, 'ชื่อสินค้าต้องไม่ว่าง')
    .max(200, 'ชื่อสินค้าต้องไม่เกิน 200 ตัวอักษร'),
  type: z.enum(['physical', 'digital', 'service', 'subscription'], {
    errorMap: () => ({ message: 'ประเภทสินค้าไม่ถูกต้อง' }),
  }).default('physical'),
  saleChannel: z.enum(['online', 'offline', 'both'], {
    errorMap: () => ({ message: 'ช่องทางการขายไม่ถูกต้อง' }),
  }).default('online'),
  price: z.number().min(0, 'ราคาสินค้าต้องไม่น้อยกว่า 0'),
  compareAtPrice: z.number().min(0, 'ราคาปกติต้องไม่น้อยกว่า 0').optional(),
  costPrice: z.number().min(0, 'ราคาทุนต้องไม่น้อยกว่า 0').optional(),
  stock: z.number().int().min(0, 'สต็อกต้องไม่น้อยกว่า 0').optional().default(0),
  trackStock: z.boolean().default(true),
  allowBackorder: z.boolean().default(false),
  categoryId: z.string().optional(),
  description: z.string().max(5000, 'รายละเอียดสินค้าต้องไม่เกิน 5000 ตัวอักษร').optional(),
  shortDescription: z.string().max(500, 'รายละเอียดสั้นต้องไม่เกิน 500 ตัวอักษร').optional(),
  tags: z.array(z.string()).optional(),
  images: z.array(productImageSchema).optional(),
  hasVariants: z.boolean().default(false),
  variants: z.array(productVariantSchema).optional(),
  variantAttributes: z.array(z.string()).optional(),
  digitalAssets: z.array(digitalAssetSchema).optional(),
  shipping: shippingSchema.optional(),
  seoTitle: z.string().max(60, 'SEO title ต้องไม่เกิน 60 ตัวอักษร').optional(),
  seoDescription: z.string().max(160, 'SEO description ต้องไม่เกิน 160 ตัวอักษร').optional(),
  featured: z.boolean().default(false),
  isActive: z.boolean().default(true),
  allowPreOrder: z.boolean().default(false),
  customFields: z.record(z.string(), z.any()).optional(),
});

export const updateProductSchema = createProductSchema.partial().extend({
  id: z.string().optional(),
});

export const productStockSchema = z.object({
  stock: z.number().int().min(0, 'สต็อกต้องไม่น้อยกว่า 0').max(999999, 'สต็อกต้องไม่เกิน 999,999'),
  variantId: z.string().optional(),
});

// ✅ Type inference from schemas
export type CreateProductData = z.infer<typeof createProductSchema>;
export type UpdateProductData = z.infer<typeof updateProductSchema>;
export type ProductStockData = z.infer<typeof productStockSchema>;
export type ProductVariantData = z.infer<typeof productVariantSchema>;

// ✅ Validation Functions using Zod
export function validateCreateProductData(data: unknown): {
  success: boolean;
  data?: CreateProductData;
  error?: string;
} {
  const result = createProductSchema.safeParse(data);
  if (result.success) {
    return { success: true, data: result.data };
  }

  const firstError = result.error.issues[0];
  return { success: false, error: firstError?.message || 'ข้อมูลไม่ถูกต้อง' };
}

export function validateUpdateProductData(data: unknown): {
  success: boolean;
  data?: UpdateProductData;
  error?: string;
} {
  const result = updateProductSchema.safeParse(data);
  if (result.success) {
    return { success: true, data: result.data };
  }

  const firstError = result.error.issues[0];
  return { success: false, error: firstError?.message || 'ข้อมูลไม่ถูกต้อง' };
}

export function validateProductStockData(data: unknown): {
  success: boolean;
  data?: ProductStockData;
  error?: string;
} {
  const result = productStockSchema.safeParse(data);
  if (result.success) {
    return { success: true, data: result.data };
  }

  const firstError = result.error.issues[0];
  return { success: false, error: firstError?.message || 'ข้อมูลไม่ถูกต้อง' };
}

// ✅ Sanitization Functions
export function sanitizeProductData(
  data: CreateProductData | UpdateProductData,
): CreateProductData | UpdateProductData {
  const sanitized = { ...data };

  if (sanitized.name) {
    sanitized.name = sanitized.name.trim();
  }

  if (sanitized.description) {
    sanitized.description = sanitized.description.trim();
  }

  if (sanitized.price !== undefined) {
    sanitized.price = Number(sanitized.price);
  }

  if (sanitized.stock !== undefined) {
    sanitized.stock = Number(sanitized.stock);
  }

  if (sanitized.images?.length) {
    sanitized.images = sanitized.images.filter(img => img && img.trim());
  }

  return sanitized;
}

// ✅ Type Guards using Zod
export function isValidProductData(data: unknown): data is CreateProductData {
  return createProductSchema.safeParse(data).success;
}

export function isValidProductStockData(data: unknown): data is ProductStockData {
  return productStockSchema.safeParse(data).success;
}
