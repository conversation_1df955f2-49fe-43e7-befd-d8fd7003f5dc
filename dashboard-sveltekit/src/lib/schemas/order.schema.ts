// ✅ Order Schema - Zod Validation
import { z } from 'zod';

// ✅ Zod Schemas
const orderItemSchema = z.object({
  productId: z.string().min(1, 'สินค้าแต่ละรายการต้องมีรหัสสินค้า'),
  quantity: z.number().int().min(1, 'จำนวนสินค้าต้องมากกว่า 0'),
  price: z.number().min(0, 'ราคาสินค้าต้องไม่น้อยกว่า 0'),
  variantId: z.string().optional(),
});

const shippingAddressSchema = z.object({
  street: z.string().min(1, 'กรุณากรอกที่อยู่จัดส่ง'),
  city: z.string().min(1, 'กรุณากรอกเมือง'),
  state: z.string().optional(),
  zipCode: z.string().min(1, 'กรุณากรอกรหัสไปรษณีย์'),
  country: z.string().min(1, 'กรุณากรอกประเทศ'),
});

export const createOrderSchema = z.object({
  customerId: z.string().min(1, 'กรุณาเลือกลูกค้า'),
  items: z.array(orderItemSchema).min(1, 'ต้องมีสินค้าอย่างน้อย 1 รายการ'),
  shippingAddress: shippingAddressSchema,
  paymentMethod: z.string().min(1, 'กรุณาเลือกวิธีชำระเงิน'),
  note: z.string().optional(),
});

export const updateOrderSchema = createOrderSchema.partial().extend({
  id: z.string().optional(),
  status: z.string().optional(),
});

// ✅ Type inference from schemas
export type CreateOrderData = z.infer<typeof createOrderSchema>;
export type UpdateOrderData = z.infer<typeof updateOrderSchema>;
export type OrderItemData = z.infer<typeof orderItemSchema>;
export type ShippingAddressData = z.infer<typeof shippingAddressSchema>;

// ✅ Validation Functions using Zod
export function validateCreateOrderData(data: unknown): {
  success: boolean;
  data?: CreateOrderData;
  error?: string;
} {
  const result = createOrderSchema.safeParse(data);
  if (result.success) {
    return { success: true, data: result.data };
  }

  const firstError = result.error.issues[0];
  return { success: false, error: firstError?.message || 'ข้อมูลไม่ถูกต้อง' };
}

export function validateUpdateOrderData(data: unknown): {
  success: boolean;
  data?: UpdateOrderData;
  error?: string;
} {
  const result = updateOrderSchema.safeParse(data);
  if (result.success) {
    return { success: true, data: result.data };
  }

  const firstError = result.error.issues[0];
  return { success: false, error: firstError?.message || 'ข้อมูลไม่ถูกต้อง' };
}

// ✅ Type Guards using Zod
export function isValidOrderData(data: unknown): data is CreateOrderData {
  return createOrderSchema.safeParse(data).success;
}

export function isValidUpdateOrderData(data: unknown): data is UpdateOrderData {
  return updateOrderSchema.safeParse(data).success;
}

export function isValidOrderItemData(data: unknown): data is OrderItemData {
  return orderItemSchema.safeParse(data).success;
}

export function isValidShippingAddressData(data: unknown): data is ShippingAddressData {
  return shippingAddressSchema.safeParse(data).success;
}

// ✅ Sanitization Functions
export function sanitizeOrderData(
  data: CreateOrderData | UpdateOrderData,
): CreateOrderData | UpdateOrderData {
  const sanitized = { ...data };

  if (sanitized.note) {
    sanitized.note = sanitized.note.trim();
  }

  if (sanitized.paymentMethod) {
    sanitized.paymentMethod = sanitized.paymentMethod.trim();
  }

  if (sanitized.shippingAddress) {
    sanitized.shippingAddress = { ...sanitized.shippingAddress };
    if (sanitized.shippingAddress.street) {
      sanitized.shippingAddress.street = sanitized.shippingAddress.street.trim();
    }
    if (sanitized.shippingAddress.city) {
      sanitized.shippingAddress.city = sanitized.shippingAddress.city.trim();
    }
    if (sanitized.shippingAddress.state) {
      sanitized.shippingAddress.state = sanitized.shippingAddress.state.trim();
    }
    if (sanitized.shippingAddress.zipCode) {
      sanitized.shippingAddress.zipCode = sanitized.shippingAddress.zipCode.trim();
    }
    if (sanitized.shippingAddress.country) {
      sanitized.shippingAddress.country = sanitized.shippingAddress.country.trim();
    }
  }

  return sanitized;
}
