// ✅ Auth Schema - Zod Validation
import { z } from 'zod';

// ✅ Zod Schemas
export const signinSchema = z.object({
  email: z.email('รูปแบบอีเมลไม่ถูกต้อง'),
  password: z
    .string()
    .min(6, 'รหัสผ่านต้องมีอย่างน้อย 6 ตัวอักษร')
    .max(128, 'รหัสผ่านต้องไม่เกิน 128 ตัวอักษร'),
  rememberMe: z.boolean().optional().default(false),
});

// Login schema (alias for signin)
export const loginSchema = signinSchema;

export const signupSchema = z
  .object({
    email: z.email('รูปแบบอีเมลไม่ถูกต้อง'),
    password: z
      .string()
      .min(6, 'รหัสผ่านต้องมีอย่างน้อย 6 ตัวอักษร')
      .max(128, 'รหัสผ่านต้องไม่เกิน 128 ตัวอักษร')
      .refine(val => /^[a-zA-Z0-9!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?`~]+$/.test(val), {
        message: 'รหัสผ่านต้องใช้เฉพาะภาษาอังกฤษ ตัวเลข และอักขระพิเศษเท่านั้น',
      }),
    firstName: z.string().min(1, 'กรุณากรอกชื่อ').max(50, 'ชื่อต้องไม่เกิน 50 ตัวอักษร').optional(),
    lastName: z
      .string()
      .min(1, 'กรุณากรอกนามสกุล')
      .max(50, 'นามสกุลต้องไม่เกิน 50 ตัวอักษร')
      .optional(),
    confirmPassword: z.string(),
    agreeToTerms: z.boolean().refine(val => val === true, 'กรุณายอมรับเงื่อนไขการใช้งาน'),
  })
  .refine(data => data.password === data.confirmPassword, {
    message: 'รหัสผ่านไม่ตรงกัน',
    path: ['confirmPassword'],
  });

// Register schema (alias for signup)
export const registerSchema = signupSchema;

export const forgotPasswordSchema = z.object({
  email: z.email('รูปแบบอีเมลไม่ถูกต้อง'),
});

export const resetPasswordSchema = z
  .object({
    token: z.string().min(1, 'Token ไม่ถูกต้อง'),
    password: z
      .string()
      .min(6, 'รหัสผ่านต้องมีอย่างน้อย 6 ตัวอักษร')
      .max(128, 'รหัสผ่านต้องไม่เกิน 128 ตัวอักษร'),
    confirmPassword: z.string().optional(),
  })
  .refine(data => !data.confirmPassword || data.password === data.confirmPassword, {
    message: 'รหัสผ่านไม่ตรงกัน',
    path: ['confirmPassword'],
  });

export const verifyEmailSchema = z.object({
  token: z.string().min(1, 'Token ไม่ถูกต้อง'),
});

export const changePasswordSchema = z
  .object({
    currentPassword: z.string().min(1, 'กรุณากรอกรหัสผ่านปัจจุบัน'),
    newPassword: z
      .string()
      .min(6, 'รหัสผ่านต้องมีอย่างน้อย 6 ตัวอักษร')
      .max(128, 'รหัสผ่านต้องไม่เกิน 128 ตัวอักษร')
      .refine(val => /^[a-zA-Z0-9!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?`~]+$/.test(val), {
        message: 'รหัสผ่านต้องใช้เฉพาะภาษาอังกฤษ ตัวเลข และอักขระพิเศษเท่านั้น',
      }),
    confirmNewPassword: z.string(),
  })
  .refine(data => data.newPassword === data.confirmNewPassword, {
    message: 'รหัสผ่านไม่ตรงกัน',
    path: ['confirmNewPassword'],
  });

export const profileUpdateSchema = z.object({
  firstName: z.string().min(1, 'กรุณากรอกชื่อ').max(50, 'ชื่อต้องไม่เกิน 50 ตัวอักษร'),
  lastName: z.string().min(1, 'กรุณากรอกนามสกุล').max(50, 'นามสกุลต้องไม่เกิน 50 ตัวอักษร'),
  email: z.email('รูปแบบอีเมลไม่ถูกต้อง'),
  phone: z
    .string()
    .refine(val => /^[0-9+\-\s()]*$/.test(val), {
      message: 'หมายเลขโทรศัพท์ไม่ถูกต้อง',
    })
    .refine(val => val.length <= 20, {
      message: 'หมายเลขโทรศัพท์ต้องไม่เกิน 20 ตัวอักษร',
    })
    .optional(),
  bio: z.string().max(500, 'ข้อมูลส่วนตัวต้องไม่เกิน 500 ตัวอักษร').optional(),
});

// Password reset schema (alias)
export const passwordResetSchema = forgotPasswordSchema;

// ✅ Type inference from schemas
export type SigninData = z.infer<typeof signinSchema>;
export type SignupData = z.infer<typeof signupSchema>;
export type ForgotPasswordData = z.infer<typeof forgotPasswordSchema>;
export type ResetPasswordData = z.infer<typeof resetPasswordSchema>;
export type VerifyEmailData = z.infer<typeof verifyEmailSchema>;
export type ChangePasswordData = z.infer<typeof changePasswordSchema>;
export type ProfileUpdateData = z.infer<typeof profileUpdateSchema>;

// Auth Response Types
export interface AuthResponse {
  success: boolean;
  data?: {
    user: any; // Use any for now to avoid import issues
    token: string;
    refreshToken: string;
    sessionId?: string;
  };
  error?: string;
  message?: string;
}

export interface RefreshTokenResponse {
  success: boolean;
  data?: {
    token: string;
    refreshToken: string;
    user: any; // Use any for now to avoid import issues
  };
  error?: string;
}

// ✅ Validation Functions using Zod
export function validateSigninData(data: unknown): {
  success: boolean;
  data?: SigninData;
  error?: string;
} {
  const result = signinSchema.safeParse(data);
  if (result.success) {
    return { success: true, data: result.data };
  }

  const firstError = result.error.issues[0];
  return { success: false, error: firstError?.message || 'ข้อมูลไม่ถูกต้อง' };
}

export function validateSignupData(data: unknown): {
  success: boolean;
  data?: SignupData;
  error?: string;
} {
  const result = signupSchema.safeParse(data);
  if (result.success) {
    return { success: true, data: result.data };
  }

  const firstError = result.error.issues[0];
  return { success: false, error: firstError?.message || 'ข้อมูลไม่ถูกต้อง' };
}

export function validateForgotPasswordData(data: unknown): {
  success: boolean;
  data?: ForgotPasswordData;
  error?: string;
} {
  const result = forgotPasswordSchema.safeParse(data);
  if (result.success) {
    return { success: true, data: result.data };
  }

  const firstError = result.error.issues[0];
  return { success: false, error: firstError?.message || 'ข้อมูลไม่ถูกต้อง' };
}

export function validateResetPasswordData(data: unknown): {
  success: boolean;
  data?: ResetPasswordData;
  error?: string;
} {
  const result = resetPasswordSchema.safeParse(data);
  if (result.success) {
    return { success: true, data: result.data };
  }

  const firstError = result.error.issues[0];
  return { success: false, error: firstError?.message || 'ข้อมูลไม่ถูกต้อง' };
}

export function validateChangePasswordData(data: unknown): {
  success: boolean;
  data?: ChangePasswordData;
  error?: string;
} {
  const result = changePasswordSchema.safeParse(data);
  if (result.success) {
    return { success: true, data: result.data };
  }

  const firstError = result.error.issues[0];
  return { success: false, error: firstError?.message || 'ข้อมูลไม่ถูกต้อง' };
}

export function validateProfileUpdateData(data: unknown): {
  success: boolean;
  data?: ProfileUpdateData;
  error?: string;
} {
  const result = profileUpdateSchema.safeParse(data);
  if (result.success) {
    return { success: true, data: result.data };
  }

  const firstError = result.error.issues[0];
  return { success: false, error: firstError?.message || 'ข้อมูลไม่ถูกต้อง' };
}

// Validation functions with detailed errors (for forms)
export function validateLoginForm(data: unknown) {
  const result = loginSchema.safeParse(data);
  if (result.success) {
    return {
      success: true,
      data: result.data,
      errors: {},
    };
  }

  const errors: Record<string, string> = {};
  result.error.issues.forEach(issue => {
    const path = issue.path.join('.');
    if (path) {
      errors[path] = issue.message;
    }
  });

  return {
    success: false,
    errors,
  };
}

export function validateRegisterForm(data: unknown) {
  const result = registerSchema.safeParse(data);
  if (result.success) {
    return {
      success: true,
      data: result.data,
      errors: {},
    };
  }

  const errors: Record<string, string> = {};
  result.error.issues.forEach(issue => {
    const path = issue.path.join('.');
    if (path) {
      errors[path] = issue.message;
    }
  });

  return {
    success: false,
    errors,
  };
}

// Alias functions
export const validateSigninForm = validateLoginForm;
export const validateSignupForm = validateRegisterForm;

// ✅ Helper Functions
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

export function sanitizeAuthData<T extends Record<string, unknown>>(data: T): T {
  const sanitized = { ...data } as T;

  // Trim string fields
  Object.keys(sanitized).forEach(key => {
    if (typeof sanitized[key] === 'string') {
      (sanitized as Record<string, unknown>)[key] = (sanitized[key] as string).trim();
    }
  });

  return sanitized;
}
