// reCAPTCHA Configuration
export const recaptchaConfig = {
  // Site Key สำหรับ frontend
  siteKey: import.meta.env.VITE_RECAPTCHA_SITE_KEY || '6LcRobIqAAAAAAnGAodZL6KoxsUzgyV-YDf2gbVy',

  // Secret Key สำหรับ backend (ไม่ควรใช้ใน frontend)
  // secretKey: import.meta.env.VITE_RECAPTCHA_SECRET_KEY,

  // Test keys สำหรับ development
  testSiteKey: '6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI',
  testSecretKey: '6LeIxAcTAAAAAGG-vFI1TnRWxMZNFuojJ4WifJWe',

  // Default settings
  version: 'v2' as const,
  theme: 'light' as const,
  size: 'normal' as const,
} as const;

// Helper function เพื่อตรวจสอบว่าใช้ test key หรือไม่
export function isTestKey(siteKey: string): boolean {
  return siteKey === recaptchaConfig.testSiteKey;
}

// Helper function เพื่อ get site key ที่ถูกต้อง
export function getSiteKey(): string {
  const envKey = import.meta.env.VITE_RECAPTCHA_SITE_KEY;

  // ใช้ env key หรือ fallback ไปที่ real key
  const finalKey = envKey || recaptchaConfig.siteKey;

  console.log('[reCAPTCHA Config] getSiteKey():', {
    envKey: envKey ? envKey.substring(0, 15) + '...' : 'NOT_SET',
    finalKey: finalKey.substring(0, 15) + '...',
    isDev: import.meta.env.DEV,
  });

  return finalKey;
}

// Debug function
export function debugRecaptchaConfig(): void {
  console.log('[reCAPTCHA Config] Debug info:', {
    envSiteKey: import.meta.env.VITE_RECAPTCHA_SITE_KEY ? 'SET' : 'NOT_SET',
    envSecretKey: import.meta.env.VITE_RECAPTCHA_SECRET_KEY ? 'SET' : 'NOT_SET',
    isDev: import.meta.env.DEV,
    isProd: import.meta.env.PROD,
    mode: import.meta.env.MODE,
    finalSiteKey: getSiteKey().substring(0, 10) + '...',
    isTestKey: isTestKey(getSiteKey()),
  });
}
