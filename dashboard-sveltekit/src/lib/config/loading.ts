import { env } from '$env/dynamic/public';

// Loading configuration interface
export interface LoadingConfig {
  // Basic settings
  type: 'spinner' | 'dots' | 'bars' | 'pulse' | 'skeleton' | 'progress';
  size: 'sm' | 'md' | 'lg' | 'xl';
  color: 'primary' | 'secondary' | 'accent' | 'info' | 'success' | 'warning' | 'error';

  // Display settings
  message: string;
  showProgress: boolean;
  overlay: boolean;
  blur: boolean;

  // Animation settings
  animation: 'fade' | 'slide-up' | 'scale' | 'none';
  duration: number;

  // Control settings
  disabled: boolean;
  minDisplayTime: number;

  // Advanced settings
  autoHide: boolean;
  autoHideDelay: number;
  showPercentage: boolean;
  customMessages: Record<string, string>;
}

// Default configuration
const defaultConfig: LoadingConfig = {
  type: 'spinner',
  size: 'lg',
  color: 'primary',
  message: 'กำลังโหลด...',
  showProgress: false,
  overlay: true,
  blur: false,
  animation: 'fade',
  duration: 300,
  disabled: false,
  minDisplayTime: 500,
  autoHide: false,
  autoHideDelay: 3000,
  showPercentage: true,
  customMessages: {
    loading: 'กำลังโหลด...',
    processing: 'กำลังประมวลผล...',
    saving: 'กำลังบันทึก...',
    uploading: 'กำลังอัปโหลด...',
    downloading: 'กำลังดาวน์โหลด...',
    connecting: 'กำลังเชื่อมต่อ...',
    authenticating: 'กำลังตรวจสอบสิทธิ์...',
    initializing: 'กำลังเริ่มต้น...',
  },
};

// Parse environment variables with fallbacks
const parseEnvBoolean = (value: string | undefined, defaultValue: boolean): boolean => {
  if (!value) return defaultValue;
  return value.toLowerCase() === 'true' || value === '1';
};

const parseEnvNumber = (value: string | undefined, defaultValue: number): number => {
  if (!value) return defaultValue;
  const parsed = parseInt(value, 10);
  return isNaN(parsed) ? defaultValue : parsed;
};

const parseEnvString = <T extends string>(
  value: string | undefined,
  allowedValues: readonly T[],
  defaultValue: T,
): T => {
  if (!value) return defaultValue;
  return allowedValues.includes(value as T) ? (value as T) : defaultValue;
};

// Environment-based configuration
export const loadingConfig: LoadingConfig = {
  // Basic settings from env
  type: parseEnvString(
    env.PUBLIC_LOADING_TYPE,
    ['spinner', 'dots', 'bars', 'pulse', 'skeleton', 'progress'] as const,
    defaultConfig.type,
  ),

  size: parseEnvString(
    env.PUBLIC_LOADING_SIZE,
    ['sm', 'md', 'lg', 'xl'] as const,
    defaultConfig.size,
  ),

  color: parseEnvString(
    env.PUBLIC_LOADING_COLOR,
    ['primary', 'secondary', 'accent', 'info', 'success', 'warning', 'error'] as const,
    defaultConfig.color,
  ),

  // Display settings from env
  message: env.PUBLIC_LOADING_MESSAGE || defaultConfig.message,
  showProgress: parseEnvBoolean(env.PUBLIC_LOADING_SHOW_PROGRESS, defaultConfig.showProgress),
  overlay: parseEnvBoolean(env.PUBLIC_LOADING_OVERLAY, defaultConfig.overlay),
  blur: parseEnvBoolean(env.PUBLIC_LOADING_BLUR, defaultConfig.blur),

  // Animation settings from env
  animation: parseEnvString(
    env.PUBLIC_LOADING_ANIMATION,
    ['fade', 'slide-up', 'scale', 'none'] as const,
    defaultConfig.animation,
  ),

  duration: parseEnvNumber(env.PUBLIC_LOADING_DURATION, defaultConfig.duration),

  // Control settings from env
  disabled: parseEnvBoolean(env.PUBLIC_LOADING_DISABLED, defaultConfig.disabled),
  minDisplayTime: parseEnvNumber(env.PUBLIC_LOADING_MIN_DISPLAY_TIME, defaultConfig.minDisplayTime),

  // Advanced settings from env
  autoHide: parseEnvBoolean(env.PUBLIC_LOADING_AUTO_HIDE, defaultConfig.autoHide),
  autoHideDelay: parseEnvNumber(env.PUBLIC_LOADING_AUTO_HIDE_DELAY, defaultConfig.autoHideDelay),
  showPercentage: parseEnvBoolean(env.PUBLIC_LOADING_SHOW_PERCENTAGE, defaultConfig.showPercentage),

  // Custom messages (can be extended via env)
  customMessages: {
    ...defaultConfig.customMessages,
    // Allow override via env variables
    loading: env.PUBLIC_LOADING_MSG_LOADING || defaultConfig.customMessages.loading,
    processing: env.PUBLIC_LOADING_MSG_PROCESSING || defaultConfig.customMessages.processing,
    saving: env.PUBLIC_LOADING_MSG_SAVING || defaultConfig.customMessages.saving,
    uploading: env.PUBLIC_LOADING_MSG_UPLOADING || defaultConfig.customMessages.uploading,
    downloading: env.PUBLIC_LOADING_MSG_DOWNLOADING || defaultConfig.customMessages.downloading,
    connecting: env.PUBLIC_LOADING_MSG_CONNECTING || defaultConfig.customMessages.connecting,
    authenticating: env.PUBLIC_LOADING_MSG_AUTHENTICATING || defaultConfig.customMessages.authenticating,
    initializing: env.PUBLIC_LOADING_MSG_INITIALIZING || defaultConfig.customMessages.initializing,
  },
};

// Helper functions for loading states
export const createLoadingState = () => {
  let isLoading = $state(false);
  let progress = $state(0);
  let message = $state(loadingConfig.message);
  let startTime = 0;

  const show = (msg?: string) => {
    if (loadingConfig.disabled) return;

    startTime = Date.now();
    message = msg || loadingConfig.message;
    progress = 0;
    isLoading = true;
  };

  const hide = async () => {
    if (!isLoading) return;

    const elapsed = Date.now() - startTime;
    const remaining = Math.max(0, loadingConfig.minDisplayTime - elapsed);

    if (remaining > 0) {
      await new Promise(resolve => setTimeout(resolve, remaining));
    }

    isLoading = false;
    progress = 0;
  };

  const setProgress = (value: number, msg?: string) => {
    progress = Math.max(0, Math.min(100, value));
    if (msg) message = msg;

    if (progress >= 100 && loadingConfig.autoHide) {
      setTimeout(() => hide(), loadingConfig.autoHideDelay);
    }
  };

  const setMessage = (msg: string) => {
    message = msg;
  };

  return {
    get isLoading() {
      return isLoading;
    },
    get progress() {
      return progress;
    },
    get message() {
      return message;
    },
    show,
    hide,
    setProgress,
    setMessage,
  };
};

// Preset configurations for common use cases
export const loadingPresets = {
  // Fast loading for quick operations
  fast: {
    type: 'spinner' as const,
    size: 'md' as const,
    animation: 'fade' as const,
    duration: 200,
    minDisplayTime: 300,
    message: 'กำลังโหลด...',
  },

  // Progress loading for file operations
  progress: {
    type: 'progress' as const,
    size: 'lg' as const,
    showProgress: true,
    showPercentage: true,
    animation: 'scale' as const,
    duration: 400,
    message: 'กำลังประมวลผล...',
  },

  // Skeleton loading for content
  skeleton: {
    type: 'skeleton' as const,
    size: 'md' as const,
    animation: 'fade' as const,
    duration: 300,
    overlay: false,
    message: '',
  },

  // Minimal loading
  minimal: {
    type: 'dots' as const,
    size: 'sm' as const,
    animation: 'none' as const,
    overlay: false,
    blur: false,
    message: '',
  },

  // Full screen loading
  fullscreen: {
    type: 'pulse' as const,
    size: 'xl' as const,
    animation: 'slide-up' as const,
    duration: 500,
    overlay: true,
    blur: true,
    message: 'กำลังเริ่มต้นระบบ...',
  },
};

// Export types for TypeScript support
export type LoadingType = LoadingConfig['type'];
export type LoadingSize = LoadingConfig['size'];
export type LoadingColor = LoadingConfig['color'];
export type LoadingAnimation = LoadingConfig['animation'];
export type LoadingPreset = keyof typeof loadingPresets;
