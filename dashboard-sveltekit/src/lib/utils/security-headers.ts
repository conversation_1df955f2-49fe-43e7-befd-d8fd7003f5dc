import type { RequestEvent } from '@sveltejs/kit';

/**
 * ✅ Security Headers Configuration
 */

export interface SecurityHeadersConfig {
  contentSecurityPolicy?: string;
  strictTransportSecurity?: string;
  xFrameOptions?: string;
  xContentTypeOptions?: string;
  xXSSProtection?: string;
  referrerPolicy?: string;
  permissionsPolicy?: string;
}

// Default security headers
export const DEFAULT_SECURITY_HEADERS: SecurityHeadersConfig = {
  // Content Security Policy
  contentSecurityPolicy: [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://unpkg.com https://www.google.com https://www.gstatic.com",
    "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdn.jsdelivr.net https://www.gstatic.com",
    "font-src 'self' https://fonts.gstatic.com https://cdn.jsdelivr.net https://www.gstatic.com",
    "img-src 'self' data: https: blob: https://www.google.com https://www.gstatic.com",
    "connect-src 'self' https://api.cloudinary.com https://api.iconify.design https://api.unisvg.com https://api.simplesvg.com https://www.google.com",
    "frame-src 'self' https://www.google.com https://www.gstatic.com",
    "media-src 'self' https:",
    "object-src 'none'",
    "base-uri 'self'",
    "form-action 'self'",
    "frame-ancestors 'none'",
    'upgrade-insecure-requests',
  ].join('; '),

  // HTTPS enforcement
  strictTransportSecurity: 'max-age=31536000; includeSubDomains; preload',

  // Frame protection
  xFrameOptions: 'DENY',

  // MIME type sniffing protection
  xContentTypeOptions: 'nosniff',

  // XSS protection
  xXSSProtection: '1; mode=block',

  // Referrer policy
  referrerPolicy: 'strict-origin-when-cross-origin',

  // Permissions policy
  permissionsPolicy: [
    'camera=()',
    'microphone=()',
    'geolocation=()',
    'payment=()',
    'usb=()',
    'magnetometer=()',
    'gyroscope=()',
    'accelerometer=()',
  ].join(', '),
};

/**
 * Apply security headers to response
 */
export function applySecurityHeaders(
  event: RequestEvent,
  config: SecurityHeadersConfig = DEFAULT_SECURITY_HEADERS,
): void {
  const { setHeaders } = event;

  // Only apply HSTS in production with HTTPS
  if (config.strictTransportSecurity && import.meta.env.NODE_ENV === 'production') {
    setHeaders({
      'Strict-Transport-Security': config.strictTransportSecurity,
    });
  }

  // Apply other security headers
  const headers: Record<string, string> = {};

  if (config.contentSecurityPolicy) {
    // ✅ In development, make CSP more permissive for better DX
    if (import.meta.env.NODE_ENV === 'development') {
      const devCSP = config.contentSecurityPolicy
        .replace(
          "connect-src 'self' https://api.cloudinary.com https://api.iconify.design https://api.unisvg.com https://api.simplesvg.com https://www.google.com",
          "connect-src 'self' https://api.cloudinary.com https://api.iconify.design https://api.unisvg.com https://api.simplesvg.com https://www.google.com ws: wss:",
        )
        .replace(
          "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://unpkg.com https://www.google.com https://www.gstatic.com",
          "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://unpkg.com https://www.google.com https://www.gstatic.com 'unsafe-hashes'",
        )
        .replace(
          "frame-src 'self' https://www.google.com https://www.gstatic.com",
          "frame-src 'self' https://www.google.com https://www.gstatic.com",
        );

      headers['Content-Security-Policy'] = devCSP;
    }
    else {
      headers['Content-Security-Policy'] = config.contentSecurityPolicy;
    }
  }

  if (config.xFrameOptions) {
    headers['X-Frame-Options'] = config.xFrameOptions;
  }

  if (config.xContentTypeOptions) {
    headers['X-Content-Type-Options'] = config.xContentTypeOptions;
  }

  if (config.xXSSProtection) {
    headers['X-XSS-Protection'] = config.xXSSProtection;
  }

  if (config.referrerPolicy) {
    headers['Referrer-Policy'] = config.referrerPolicy;
  }

  if (config.permissionsPolicy) {
    headers['Permissions-Policy'] = config.permissionsPolicy;
  }

  // Additional security headers
  headers['X-Powered-By'] = ''; // Remove server signature
  headers['Server'] = ''; // Remove server signature

  setHeaders(headers);
}

/**
 * Get CSP nonce for inline scripts/styles
 */
export function generateCSPNonce(): string {
  return Buffer.from(crypto.getRandomValues(new Uint8Array(16))).toString('base64');
}

/**
 * Create CSP with nonce
 */
export function createCSPWithNonce(nonce: string): string {
  return DEFAULT_SECURITY_HEADERS.contentSecurityPolicy!.replace(
    "'unsafe-inline'",
    `'nonce-${nonce}'`,
  );
}
