# Validation Utility สำหรับ Svelte 5 + Zod

ระบบ validation ที่ยืดหยุ่นและใช้งานง่าย คล้าย vuelidate แต่ใช้ zod และรองรับ Svelte 5 runes

## Features

- ✅ Real-time validation ขณะพิมพ์และคลิกจุดอื่น
- ✅ Debounced validation เพื่อประสิทธิภาพ
- ✅ รองรับ Svelte 5 runes
- ✅ API คล้าย vuelidate
- ✅ ใช้ zod สำหรับ schema validation
- ✅ TypeScript support เต็มรูปแบบ
- ✅ Flexible configuration options

## การติดตั้ง

```bash
npm install zod
```

## การใช้งาน

### 1. Reactive Validator (แนะนำ)

```typescript
import { createReactiveValidator } from '$lib/utils/validation';
import { z } from 'zod';

// สร้าง schema
const userSchema = z.object({
  name: z.string().min(2, 'ชื่อต้องมีอย่างน้อย 2 ตัวอักษร'),
  email: z.string().email('รูปแบบอีเมลไม่ถูกต้อง'),
  age: z.number().min(18, 'อายุต้องมากกว่า 18 ปี').optional(),
});

// สร้าง initial data
const initialData = {
  name: '',
  email: '',
  age: undefined,
};

// สร้าง validator
const v = createReactiveValidator(userSchema, initialData, {
  validateOnInput: true,
  validateOnBlur: true,
  debounceMs: 300,
  showErrorsOnlyAfterTouch: true,
});
```

### 2. ใช้ใน Svelte Component

```svelte
<script lang="ts">
  import { createReactiveValidator } from '$lib/utils/validation';
  import { userSchema, type UserData } from './schemas';

  const initialData: UserData = {
    name: '',
    email: '',
    age: undefined
  };

  const v = createReactiveValidator(userSchema, initialData);

  function handleSubmit() {
    if (v.$validate()) {
      console.log('Form is valid!', v.$data);
      // ส่งข้อมูล
    } else {
      console.log('Form has errors:', v.$errors);
    }
  }

  // Cleanup เมื่อ component ถูก destroy
  onDestroy(() => {
    v.$cleanup();
  });
</script>

<form on:submit|preventDefault={handleSubmit}>
  <!-- Input fields -->
  <input
    bind:value={v.fields.name.value}
    on:blur={v.fields.name.onBlur}
    class:error={v.fields.name.hasError}
  />
  {#if v.fields.name.error}
    <span class="error">{v.fields.name.error}</span>
  {/if}

  <input
    type="email"
    bind:value={v.fields.email.value}
    on:blur={v.fields.email.onBlur}
    class:error={v.fields.email.hasError}
  />
  {#if v.fields.email.error}
    <span class="error">{v.fields.email.error}</span>
  {/if}

  <button type="submit" disabled={!v.$valid}>
    Submit
  </button>
</form>

<!-- Debug info -->
<div class="debug">
  <p>Form Valid: {v.$valid}</p>
  <p>Form Dirty: {v.$dirty}</p>
  <p>Errors: {JSON.stringify(v.$errors)}</p>
</div>
```

## API Reference

### createReactiveValidator(schema, initialData, options?)

สร้าง reactive validator ที่ทำงานคล้าย vuelidate

#### Parameters

- `schema`: Zod schema object
- `initialData`: ข้อมูลเริ่มต้น
- `options`: การตั้งค่าเพิ่มเติม

#### Options

```typescript
interface ValidationOptions {
  validateOnInput?: boolean; // validate ขณะพิมพ์ (default: true)
  validateOnBlur?: boolean; // validate เมื่อคลิกจุดอื่น (default: true)
  validateOnMount?: boolean; // validate เมื่อ component mount (default: false)
  debounceMs?: number; // เวลา debounce ในมิลลิวินาที (default: 300)
  showErrorsOnlyAfterTouch?: boolean; // แสดง error หลังจาก touch เท่านั้น (default: true)
}
```

#### Return Value

```typescript
{
  // Fields object (คล้าย vuelidate)
  fields: {
    [fieldName]: {
      value: any;           // ค่าของ field
      error: string;        // error message
      hasError: boolean;    // มี error หรือไม่
      touched: boolean;     // ถูก touch แล้วหรือไม่
      dirty: boolean;       // มีการเปลี่ยนแปลงหรือไม่
      valid: boolean;       // valid หรือไม่
      invalid: boolean;     // invalid หรือไม่
      onInput: (value) => void;  // handler สำหรับ input
      onBlur: () => void;        // handler สำหรับ blur
      validate: () => string | null; // validate manual
      clearError: () => void;    // ลบ error
    }
  },
  
  // Form-level properties
  $valid: boolean;         // form valid หรือไม่
  $invalid: boolean;       // form invalid หรือไม่
  $dirty: boolean;         // form มีการเปลี่ยนแปลงหรือไม่
  $pristine: boolean;      // form ยังไม่มีการเปลี่ยนแปลง
  $errors: Record<string, string>; // errors ทั้งหมด
  $hasErrors: boolean;     // มี errors หรือไม่
  $data: T;               // ข้อมูลปัจจุบัน
  
  // Methods
  $validate: () => boolean;     // validate ทั้ง form
  $reset: () => void;          // reset form
  $setErrors: (errors) => void; // set errors จากภายนอก
  $cleanup: () => void;        // cleanup timers
}
```

### createValidator(schema, options?)

สร้าง simple validator สำหรับการใช้งานแบบ manual

```typescript
const validator = createValidator(schema);

// Validate field เดียว
const error = validator.validateField('email', '<EMAIL>');

// Validate ทั้ง form
const errors = validator.validateForm(formData);
```

### createFormValidator(schema, initialData, options?)

สร้าง form validator ที่มี reactive store

```typescript
const formValidator = createFormValidator(schema, initialData);

// ใช้ store
formValidator.state.subscribe(state => {
  console.log('Form state:', state);
});

// Update field
formValidator.updateField('email', '<EMAIL>');
```

## ตัวอย่างการใช้งานขั้นสูง

### 1. Custom Validation Messages

```typescript
const schema = z.object({
  email: z.string().email('กรุณากรอกอีเมลให้ถูกต้อง'),
  password: z.string()
    .min(8, 'รหัสผ่านต้องมีอย่างน้อย 8 ตัวอักษร')
    .regex(/[A-Z]/, 'รหัสผ่านต้องมีตัวพิมพ์ใหญ่อย่างน้อย 1 ตัว')
    .regex(/[0-9]/, 'รหัสผ่านต้องมีตัวเลขอย่างน้อย 1 ตัว'),
});
```

### 2. Conditional Validation

```typescript
const schema = z.object({
  hasAddress: z.boolean(),
  address: z.string().optional(),
}).refine(data => {
  if (data.hasAddress && !data.address) {
    return false;
  }
  return true;
}, {
  message: 'กรุณากรอกที่อยู่',
  path: ['address'],
});
```

### 3. Cross-field Validation

```typescript
const schema = z.object({
  password: z.string().min(6),
  confirmPassword: z.string(),
}).refine(data => data.password === data.confirmPassword, {
  message: 'รหัสผ่านไม่ตรงกัน',
  path: ['confirmPassword'],
});
```

## Best Practices

1. **ใช้ debouncing**: ตั้งค่า `debounceMs` ให้เหมาะสมเพื่อประสิทธิภาพ
2. **Cleanup**: เรียก `$cleanup()` เมื่อ component ถูก destroy
3. **Show errors after touch**: ใช้ `showErrorsOnlyAfterTouch: true` เพื่อ UX ที่ดี
4. **Type safety**: ใช้ TypeScript เพื่อความปลอดภัย
5. **Reusable schemas**: แยก schema ออกเป็นไฟล์แยก

## Migration จาก vuelidate

```javascript
// vuelidate
const v$ = useVuelidate(rules, formData);

// เปลี่ยนเป็น
const v = createReactiveValidator(zodSchema, initialData);

// การใช้งานคล้ายกัน
v.fields.email.value; // แทน v$.email.$model
v.fields.email.error; // แทน v$.email.$error
v.fields.email.touched; // แทน v$.email.$touched
v.$valid; // แทน v$.$invalid
```
