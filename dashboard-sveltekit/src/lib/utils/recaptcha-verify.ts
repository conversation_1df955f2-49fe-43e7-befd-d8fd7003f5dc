/**
 * ✅ Enhanced reCAPTCHA Verification Utility with v3 Support & Monitoring
 * ใช้สำหรับตรวจสอบ reCAPTCHA token ใน SvelteKit server actions
 */

interface RecaptchaResponse {
  success: boolean;
  score?: number;
  action?: string;
  challenge_ts?: string;
  hostname?: string;
  'error-codes'?: string[];
}

interface RecaptchaStats {
  total: number;
  success: number;
  failed: number;
  lowScore: number;
  averageScore: number;
  versions: {
    v2: { total: number; success: number; };
    v3: { total: number; success: number; averageScore: number; };
  };
  actions: Record<
    string,
    {
      total: number;
      success: number;
      averageScore: number;
      versions: { v2: number; v3: number; };
    }
  >;
}

interface RecaptchaVerificationResult {
  success: boolean;
  score?: number;
  action?: string;
  reason?: string;
  errorCodes?: string[];
}

// In-memory stats storage (ในการใช้งานจริงควรใช้ Redis หรือ Database)
let recaptchaStats: RecaptchaStats = {
  total: 0,
  success: 0,
  failed: 0,
  lowScore: 0,
  averageScore: 0,
  versions: {
    v2: { total: 0, success: 0 },
    v3: { total: 0, success: 0, averageScore: 0 },
  },
  actions: {},
};

/**
 * อัพเดทสถิติ reCAPTCHA พร้อม version tracking
 */
function updateRecaptchaStats(
  result: RecaptchaVerificationResult,
  action: string = 'unknown',
  detectedVersion?: 'v2' | 'v3',
) {
  recaptchaStats.total++;

  if (result.success) {
    recaptchaStats.success++;
  }
  else {
    recaptchaStats.failed++;
    if (result.reason === 'low_score') {
      recaptchaStats.lowScore++;
    }
  }

  // อัพเดทสถิติตาม version
  if (detectedVersion) {
    recaptchaStats.versions[detectedVersion].total++;
    if (result.success) {
      recaptchaStats.versions[detectedVersion].success++;
    }

    // คำนวณ average score สำหรับ v3
    if (detectedVersion === 'v3' && result.score !== undefined) {
      const v3Stats = recaptchaStats.versions.v3;
      v3Stats.averageScore = (v3Stats.averageScore * (v3Stats.total - 1) + result.score) / v3Stats.total;
    }
  }

  // อัพเดทสถิติตาม action
  if (!recaptchaStats.actions[action]) {
    recaptchaStats.actions[action] = {
      total: 0,
      success: 0,
      averageScore: 0,
      versions: { v2: 0, v3: 0 },
    };
  }

  const actionStats = recaptchaStats.actions[action];
  actionStats.total++;

  if (result.success) {
    actionStats.success++;
  }

  // อัพเดท version count ตาม action
  if (detectedVersion) {
    actionStats.versions[detectedVersion]++;
  }

  // คำนวณ average score
  if (result.score !== undefined) {
    const totalScores = recaptchaStats.total;
    recaptchaStats.averageScore = (recaptchaStats.averageScore * (totalScores - 1) + result.score) / totalScores;

    const actionTotalScores = actionStats.total;
    actionStats.averageScore = (actionStats.averageScore * (actionTotalScores - 1) + result.score) / actionTotalScores;
  }
}

/**
 * ตรวจจับ version ของ reCAPTCHA จาก token
 */
function detectRecaptchaVersion(token: string): 'v2' | 'v3' {
  // v3 tokens มักจะมี format ที่แตกต่างและยาวกว่า
  // v2 tokens มักจะสั้นกว่า
  // แต่ในกรณีนี้เราต้องการให้ v2 เป็นค่าเริ่มต้นถ้าไม่แน่ใจ
  if (token.length > 2000) {
    return 'v3';
  }
  // สำหรับ v2 tokens ที่สั้นกว่า
  return 'v2';
}

/**
 * ตรวจสอบ reCAPTCHA token กับ Google API (Enhanced Version - รองรับทั้ง v2 และ v3)
 */
export async function verifyRecaptcha(
  token: string,
  remoteIP?: string,
  expectedAction?: string,
  minScore: number = 0.5,
  expectedVersion?: 'v2' | 'v3',
): Promise<RecaptchaVerificationResult> {
  const secretKey = import.meta.env.VITE_RECAPTCHA_SECRET_KEY;

  if (!secretKey) {
    console.error('[reCAPTCHA] VITE_RECAPTCHA_SECRET_KEY not configured');
    const result = { success: false, reason: 'config_error' };
    updateRecaptchaStats(result, expectedAction || 'unknown');
    return result;
  }

  if (!token) {
    console.warn('[reCAPTCHA] No token provided');
    const result = { success: false, reason: 'no_token' };
    updateRecaptchaStats(result, expectedAction || 'unknown');
    return result;
  }

  try {
    // สร้าง form data สำหรับ Google reCAPTCHA API
    const formData = new FormData();
    formData.append('secret', secretKey);
    formData.append('response', token);

    if (remoteIP) {
      formData.append('remoteip', remoteIP);
    }

    // ใช้ expectedVersion ถ้ามี หรือตรวจจับอัตโนมัติ
    const detectedVersion = expectedVersion || detectRecaptchaVersion(token);

    console.log('[reCAPTCHA] Verifying token with Google API', {
      detectedVersion,
      expectedVersion,
      expectedAction,
      minScore,
      hasRemoteIP: !!remoteIP,
    });

    // เรียก Google reCAPTCHA verification API
    const response = await fetch('https://www.google.com/recaptcha/api/siteverify', {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      console.error('[reCAPTCHA] Google API error:', response.status, response.statusText);
      const result = { success: false, reason: 'api_error' };
      updateRecaptchaStats(result, expectedAction || 'unknown', detectedVersion);
      return result;
    }

    const apiResult: RecaptchaResponse = await response.json();

    console.log('[reCAPTCHA] Verification result:', {
      success: apiResult.success,
      score: apiResult.score,
      action: apiResult.action,
      hostname: apiResult.hostname,
      errorCodes: apiResult['error-codes'],
    });

    if (!apiResult.success) {
      console.warn('[reCAPTCHA] Verification failed:', {
        errorCodes: apiResult['error-codes'],
        hostname: apiResult.hostname,
      });
      const result = {
        success: false,
        reason: 'verification_failed',
        errorCodes: apiResult['error-codes'],
      };
      updateRecaptchaStats(result, expectedAction || 'unknown', detectedVersion);
      return result;
    }

    // ตรวจสอบ action (สำหรับ v3 เท่านั้น)
    if (detectedVersion === 'v3' && expectedAction && apiResult.action !== expectedAction) {
      console.warn('[reCAPTCHA] Action mismatch (v3):', {
        expected: expectedAction,
        received: apiResult.action,
      });
      const result = {
        success: false,
        reason: 'action_mismatch',
        action: apiResult.action,
      };
      updateRecaptchaStats(result, expectedAction || 'unknown', detectedVersion);
      return result;
    }

    // ตรวจสอบ score (สำหรับ v3 เท่านั้น)
    if (detectedVersion === 'v3' && apiResult.score !== undefined) {
      if (apiResult.score < minScore) {
        console.warn('[reCAPTCHA] Score too low (v3):', {
          score: apiResult.score,
          minScore,
          action: apiResult.action,
        });
        const result = {
          success: false,
          reason: 'low_score',
          score: apiResult.score,
          action: apiResult.action,
        };
        updateRecaptchaStats(result, expectedAction || apiResult.action || 'unknown');
        return result;
      }
    }

    // สำหรับ v2 - ไม่ต้องตรวจสอบ score หรือ action
    if (detectedVersion === 'v2') {
      console.log('[reCAPTCHA] v2 verification successful - no score/action check needed');
    }

    console.log('[reCAPTCHA] Verification successful');
    const result = {
      success: true,
      score: apiResult.score,
      action: apiResult.action,
    };
    updateRecaptchaStats(result, expectedAction || apiResult.action || 'unknown');
    return result;
  }
  catch (error) {
    console.error(
      '[reCAPTCHA] Verification error:',
      error instanceof Error ? error.message : 'Unknown error',
    );
    const result = { success: false, reason: 'network_error' };
    updateRecaptchaStats(result, expectedAction || 'unknown');
    return result;
  }
}

/**
 * ตรวจสอบ reCAPTCHA token (Legacy function for backward compatibility)
 */
export async function verifyRecaptchaLegacy(token: string, remoteIP?: string): Promise<boolean> {
  const result = await verifyRecaptcha(token, remoteIP);
  return result.success;
}

/**
 * ตรวจสอบว่า reCAPTCHA ถูกเปิดใช้งานหรือไม่
 */
export function isRecaptchaEnabled(): boolean {
  return !!(import.meta.env.VITE_RECAPTCHA_SECRET_KEY && import.meta.env.VITE_RECAPTCHA_SITE_KEY);
}

/**
 * ข้อมูลการตั้งค่า reCAPTCHA
 */
export function getRecaptchaConfig() {
  return {
    enabled: isRecaptchaEnabled(),
    siteKey: import.meta.env.VITE_RECAPTCHA_SITE_KEY,
    hasSecretKey: !!import.meta.env.VITE_RECAPTCHA_SECRET_KEY,
  };
}

/**
 * ดึงสถิติ reCAPTCHA
 */
export function getRecaptchaStats(): RecaptchaStats {
  return { ...recaptchaStats };
}

/**
 * รีเซ็ตสถิติ reCAPTCHA
 */
export function resetRecaptchaStats(): void {
  recaptchaStats = {
    total: 0,
    success: 0,
    failed: 0,
    lowScore: 0,
    averageScore: 0,
    versions: {
      v2: { total: 0, success: 0 },
      v3: { total: 0, success: 0, averageScore: 0 },
    },
    actions: {},
  };
}

/**
 * คำนวณ success rate
 */
export function getRecaptchaSuccessRate(): number {
  if (recaptchaStats.total === 0) return 0;
  return (recaptchaStats.success / recaptchaStats.total) * 100;
}

/**
 * ดึงสถิติตาม action
 */
export function getRecaptchaActionStats(action: string) {
  const actionStats = recaptchaStats.actions[action];
  if (!actionStats) return null;

  return {
    ...actionStats,
    successRate: actionStats.total > 0 ? (actionStats.success / actionStats.total) * 100 : 0,
  };
}

/**
 * Helper function สำหรับการใช้งาน reCAPTCHA v2 (default)
 */
export async function verifyRecaptchaV2(
  token: string,
  remoteIP?: string,
  action: string = 'unknown',
): Promise<RecaptchaVerificationResult> {
  return await verifyRecaptcha(token, remoteIP, action, 0, 'v2'); // minScore = 0 for v2, force v2
}

/**
 * Helper function สำหรับการใช้งาน reCAPTCHA v3
 */
export async function verifyRecaptchaV3(
  token: string,
  remoteIP?: string,
  expectedAction: string = 'submit',
  minScore: number = 0.5,
): Promise<RecaptchaVerificationResult> {
  return await verifyRecaptcha(token, remoteIP, expectedAction, minScore, 'v3'); // force v3
}

/**
 * Smart verification - ตรวจจับ version อัตโนมัติและใช้การตั้งค่าที่เหมาะสม
 */
export async function verifyRecaptchaSmart(
  token: string,
  remoteIP?: string,
  action: string = 'submit',
  options?: {
    v3MinScore?: number;
    strictActionCheck?: boolean;
    expectedVersion?: 'v2' | 'v3';
  },
): Promise<RecaptchaVerificationResult & { detectedVersion: 'v2' | 'v3'; }> {
  // ใช้ expectedVersion จาก frontend ถ้ามี หรือตรวจจับอัตโนมัติ
  const detectedVersion = options?.expectedVersion || detectRecaptchaVersion(token);
  const { v3MinScore = 0.5, strictActionCheck = true } = options || {};

  let result: RecaptchaVerificationResult;

  if (detectedVersion === 'v3') {
    result = await verifyRecaptchaV3(
      token,
      remoteIP,
      strictActionCheck ? action : undefined,
      v3MinScore,
    );
  }
  else {
    result = await verifyRecaptchaV2(token, remoteIP, action);
  }

  return {
    ...result,
    detectedVersion,
  };
}
