import { LogCategory, logger } from './logger';

/**
 * ✅ Unified Error Handler - จัดการ error ให้สอดคล้องกันทั้งระบบ
 */
export const ErrorHandler = {
  /**
   * ✅ จัดการ API Error
   */
  handleApiError(error: any, context: string = 'API'): string {
    const errorMessage = error instanceof Error ? error.message : 'เกิดข้อผิดพลาดที่ไม่ทราบสาเหตุ';
    const userMessage = this.toUserMessage(error);

    logger.error(LogCategory.API, 'api_error', `${context} error occurred`, {
      error: errorMessage,
      userMessage,
      stack: error instanceof Error ? error.stack : undefined,
      context,
    });

    // Return user-friendly message โดยไม่เรียก toUserMessage ซ้ำ
    return userMessage;
  },

  /**
   * ✅ จัดการ Service Error
   */
  handleServiceError(error: any, service: string = 'Service'): string {
    const errorMessage = error instanceof Error ? error.message : 'เกิดข้อผิดพลาดที่ไม่ทราบสาเหตุ';
    const userMessage = this.toUserMessage(error);

    logger.error(LogCategory.SYSTEM, 'service_error', `${service} error occurred`, {
      error: errorMessage,
      userMessage,
      stack: error instanceof Error ? error.stack : undefined,
      service,
    });

    // Return user-friendly message โดยไม่เรียก toUserMessage ซ้ำ
    return userMessage;
  },

  /**
   * ✅ จัดการ Validation Error
   */
  handleValidationError(error: string, field?: string): string {
    logger.warn(LogCategory.USER, 'validation_error', 'Validation error occurred', {
      error,
      field,
    });

    return error;
  },

  /**
   * ✅ แปลง Error เป็น User-friendly Message
   */
  toUserMessage(error: any): string {
    if (typeof error === 'string') {
      return error;
    }

    if (error instanceof Error) {
      // จัดการ error message ที่เฉพาะเจาะจง
      const message = error.message;

      // Auth errors
      if (message.includes('รหัสผ่านไม่ถูกต้อง') || message.includes('Invalid password')) {
        return 'อีเมลหรือรหัสผ่านไม่ถูกต้อง';
      }

      if (message.includes('ไม่พบผู้ใช้') || message.includes('User not found')) {
        return 'ไม่พบบัญชีผู้ใช้นี้';
      }

      if (message.includes('อีเมลไม่ถูกต้อง') || message.includes('Invalid email')) {
        return 'รูปแบบอีเมลไม่ถูกต้อง';
      }

      if (message.includes('บัญชีถูกล็อกชั่วคราว')) {
        return message;
      }

      if (message.includes('ลองเข้าสู่ระบบหลายครั้งเกินไป')) {
        return message;
      }

      // Network errors
      if (message.includes('fetch') || message.includes('network')) {
        return 'ไม่สามารถเชื่อมต่อกับเซิร์ฟเวอร์ กรุณาลองใหม่อีกครั้ง';
      }

      if (message.includes('timeout')) {
        return 'การเชื่อมต่อใช้เวลานานเกินไป กรุณาลองใหม่อีกครั้ง';
      }

      // HTTP errors
      if (message.includes('401') || message.includes('Unauthorized')) {
        return 'อีเมลหรือรหัสผ่านไม่ถูกต้อง';
      }

      if (message.includes('403') || message.includes('Forbidden')) {
        return 'ไม่มีสิทธิ์เข้าถึง';
      }

      if (message.includes('404') || message.includes('Not Found')) {
        return 'ไม่พบข้อมูลที่ต้องการ';
      }

      if (message.includes('429') || message.includes('Too Many Requests')) {
        return 'มีการร้องขอมากเกินไป กรุณารอสักครู่แล้วลองใหม่อีกครั้ง';
      }

      if (message.includes('500') || message.includes('Internal Server Error')) {
        return 'เกิดข้อผิดพลาดในเซิร์ฟเวอร์ กรุณาลองใหม่อีกครั้ง';
      }

      return message;
    }

    return 'เกิดข้อผิดพลาดที่ไม่ทราบสาเหตุ';
  },
};
