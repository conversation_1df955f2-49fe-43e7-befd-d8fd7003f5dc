/**
 * ✅ Frontend Security Monitoring for Token Activities
 */

export interface SecurityEvent {
  type:
    | 'token_refresh_failed'
    | 'multiple_refresh_attempts'
    | 'suspicious_activity'
    | 'device_change';
  severity: 'low' | 'medium' | 'high';
  timestamp: number;
  details: Record<string, any>;
  userAgent: string;
  url: string;
}

export class SecurityMonitor {
  private static instance: SecurityMonitor;
  private events: SecurityEvent[] = [];
  private maxEvents = 100;

  private constructor() {
    // ตรวจสอบว่าอยู่ใน browser environment หรือไม่
    if (typeof window !== 'undefined' && typeof localStorage !== 'undefined') {
      // Load existing events from localStorage
      this.loadEvents();

      // Setup periodic cleanup
      setInterval(() => this.cleanupOldEvents(), 60 * 60 * 1000); // Every hour
    }
  }

  static getInstance(): SecurityMonitor {
    if (!SecurityMonitor.instance) {
      SecurityMonitor.instance = new SecurityMonitor();
    }
    return SecurityMonitor.instance;
  }

  /**
   * Log security event
   */
  logEvent(
    type: SecurityEvent['type'],
    severity: SecurityEvent['severity'],
    details: Record<string, any> = {},
  ): void {
    const event: SecurityEvent = {
      type,
      severity,
      timestamp: Date.now(),
      details,
      userAgent: navigator.userAgent,
      url: window.location.href,
    };

    this.events.push(event);

    // Keep only recent events
    if (this.events.length > this.maxEvents) {
      this.events = this.events.slice(-this.maxEvents);
    }

    // Save to localStorage
    this.saveEvents();

    // Log to console based on severity
    const logLevel = severity === 'high' ? 'error' : severity === 'medium' ? 'warn' : 'log';
    console[logLevel](`[SecurityMonitor] ${type}:`, details);

    // Send alert for high severity events
    if (severity === 'high') {
      this.sendAlert(event);
    }
  }

  /**
   * Check for suspicious patterns
   */
  checkSuspiciousActivity(): void {
    const recentEvents = this.getRecentEvents(5 * 60 * 1000); // Last 5 minutes

    // Multiple failed refresh attempts
    const failedRefreshes = recentEvents.filter(e => e.type === 'token_refresh_failed');
    if (failedRefreshes.length >= 3) {
      this.logEvent('multiple_refresh_attempts', 'high', {
        count: failedRefreshes.length,
        timeWindow: '5 minutes',
      });
    }

    // Rapid page changes (potential session hijacking)
    const uniqueUrls = new Set(recentEvents.map(e => e.url));
    if (uniqueUrls.size >= 10) {
      this.logEvent('suspicious_activity', 'medium', {
        uniqueUrls: uniqueUrls.size,
        timeWindow: '5 minutes',
      });
    }
  }

  /**
   * Get recent events
   */
  getRecentEvents(timeWindow: number): SecurityEvent[] {
    const cutoff = Date.now() - timeWindow;
    return this.events.filter(e => e.timestamp > cutoff);
  }

  /**
   * Get events by type
   */
  getEventsByType(type: SecurityEvent['type']): SecurityEvent[] {
    return this.events.filter(e => e.type === type);
  }

  /**
   * Get security summary
   */
  getSecuritySummary(): {
    totalEvents: number;
    highSeverityEvents: number;
    recentEvents: number;
    eventsByType: Record<string, number>;
  } {
    const recentEvents = this.getRecentEvents(24 * 60 * 60 * 1000); // Last 24 hours
    const highSeverityEvents = this.events.filter(e => e.severity === 'high');

    const eventsByType: Record<string, number> = {};
    this.events.forEach(event => {
      eventsByType[event.type] = (eventsByType[event.type] || 0) + 1;
    });

    return {
      totalEvents: this.events.length,
      highSeverityEvents: highSeverityEvents.length,
      recentEvents: recentEvents.length,
      eventsByType,
    };
  }

  /**
   * Send security alert
   */
  private sendAlert(event: SecurityEvent): void {
    // In production, this could send to external monitoring service
    console.error('🚨 SECURITY ALERT 🚨');
    console.error(`Type: ${event.type}`);
    console.error(`Severity: ${event.severity}`);
    console.error(`Time: ${new Date(event.timestamp).toISOString()}`);
    console.error(`Details:`, event.details);
    console.error('🚨 END ALERT 🚨');

    // Could also show user notification
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification('Security Alert', {
        body: `${event.type} detected`,
        icon: '/favicon.ico',
      });
    }
  }

  /**
   * Load events from localStorage
   */
  private loadEvents(): void {
    try {
      const stored = localStorage.getItem('security-events');
      if (stored) {
        this.events = JSON.parse(stored);
      }
    }
    catch (error) {
      console.error('Failed to load security events:', error);
      this.events = [];
    }
  }

  /**
   * Save events to localStorage
   */
  private saveEvents(): void {
    try {
      localStorage.setItem('security-events', JSON.stringify(this.events));
    }
    catch (error) {
      console.error('Failed to save security events:', error);
    }
  }

  /**
   * Cleanup old events
   */
  private cleanupOldEvents(): void {
    const cutoff = Date.now() - 7 * 24 * 60 * 60 * 1000; // 7 days ago
    const originalLength = this.events.length;

    this.events = this.events.filter(e => e.timestamp > cutoff);

    if (this.events.length < originalLength) {
      this.saveEvents();
      console.log(`[SecurityMonitor] Cleaned up ${originalLength - this.events.length} old events`);
    }
  }

  /**
   * Clear all events (for testing/debugging)
   */
  clearEvents(): void {
    this.events = [];
    this.saveEvents();
    console.log('[SecurityMonitor] All events cleared');
  }
}

export const securityMonitor = SecurityMonitor.getInstance();
