import crypto from 'crypto';

/**
 * ✅ Refresh Token Rotation System
 */

interface RefreshTokenRecord {
  tokenId: string;
  userId: string;
  hashedToken: string;
  createdAt: number;
  lastUsed: number;
  rotationCount: number;
  isRevoked: boolean;
  revokedAt?: number;
  deviceFingerprint?: string;
}

// Token storage (ใน production ควรใช้ Database)
const refreshTokenStore = new Map<string, RefreshTokenRecord>();

// Configuration
const TOKEN_CONFIG = {
  REFRESH_TOKEN_LENGTH: 64,
  MAX_ROTATION_COUNT: 10,
  TOKEN_LIFETIME: 30 * 24 * 60 * 60 * 1000, // 30 days
  ROTATION_THRESHOLD: 24 * 60 * 60 * 1000, // 24 hours
} as const;

/**
 * Generate secure refresh token
 */
export function generateRefreshToken(): { token: string; tokenId: string; } {
  const token = crypto.randomBytes(TOKEN_CONFIG.REFRESH_TOKEN_LENGTH).toString('hex');
  const tokenId = crypto.randomUUID();

  return { token, tokenId };
}

/**
 * Hash refresh token for storage
 */
function hashRefreshToken(token: string): string {
  return crypto.createHash('sha256').update(token).digest('hex');
}

/**
 * Create device fingerprint
 */
export function createDeviceFingerprint(userAgent: string, ipAddress: string): string {
  return crypto
    .createHash('sha256')
    .update(`${userAgent}:${ipAddress}`)
    .digest('hex')
    .substring(0, 32);
}

/**
 * Store refresh token
 */
export function storeRefreshToken(
  token: string,
  tokenId: string,
  userId: string,
  deviceFingerprint?: string,
): void {
  const hashedToken = hashRefreshToken(token);
  const now = Date.now();

  const record: RefreshTokenRecord = {
    tokenId,
    userId,
    hashedToken,
    createdAt: now,
    lastUsed: now,
    rotationCount: 0,
    isRevoked: false,
    deviceFingerprint,
  };

  refreshTokenStore.set(tokenId, record);
}

/**
 * Validate refresh token
 */
export function validateRefreshToken(
  token: string,
  deviceFingerprint?: string,
): { valid: boolean; record?: RefreshTokenRecord; shouldRotate?: boolean; } {
  const hashedToken = hashRefreshToken(token);
  const now = Date.now();

  // Find matching token
  let matchingRecord: RefreshTokenRecord | undefined;
  for (const record of refreshTokenStore.values()) {
    if (record.hashedToken === hashedToken && !record.isRevoked) {
      matchingRecord = record;
      break;
    }
  }

  if (!matchingRecord) {
    return { valid: false };
  }

  // Check if token expired
  if (now - matchingRecord.createdAt > TOKEN_CONFIG.TOKEN_LIFETIME) {
    revokeRefreshToken(matchingRecord.tokenId);
    return { valid: false };
  }

  // Check device fingerprint if provided
  if (deviceFingerprint && matchingRecord.deviceFingerprint) {
    if (matchingRecord.deviceFingerprint !== deviceFingerprint) {
      // Suspicious activity - revoke token
      revokeRefreshToken(matchingRecord.tokenId);
      revokeAllUserTokens(matchingRecord.userId); // Revoke all tokens for security
      return { valid: false };
    }
  }

  // Check if rotation is needed
  const shouldRotate = now - matchingRecord.lastUsed > TOKEN_CONFIG.ROTATION_THRESHOLD
    || matchingRecord.rotationCount >= TOKEN_CONFIG.MAX_ROTATION_COUNT;

  // Update last used
  matchingRecord.lastUsed = now;

  return {
    valid: true,
    record: matchingRecord,
    shouldRotate,
  };
}

/**
 * Rotate refresh token
 */
export function rotateRefreshToken(
  oldTokenId: string,
  userId: string,
  deviceFingerprint?: string,
): { token: string; tokenId: string; } | null {
  const oldRecord = refreshTokenStore.get(oldTokenId);
  if (!oldRecord || oldRecord.isRevoked) {
    return null;
  }

  // Generate new token
  const { token: newToken, tokenId: newTokenId } = generateRefreshToken();

  // Store new token
  storeRefreshToken(newToken, newTokenId, userId, deviceFingerprint);

  // Update rotation count
  const newRecord = refreshTokenStore.get(newTokenId)!;
  newRecord.rotationCount = oldRecord.rotationCount + 1;

  // Revoke old token
  revokeRefreshToken(oldTokenId);

  return { token: newToken, tokenId: newTokenId };
}

/**
 * Revoke refresh token
 */
export function revokeRefreshToken(tokenId: string): void {
  const record = refreshTokenStore.get(tokenId);
  if (record) {
    record.isRevoked = true;
    record.revokedAt = Date.now();
  }
}

/**
 * Revoke all tokens for a user
 */
export function revokeAllUserTokens(userId: string): void {
  for (const record of refreshTokenStore.values()) {
    if (record.userId === userId && !record.isRevoked) {
      record.isRevoked = true;
      record.revokedAt = Date.now();
    }
  }
}

/**
 * Cleanup expired tokens
 */
export function cleanupExpiredTokens(): void {
  const now = Date.now();
  const expiredTokenIds: string[] = [];

  for (const [tokenId, record] of refreshTokenStore.entries()) {
    // Remove if expired or revoked for more than 7 days
    const isExpired = now - record.createdAt > TOKEN_CONFIG.TOKEN_LIFETIME;
    const isOldRevoked = record.isRevoked && record.revokedAt && now - record.revokedAt > 7 * 24 * 60 * 60 * 1000;

    if (isExpired || isOldRevoked) {
      expiredTokenIds.push(tokenId);
    }
  }

  // Remove expired tokens
  for (const tokenId of expiredTokenIds) {
    refreshTokenStore.delete(tokenId);
  }
}

/**
 * Get user token statistics
 */
export function getUserTokenStats(userId: string): {
  activeTokens: number;
  revokedTokens: number;
  totalRotations: number;
} {
  let activeTokens = 0;
  let revokedTokens = 0;
  let totalRotations = 0;

  for (const record of refreshTokenStore.values()) {
    if (record.userId === userId) {
      if (record.isRevoked) {
        revokedTokens++;
      }
      else {
        activeTokens++;
      }
      totalRotations += record.rotationCount;
    }
  }

  return { activeTokens, revokedTokens, totalRotations };
}

// Cleanup expired tokens every hour
setInterval(cleanupExpiredTokens, 60 * 60 * 1000);
