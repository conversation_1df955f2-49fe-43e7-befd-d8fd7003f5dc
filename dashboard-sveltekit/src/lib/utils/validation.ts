import { writable } from 'svelte/store';
import { z } from 'zod';

export type ValidationRule<T> = {
  rule: (value: T) => boolean | string;
  message?: string;
};

export type ValidationRules<T> = {
  [K in keyof T]?: ValidationRule<T[K]>[];
};

export type ValidationState<T> = {
  data: T;
  errors: Record<keyof T, string>;
  touched: Record<keyof T, boolean>;
  isValid: boolean;
  isDirty: boolean;
};

export type ValidationOptions = {
  validateOnInput?: boolean;
  validateOnBlur?: boolean;
  validateOnMount?: boolean;
  debounceMs?: number;
  showErrorsOnlyAfterTouch?: boolean;
};

export type FieldValidationState = {
  value: any;
  error: string | null;
  touched: boolean;
  dirty: boolean;
  validating: boolean;
};

/**
 * สร้าง Form Validator ที่ยืดหยุ่นและใช้งานง่าย คล้าย vuelidate แต่ใช้ zod
 */
export function createFormValidator<T extends Record<string, any>>(
  schema: z.ZodSchema<T>,
  initialData: T,
  options?: ValidationOptions,
) {
  const defaultOptions: ValidationOptions = {
    validateOnInput: true,
    validateOnBlur: true,
    validateOnMount: false,
    debounceMs: 300,
    showErrorsOnlyAfterTouch: true,
    ...options,
  };

  // สร้าง reactive store สำหรับ form state
  const state = writable<ValidationState<T>>({
    data: initialData,
    errors: {} as Record<keyof T, string>,
    touched: {} as Record<keyof T, boolean>,
    isValid: false,
    isDirty: false,
  });

  // เก็บ debounce timers สำหรับแต่ละ field
  const debounceTimers: Record<string, NodeJS.Timeout> = {};

  // ฟังก์ชัน validate field เดียว
  function validateField<K extends keyof T>(
    field: K,
    value: T[K],
    showError: boolean = true,
  ): string | null {
    // ถ้าไม่ต้องการแสดง error และ field ว่าง ให้ return null
    if (!showError && (!value || value === '')) return null;

    try {
      const fieldSchema = (schema as any).pick({ [field]: true });
      fieldSchema.parse({ [field]: value });
      return null;
    }
    catch (error: any) {
      if (error.errors && error.errors.length > 0) {
        return error.errors[0].message;
      }
      return 'ข้อมูลไม่ถูกต้อง';
    }
  }

  // ฟังก์ชัน validate ทั้ง form
  function validateForm(data: T): Record<keyof T, string> {
    const errors: Record<keyof T, string> = {} as any;

    try {
      schema.parse(data);
    }
    catch (error: any) {
      if (error.errors) {
        error.errors.forEach((issue: any) => {
          const path = issue.path.join('.') as keyof T;
          if (path) {
            errors[path] = issue.message;
          }
        });
      }
    }

    return errors;
  }

  // ฟังก์ชัน validate field แบบ debounced
  function validateFieldDebounced<K extends keyof T>(
    field: K,
    value: T[K],
    shouldShowError: boolean = true,
  ) {
    const fieldKey = String(field);

    // Clear existing timer
    if (debounceTimers[fieldKey]) {
      clearTimeout(debounceTimers[fieldKey]);
    }

    // Set new timer
    debounceTimers[fieldKey] = setTimeout(() => {
      const error = validateField(field, value, shouldShowError);

      state.update(current => {
        const newErrors = { ...current.errors };

        if (error) {
          newErrors[field] = error;
        }
        else {
          delete newErrors[field];
        }

        // Check overall form validity
        const allErrors = validateForm(current.data);
        const isValid = Object.keys(allErrors).length === 0;

        return {
          ...current,
          errors: newErrors,
          isValid,
        };
      });
    }, defaultOptions.debounceMs || 300);
  }

  // ฟังก์ชัน update field
  function updateField<K extends keyof T>(field: K, value: T[K]) {
    state.update(current => {
      const newData = { ...current.data, [field]: value };
      const newTouched = { ...current.touched, [field]: true };
      const isDirty = JSON.stringify(newData) !== JSON.stringify(initialData);

      return {
        ...current,
        data: newData,
        touched: newTouched,
        isDirty,
      };
    });

    // Validate field ถ้า validateOnInput เปิดอยู่
    if (defaultOptions.validateOnInput) {
      const shouldShowError = !defaultOptions.showErrorsOnlyAfterTouch
        || getCurrentState().touched[field];
      validateFieldDebounced(field, value, shouldShowError);
    }
  }

  // ฟังก์ชันสำหรับดึง state ปัจจุบัน
  function getCurrentState(): ValidationState<T> {
    let currentState: ValidationState<T> = {
      data: initialData,
      errors: {} as Record<keyof T, string>,
      touched: {} as Record<keyof T, boolean>,
      isValid: false,
      isDirty: false,
    };
    state.subscribe(s => currentState = s)();
    return currentState;
  }

  // ฟังก์ชัน validate field เมื่อ blur
  function validateFieldOnBlur<K extends keyof T>(field: K) {
    if (!defaultOptions.validateOnBlur) return;

    // Mark field as touched
    state.update(current => ({
      ...current,
      touched: { ...current.touched, [field]: true },
    }));

    // Validate field immediately (no debounce on blur)
    const currentState = getCurrentState();
    const fieldError = validateField(field, currentState.data[field], true);

    state.update(current => {
      const newErrors = { ...current.errors };

      if (fieldError) {
        newErrors[field] = fieldError;
      }
      else {
        delete newErrors[field];
      }

      // Check overall form validity
      const allErrors = validateForm(current.data);
      const isValid = Object.keys(allErrors).length === 0;

      return {
        ...current,
        errors: newErrors,
        isValid,
      };
    });
  }

  // ฟังก์ชัน reset form
  function resetForm() {
    state.set({
      data: initialData,
      errors: {} as Record<keyof T, string>,
      touched: {} as Record<keyof T, boolean>,
      isValid: false,
      isDirty: false,
    });
  }

  // ฟังก์ชัน set errors จากภายนอก
  function setErrors(errors: Record<keyof T, string>) {
    state.update(current => ({
      ...current,
      errors,
    }));
  }

  // สร้าง field binder ที่ใช้งานง่าย คล้าย vuelidate
  function createFieldBinder<K extends keyof T>(field: K) {
    return {
      get value(): T[K] {
        return getCurrentState().data[field];
      },
      set value(val: T[K]) {
        updateField(field, val);
      },
      get error() {
        const currentState = getCurrentState();
        const shouldShowError = !defaultOptions.showErrorsOnlyAfterTouch
          || currentState.touched[field];
        return shouldShowError ? (currentState.errors[field] || '') : '';
      },
      get hasError() {
        return !!this.error;
      },
      get touched() {
        return getCurrentState().touched[field] || false;
      },
      get dirty() {
        const currentState = getCurrentState();
        return currentState.data[field] !== initialData[field];
      },
      get valid() {
        return !this.hasError;
      },
      get invalid() {
        return this.hasError;
      },
      // Event handlers
      onInput: (val: T[K]) => updateField(field, val),
      onBlur: () => validateFieldOnBlur(field),
      // Manual validation
      validate: () => {
        const currentState = getCurrentState();
        return validateField(field, currentState.data[field], true);
      },
      // Clear error
      clearError: () => {
        state.update(current => {
          const newErrors = { ...current.errors };
          delete newErrors[field];
          return { ...current, errors: newErrors };
        });
      },
    };
  }

  // Cleanup function
  function cleanup() {
    Object.values(debounceTimers).forEach(timer => clearTimeout(timer));
  }

  return {
    // Reactive store
    state,

    // Methods
    updateField,
    validateField,
    validateForm,
    validateFieldOnBlur,
    validateFieldDebounced,
    resetForm,
    setErrors,
    createFieldBinder,
    getCurrentState,
    cleanup,

    // Computed values (using getCurrentState for better performance)
    get isValid() {
      return getCurrentState().isValid;
    },

    get isDirty() {
      return getCurrentState().isDirty;
    },

    get data() {
      return getCurrentState().data;
    },

    get errors() {
      return getCurrentState().errors;
    },

    get touched() {
      return getCurrentState().touched;
    },

    // Helper methods
    get hasErrors() {
      return Object.keys(this.errors).length > 0;
    },

    get touchedFields() {
      const touched = this.touched;
      return Object.keys(touched).filter(key => touched[key as keyof T]);
    },

    // Validate all fields
    validateAll: () => {
      const currentState = getCurrentState();
      const allErrors = validateForm(currentState.data);

      state.update(current => ({
        ...current,
        errors: allErrors,
        isValid: Object.keys(allErrors).length === 0,
        touched: Object.keys(currentState.data).reduce((acc, key) => {
          acc[key as keyof T] = true;
          return acc;
        }, {} as Record<keyof T, boolean>),
      }));

      return Object.keys(allErrors).length === 0;
    },
  };
}

/**
 * สร้าง Validator แบบง่ายสำหรับ Svelte 5 runes
 * ใช้งานง่าย เหมาะสำหรับ form ที่ไม่ซับซ้อน
 */
export function createValidator<T extends Record<string, any>>(
  schema: z.ZodSchema<T>,
  options?: ValidationOptions,
) {
  const defaultOptions: ValidationOptions = {
    validateOnBlur: true,
    validateOnInput: true,
    debounceMs: 300,
    showErrorsOnlyAfterTouch: false,
    ...options,
  };

  return {
    validateField<K extends keyof T>(
      field: K,
      value: T[K],
      _showError: boolean = true,
    ): string | null {
      // ไม่ validate ถ้า field ว่าง
      if (!value || value === '') return null;

      try {
        const fieldSchema = (schema as any).pick({ [field]: true });
        fieldSchema.parse({ [field]: value });
        return null;
      }
      catch (error: any) {
        if (error.errors && error.errors.length > 0) {
          return error.errors[0].message;
        }
        return 'ข้อมูลไม่ถูกต้อง';
      }
    },

    validateForm(data: T): Record<keyof T, string> {
      const errors: Record<keyof T, string> = {} as any;

      try {
        schema.parse(data);
      }
      catch (error: any) {
        if (error.errors) {
          error.errors.forEach((issue: any) => {
            const path = issue.path.join('.') as keyof T;
            if (path) {
              errors[path] = issue.message;
            }
          });
        }
      }

      return errors;
    },

    createFieldValidator<K extends keyof T>(
      field: K,
      onError?: (error: string | null) => void,
    ) {
      return {
        validate: (value: T[K], showError: boolean = true) => {
          const error = this.validateField(field, value, showError);
          if (onError) onError(error);
          return error;
        },
        validateOnInput: (value: T[K]) => {
          if (defaultOptions.validateOnInput) {
            return this.validateField(field, value, true);
          }
          return null;
        },
        validateOnBlur: (value: T[K]) => {
          if (defaultOptions.validateOnBlur) {
            return this.validateField(field, value, true);
          }
          return null;
        },
      };
    },
  };
}

/**
 * สร้าง Reactive Form Validator สำหรับ Svelte 5 runes
 * ใช้งานคล้าย vuelidate แต่ใช้ zod และ runes
 */
export function createReactiveValidator<T extends Record<string, any>>(
  schema: z.ZodSchema<T>,
  initialData: T,
  options?: ValidationOptions,
) {
  const validator = createFormValidator(schema, initialData, options);

  // สร้าง reactive fields object
  const fields = {} as {
    [K in keyof T]: ReturnType<typeof validator.createFieldBinder>;
  };

  // สร้าง field binders สำหรับทุก field
  Object.keys(initialData).forEach(key => {
    fields[key as keyof T] = validator.createFieldBinder(key as keyof T);
  });

  return {
    // Fields object คล้าย vuelidate
    fields,

    // Form-level properties
    get $valid() {
      return validator.isValid;
    },
    get $invalid() {
      return !validator.isValid;
    },
    get $dirty() {
      return validator.isDirty;
    },
    get $pristine() {
      return !validator.isDirty;
    },
    get $errors() {
      return validator.errors;
    },
    get $hasErrors() {
      return validator.hasErrors;
    },
    get $data() {
      return validator.data;
    },

    // Methods
    $validate: validator.validateAll,
    $reset: validator.resetForm,
    $setErrors: validator.setErrors,
    $cleanup: validator.cleanup,

    // Internal validator access
    _validator: validator,
  };
}

// Helper function สำหรับสร้าง simple validator
export function createSimpleValidator<T extends Record<string, any>>(
  rules: ValidationRules<T>,
) {
  return {
    validateField<K extends keyof T>(
      field: K,
      value: T[K],
    ): string | null {
      const fieldRules = rules[field];
      if (!fieldRules) return null;

      for (const rule of fieldRules) {
        const result = rule.rule(value);
        if (result !== true) {
          return typeof result === 'string' ? result : rule.message || 'ข้อมูลไม่ถูกต้อง';
        }
      }
      return null;
    },
  };
}
