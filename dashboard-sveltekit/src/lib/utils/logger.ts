import { browser } from '$app/environment';
import { isLoggingEnabled, loggerConfig } from '$lib/config';
import { hashForLogging } from './security';

// Log levels
export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  CRITICAL = 4,
}

// Log categories
export enum LogCategory {
  AUTH = 'auth',
  API = 'api',
  SECURITY = 'security',
  PERFORMANCE = 'performance',
  USER = 'user',
  SYSTEM = 'system',
}

// Log entry interface
export interface LogEntry {
  timestamp: string;
  level: LogLevel;
  category: LogCategory;
  event: string;
  message: string;
  data?: Record<string, any>;
  userId?: string;
  sessionId?: string;
  ip?: string;
  userAgent?: string;
  requestId?: string;
}

// Logger configuration
const LOGGER_CONFIG = {
  maxLogSize: 1000, // Maximum number of logs to keep in memory
  enableConsole: loggerConfig.console.enabled,
  enableStorage: browser, // Only enable storage in browser
  logLevel: import.meta.env.DEV ? LogLevel.DEBUG : LogLevel.INFO,
  sensitiveFields: ['password', 'token', 'refreshToken', 'secret', 'key'],
};

// In-memory log storage
let logStorage: LogEntry[] = [];

// Performance metrics storage
const performanceMetrics = new Map<
  string,
  {
    count: number;
    totalTime: number;
    avgTime: number;
    minTime: number;
    maxTime: number;
  }
>();

class Logger {
  private static instance: Logger;
  private requestId: string = '';

  private constructor() {
    if (browser) {
      this.loadLogsFromStorage();
    }
  }

  static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger();
    }
    return Logger.instance;
  }

  setRequestId(requestId: string) {
    this.requestId = requestId;
  }

  private sanitizeData(data: any): any {
    if (!data || typeof data !== 'object') return data;

    const sanitized = { ...data };

    for (const field of LOGGER_CONFIG.sensitiveFields) {
      if (sanitized[field]) {
        sanitized[field] = hashForLogging(String(sanitized[field]));
      }
    }

    return sanitized;
  }

  private createLogEntry(
    level: LogLevel,
    category: LogCategory,
    event: string,
    message: string,
    data?: Record<string, any>,
    context?: {
      userId?: string;
      sessionId?: string;
      ip?: string;
      userAgent?: string;
    },
  ): LogEntry {
    return {
      timestamp: new Date().toISOString(),
      level,
      category,
      event,
      message,
      data: data ? this.sanitizeData(data) : undefined,
      userId: context?.userId,
      sessionId: context?.sessionId,
      ip: context?.ip,
      userAgent: context?.userAgent,
      requestId: this.requestId,
    };
  }

  private shouldLog(level: LogLevel, category: LogCategory): boolean {
    // ตรวจสอบว่า level และ category เปิดใช้งานหรือไม่
    const levelKey = Object.keys(LogLevel)[
      level
    ]?.toLowerCase() as keyof typeof loggerConfig.levels;
    const categoryKey = category.toLowerCase() as keyof typeof loggerConfig.categories;

    return isLoggingEnabled(levelKey, categoryKey);
  }

  private writeLog(entry: LogEntry) {
    // ตรวจสอบก่อน log
    if (!this.shouldLog(entry.level, entry.category)) {
      return;
    }

    // Add to in-memory storage
    logStorage.push(entry);
    if (logStorage.length > LOGGER_CONFIG.maxLogSize) {
      logStorage.shift();
    }

    // Console output
    if (LOGGER_CONFIG.enableConsole) {
      const consoleMethod = this.getConsoleMethod(entry.level);
      const timestamp = loggerConfig.console.showTimestamp ? `[${entry.timestamp}]` : '';
      const category = loggerConfig.console.showCategory ? `[${entry.category}]` : '';
      const prefix = `${timestamp}${category}`.trim();

      consoleMethod(`${prefix} ${entry.event}: ${entry.message}`, entry.data || '');
    }

    // Browser storage
    if (LOGGER_CONFIG.enableStorage) {
      this.saveLogsToStorage();
    }
  }

  private getConsoleMethod(level: LogLevel) {
    switch (level) {
      case LogLevel.DEBUG:
        return console.debug;
      case LogLevel.INFO:
        return console.info;
      case LogLevel.WARN:
        return console.warn;
      case LogLevel.ERROR:
      case LogLevel.CRITICAL:
        return console.error;
      default:
        return console.log;
    }
  }

  private loadLogsFromStorage() {
    try {
      const stored = localStorage.getItem('app_logs');
      if (stored) {
        logStorage = JSON.parse(stored);
      }
    }
    catch (error) {
      console.warn('Failed to load logs from storage:', error);
    }
  }

  private saveLogsToStorage() {
    try {
      localStorage.setItem('app_logs', JSON.stringify(logStorage));
    }
    catch (error) {
      console.warn('Failed to save logs to storage:', error);
    }
  }

  debug(
    category: LogCategory,
    event: string,
    message: string,
    data?: Record<string, any>,
    context?: any,
  ) {
    this.writeLog(this.createLogEntry(LogLevel.DEBUG, category, event, message, data, context));
  }

  info(
    category: LogCategory,
    event: string,
    message: string,
    data?: Record<string, any>,
    context?: any,
  ) {
    this.writeLog(this.createLogEntry(LogLevel.INFO, category, event, message, data, context));
  }

  warn(
    category: LogCategory,
    event: string,
    message: string,
    data?: Record<string, any>,
    context?: any,
  ) {
    this.writeLog(this.createLogEntry(LogLevel.WARN, category, event, message, data, context));
  }

  error(
    category: LogCategory,
    event: string,
    message: string,
    data?: Record<string, any>,
    context?: any,
  ) {
    this.writeLog(this.createLogEntry(LogLevel.ERROR, category, event, message, data, context));
  }

  critical(
    category: LogCategory,
    event: string,
    message: string,
    data?: Record<string, any>,
    context?: any,
  ) {
    this.writeLog(this.createLogEntry(LogLevel.CRITICAL, category, event, message, data, context));
  }

  // Specialized logging methods
  authAttempt(email: string, ip: string, userAgent: string, success: boolean, reason?: string) {
    this.info(
      LogCategory.AUTH,
      'auth_attempt',
      `Authentication attempt ${success ? 'succeeded' : 'failed'}`,
      {
        email: email.substring(0, 3) + '***',
        success,
        reason,
      },
      { ip, userAgent },
    );
  }

  authSignout(userId: string, ip: string, userAgent: string) {
    this.info(LogCategory.AUTH, 'auth_signout', 'User signed out', { userId }, { ip, userAgent });
  }

  securityEvent(
    event: string,
    message: string,
    severity: 'low' | 'medium' | 'high' | 'critical',
    data?: Record<string, any>,
    context?: any,
  ) {
    const level = severity === 'critical'
      ? LogLevel.CRITICAL
      : severity === 'high'
      ? LogLevel.ERROR
      : severity === 'medium'
      ? LogLevel.WARN
      : LogLevel.INFO;

    this.writeLog(this.createLogEntry(level, LogCategory.SECURITY, event, message, data, context));
  }

  startPerformanceTimer(operation: string): () => void {
    const startTime = performance.now();
    return () => {
      const duration = performance.now() - startTime;
      this.recordPerformanceMetric(operation, duration);
      this.debug(
        LogCategory.PERFORMANCE,
        'performance_metric',
        `${operation} took ${duration.toFixed(2)}ms`,
      );
    };
  }

  private recordPerformanceMetric(operation: string, duration: number) {
    const existing = performanceMetrics.get(operation);
    if (existing) {
      existing.count++;
      existing.totalTime += duration;
      existing.avgTime = existing.totalTime / existing.count;
      existing.minTime = Math.min(existing.minTime, duration);
      existing.maxTime = Math.max(existing.maxTime, duration);
    }
    else {
      performanceMetrics.set(operation, {
        count: 1,
        totalTime: duration,
        avgTime: duration,
        minTime: duration,
        maxTime: duration,
      });
    }
  }

  getLogs(filter?: {
    level?: LogLevel;
    category?: LogCategory;
    limit?: number;
    since?: Date;
  }): LogEntry[] {
    let filtered = [...logStorage];

    if (filter?.level !== undefined) {
      filtered = filtered.filter(log => log.level >= filter.level!);
    }

    if (filter?.category) {
      filtered = filtered.filter(log => log.category === filter.category);
    }

    if (filter?.since) {
      filtered = filtered.filter(log => new Date(log.timestamp) >= filter.since!);
    }

    if (filter?.limit) {
      filtered = filtered.slice(-filter.limit);
    }

    return filtered;
  }

  getPerformanceMetrics(): Map<string, any> {
    return new Map(performanceMetrics);
  }

  clearLogs() {
    logStorage = [];
    if (browser) {
      localStorage.removeItem('app_logs');
    }
  }

  exportLogs(): string {
    return JSON.stringify(logStorage, null, 2);
  }
}

// Singleton instance
export const logger = Logger.getInstance();

// Convenience functions
export const logAuthAttempt = (
  email: string,
  ip: string,
  userAgent: string,
  success: boolean,
  reason?: string,
) => {
  logger.authAttempt(email, ip, userAgent, success, reason);
};

export const logSecurityEvent = (
  event: string,
  message: string,
  severity: 'low' | 'medium' | 'high' | 'critical',
  data?: Record<string, any>,
  context?: any,
) => {
  logger.securityEvent(event, message, severity, data, context);
};

export const measurePerformance = (operation: string) => {
  return logger.startPerformanceTimer(operation);
};
