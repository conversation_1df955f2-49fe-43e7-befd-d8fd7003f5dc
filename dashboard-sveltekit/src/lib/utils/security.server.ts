import crypto from 'crypto';
import { SECURITY_CONFIG } from './security';

// Server-only security utilities
export function generateCSRFTokenServer(): string {
  return crypto.randomBytes(SECURITY_CONFIG.CSRF_TOKEN_LENGTH).toString('hex');
}

export function hashForLoggingServer(data: string): string {
  return crypto.createHash('sha256').update(data).digest('hex').substring(0, 8);
}

export function generateSecureRandomString(length: number = 32): string {
  return crypto.randomBytes(length).toString('hex');
}

export function createHMAC(data: string, secret: string): string {
  return crypto.createHmac('sha256', secret).update(data).digest('hex');
}

export function verifyHMAC(data: string, signature: string, secret: string): boolean {
  const expectedSignature = createHMAC(data, secret);
  return crypto.timingSafeEqual(Buffer.from(signature), Buffer.from(expectedSignature));
}
