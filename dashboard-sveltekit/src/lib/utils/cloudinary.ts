/**
 * Cloudinary Utility Functions
 * สำหรับจัดการ URL และ transformations ของ Cloudinary
 */

export interface CloudinaryConfig {
  cloudName: string;
  defaultTransformation?: string;
  secure?: boolean;
}

/**
 * Generate Cloudinary URL with transformations
 */
export function getCloudinaryUrl(
  publicId: string,
  transformation?: string,
  config?: Partial<CloudinaryConfig>,
): string {
  const defaultConfig: CloudinaryConfig = {
    cloudName: 'dsoy8tgfc',
    defaultTransformation: 'f_auto,q_auto',
    secure: true,
  };

  const finalConfig = { ...defaultConfig, ...config };
  const protocol = finalConfig.secure ? 'https' : 'http';
  const baseUrl = `${protocol}://res.cloudinary.com/${finalConfig.cloudName}/image/upload`;

  const transformations = transformation || finalConfig.defaultTransformation;
  const url = `${baseUrl}/${transformations}/${publicId}`;

  return url;
}

/**
 * Universal image URL generator - works for all image types
 */
export function getImageUrl(
  publicId: string,
  width?: number,
  height?: number,
  options?: {
    crop?: string;
    quality?: number;
    format?: string;
    config?: Partial<CloudinaryConfig>;
  },
): string {
  const { crop = 'fill', quality = 80, format = 'auto', config } = options || {};

  let transformation = `f_${format},q_${quality}`;

  if (width && height) {
    transformation = `w_${width},h_${height},c_${crop},${transformation}`;
  }
  else if (width) {
    transformation = `w_${width},c_scale,${transformation}`;
  }
  else if (height) {
    transformation = `h_${height},c_scale,${transformation}`;
  }

  return getCloudinaryUrl(publicId, transformation, config);
}

/**
 * Generate responsive image URLs
 */
export function getResponsiveUrls(
  publicId: string,
  sizes: Array<{ width: number; height: number; }>,
  config?: Partial<CloudinaryConfig>,
): string[] {
  return sizes.map(size => getImageUrl(publicId, size.width, size.height, { config }));
}

/**
 * Check if URL is from Cloudinary
 */
export function isCloudinaryUrl(url: string): boolean {
  return url.includes('cloudinary.com');
}

/**
 * Extract public ID from Cloudinary URL
 */
export function extractPublicId(url: string): string | null {
  if (!isCloudinaryUrl(url)) return null;

  const parts = url.split('/');
  const uploadIndex = parts.findIndex(part => part === 'upload');

  if (uploadIndex === -1 || uploadIndex >= parts.length - 1) return null;

  // Get everything after 'upload' and before the last part (which might be version)
  const publicIdParts = parts.slice(uploadIndex + 1);
  const lastPart = publicIdParts[publicIdParts.length - 1];

  // Remove version prefix if present (e.g., "v123/")
  if (lastPart.startsWith('v') && lastPart.includes('/')) {
    return lastPart.split('/')[1];
  }

  return lastPart;
}

/**
 * Generate srcset for responsive images
 */
export function getSrcSet(
  publicId: string,
  sizes: Array<{ width: number; height: number; }>,
  config?: Partial<CloudinaryConfig>,
): string {
  const urls = getResponsiveUrls(publicId, sizes, config);
  return urls.map((url, index) => `${url} ${sizes[index].width}w`).join(', ');
}

/**
 * Generate placeholder URL for lazy loading
 */
export function getPlaceholderUrl(publicId: string, config?: Partial<CloudinaryConfig>): string {
  return getImageUrl(publicId, 10, 10, {
    crop: 'fill',
    quality: 10,
    format: 'auto',
    config,
  });
}
