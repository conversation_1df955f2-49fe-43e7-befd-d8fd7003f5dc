import { goto } from '$app/navigation';
import {
  type AuthResponse,
  type ForgotPasswordData,
  type RefreshTokenResponse,
  type ResetPasswordData,
  sanitizeAuthData,
  type SigninData,
  type SignupData,
  validateForgotPasswordData,
  validateResetPasswordData,
  validateSigninData,
  validateSignupData,
  type VerifyEmailData,
} from '$lib/schemas/auth.schema';
import type { ApiResponse } from '$lib/types/common';
import type { User } from '$lib/types/user';
import { BaseService } from './base';

// Type aliases เพื่อลดความซับซ้อน
type AuthData = {
  user: User;
  token: string;
  refreshToken: string;
  sessionId?: string;
};

type AuthApiResponse = {
  data: AuthData;
  message?: string;
};

/**
 * ✅ Auth Service - ใช้ BaseService และ native fetch
 * - Validation ก่อนส่ง API
 * - Consistent error handling
 * - Type-safe responses
 */
class AuthService extends BaseService {
  /**
   * ✅ Sign in user with reCAPTCHA support
   */
  async signin(credentials: SigninData & { recaptchaToken?: string; }): Promise<AuthResponse> {
    // Validate input
    const sanitizedData = sanitizeAuthData(credentials);
    const validation = validateSigninData(sanitizedData);

    if (!validation.success) {
      return {
        success: false,
        error: validation.error || 'ข้อมูลไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makePublicRequest<AuthApiResponse>('/user/signin', {
        method: 'POST',
        body: {
          ...sanitizedData,
          recaptchaToken: credentials.recaptchaToken,
        },
      });

      return result;
    }, 'เข้าสู่ระบบสำเร็จ');
  }

  /**
   * ✅ Sign up new user with reCAPTCHA support
   */
  async signup(credentials: SignupData & { recaptchaToken?: string; }): Promise<AuthResponse> {
    // Validate input
    const sanitizedData = sanitizeAuthData(credentials);
    const validation = validateSignupData(sanitizedData);

    if (!validation.success) {
      return {
        success: false,
        error: validation.error || 'ข้อมูลไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makePublicRequest<AuthApiResponse>('/user/signup', {
        method: 'POST',
        body: {
          ...sanitizedData,
          recaptchaToken: credentials.recaptchaToken,
        },
      });

      return result;
    }, 'ลงทะเบียนสำเร็จ');
  }

  /**
   * ✅ Sign out user with complete cleanup
   */
  async signout(token?: string, refreshToken?: string): Promise<ApiResponse<void>> {
    return this.handleRequest(async () => {
      if (token) {
        // ✅ Send both tokens for complete revocation
        await this.makeAuthenticatedRequest<void>('/user/signout', token, {
          method: 'POST',
          body: refreshToken ? { refreshToken } : undefined,
        });
      }
      else {
        // ✅ Public signout for cases where token is invalid
        await this.makePublicRequest<void>('/user/signout', {
          method: 'POST',
          body: refreshToken ? { refreshToken } : undefined,
        });
      }
    });
  }

  /**
   * ✅ Forgot password with reCAPTCHA support
   */
  async forgotPassword(
    data: ForgotPasswordData & { recaptchaToken?: string; },
  ): Promise<ApiResponse<void>> {
    // Validate input
    const sanitizedData = sanitizeAuthData(data);
    const validation = validateForgotPasswordData(sanitizedData);

    if (!validation.success) {
      return {
        success: false,
        error: validation.error || 'ข้อมูลไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      await this.makePublicRequest<void>('/user/forgot-password', {
        method: 'POST',
        body: {
          ...sanitizedData,
          recaptchaToken: data.recaptchaToken,
        },
      });
    });
  }

  /**
   * ✅ Reset password
   */
  async resetPassword(data: ResetPasswordData): Promise<ApiResponse<void>> {
    // Validate input
    const sanitizedData = sanitizeAuthData(data);
    const validation = validateResetPasswordData(sanitizedData);

    if (!validation.success) {
      return {
        success: false,
        error: validation.error || 'ข้อมูลไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      await this.makePublicRequest<void>('/user/reset-password', {
        method: 'POST',
        body: sanitizedData,
      });
    });
  }

  /**
   * ✅ Verify email
   */
  async verifyEmail(data: VerifyEmailData): Promise<ApiResponse<void>> {
    if (!data.token?.trim()) {
      return {
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      await this.makePublicRequest<void>('/user/verify-email', {
        method: 'POST',
        body: { token: data.token.trim() },
      });
    });
  }

  /**
   * ✅ Resend verification email
   */
  async resendVerification(data: ForgotPasswordData): Promise<ApiResponse<void>> {
    // Validate input
    const sanitizedData = sanitizeAuthData(data);
    const validation = validateForgotPasswordData(sanitizedData);

    if (!validation.success) {
      return {
        success: false,
        error: validation.error || 'ข้อมูลไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      await this.makePublicRequest<void>('/user/resend-verification', {
        method: 'POST',
        body: sanitizedData,
      });
    });
  }

  /**
   * ✅ Refresh token
   */
  async refreshToken(refreshToken: string): Promise<RefreshTokenResponse> {
    if (!refreshToken?.trim()) {
      return {
        success: false,
        error: 'Refresh token ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makePublicRequest<{
        data: {
          token: string;
          refreshToken: string;
          user: User;
        };
        message?: string;
      }>('/user/refresh-token', {
        method: 'POST',
        body: { refreshToken: refreshToken.trim() },
      });

      return result;
    }, 'รีเฟรช token สำเร็จ');
  }

  /**
   * ✅ Get current user
   */
  async getCurrentUser(token: string): Promise<ApiResponse<User>> {
    if (!token?.trim()) {
      return {
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const result = await this.makeAuthenticatedRequest<{ data: User; }>('/user/profile', token);
      return result.data;
    });
  }

  /**
   * ✅ Server action signout - ใช้สำหรับ clear cookies และ redirect
   */
  async serverSignout(formData: any): Promise<ApiResponse<void>> {
    console.log('AuthService: Starting serverSignout()');

    try {
      // ใช้ fetch ธรรมดาแทน $fetch เพื่อ handle redirect ได้ดีกว่า
      console.log('AuthService: Sending POST request to /signout');

      const response = await fetch('/signout', {
        method: 'POST',
        body: formData,
        credentials: 'include', // Include cookies
        redirect: 'manual', // Don't follow redirects automatically
      });

      console.log('AuthService: Response status:', response.status);
      console.log('AuthService: Response headers:', Object.fromEntries(response.headers.entries()));

      // Check for redirect responses
      if (response.status === 302 || response.status === 301) {
        console.log('AuthService: Server signout successful (with redirect)');

        // Get redirect location and follow it
        const location = response.headers.get('location');
        console.log('AuthService: Redirect location:', location);

        if (location) {
          // Follow the redirect manually
          // window.location.href = location;
          goto(location, { replaceState: true });
        }
        else {
          // Fallback redirect
          goto('/signin', { replaceState: true });
        }

        return {
          success: true,
          message: 'ออกจากระบบสำเร็จ (redirect)',
        };
      }

      // Check for success responses
      if (response.ok) {
        console.log('AuthService: Server signout successful (no redirect)');
        return {
          success: true,
          message: 'ออกจากระบบสำเร็จ',
        };
      }

      // Handle error responses
      const errorText = await response.text();
      console.error('AuthService: Server signout failed:', response.status, errorText);

      return {
        success: false,
        error: `Server error: ${response.status} ${errorText}`,
      };
    }
    catch (error: any) {
      console.error('AuthService: Network error during signout:', error);

      return {
        success: false,
        error: error instanceof Error ? error.message : 'เกิดข้อผิดพลาดในการออกจากระบบ',
      };
    }
  }
}

export const authService = new AuthService();
