import { apiUrl } from '$lib/config';
import type { ApiResponse } from '$lib/types/common';
import type {
  CreateSubscriptionData,
  DiscountValidation,
  PackageInfo,
  Subscription,
  SubscriptionNotification,
} from '$lib/types/subscription';
import { BaseService } from './base';

export class SubscriptionService extends BaseService {
  // ดึงข้อมูลแพ็คเกจ
  async getPackages(token: string): Promise<ApiResponse<PackageInfo[]>> {
    return this.makeAuthenticatedRequest('/subscription/packages', token);
  }

  // ตรวจสอบรหัสส่วนลด
  async validateDiscount(
    discountCode: string,
    token: string,
  ): Promise<ApiResponse<DiscountValidation>> {
    return this.makeAuthenticatedRequest('/subscription/validate-discount', token, {
      method: 'POST',
      body: JSON.stringify({ discountCode }),
    });
  }

  // สร้าง subscription สำหรับ site
  async createSiteSubscription(
    siteId: string,
    data: CreateSubscriptionData,
    token: string,
  ): Promise<ApiResponse<Subscription>> {
    return this.makeAuthenticatedRequest(`/subscription/sites/${siteId}/subscribe`, token, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // ดึงรายการ subscription ของ site
  async getSiteSubscriptions(
    siteId: string,
    params: {
      page?: string;
      limit?: string;
      status?: string;
    } = {},
    token: string,
  ): Promise<
    ApiResponse<{
      subscriptions: Subscription[];
      pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
      };
    }>
  > {
    const queryParams = new URLSearchParams();
    if (params.page) queryParams.append('page', params.page);
    if (params.limit) queryParams.append('limit', params.limit);
    if (params.status) queryParams.append('status', params.status);

    const endpoint = `/subscription/sites/${siteId}/subscriptions?${queryParams}`;
    return this.makeAuthenticatedRequest(endpoint, token);
  }

  // เปิด/ปิด auto renew
  async toggleAutoRenew(
    siteId: string,
    subscriptionId: string,
    autoRenew: boolean,
    token: string,
  ): Promise<ApiResponse<Subscription>> {
    return this.makeAuthenticatedRequest(
      `/subscription/sites/${siteId}/subscriptions/${subscriptionId}/auto-renew`,
      token,
      {
        method: 'PUT',
        body: JSON.stringify({ autoRenew }),
      },
    );
  }

  // ยกเลิก subscription
  async cancelSubscription(
    siteId: string,
    subscriptionId: string,
    token: string,
  ): Promise<ApiResponse<Subscription>> {
    return this.makeAuthenticatedRequest(
      `/subscription/sites/${siteId}/subscriptions/${subscriptionId}/cancel`,
      token,
      {
        method: 'PUT',
      },
    );
  }

  // ต่ออายุ subscription แบบ manual
  async renewSubscription(
    siteId: string,
    subscriptionId: string,
    token: string,
  ): Promise<ApiResponse<Subscription>> {
    return this.makeAuthenticatedRequest(
      `/subscription/sites/${siteId}/subscriptions/${subscriptionId}/renew`,
      token,
      {
        method: 'POST',
      },
    );
  }

  // ดึงการแจ้งเตือนของ site
  async getSiteNotifications(
    siteId: string,
    params: {
      page?: string;
      limit?: string;
      unreadOnly?: string;
    } = {},
    token: string,
  ): Promise<
    ApiResponse<{
      notifications: SubscriptionNotification[];
      pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
      };
    }>
  > {
    const queryParams = new URLSearchParams();
    if (params.page) queryParams.append('page', params.page);
    if (params.limit) queryParams.append('limit', params.limit);
    if (params.unreadOnly) queryParams.append('unreadOnly', params.unreadOnly);

    const endpoint = `/subscription/sites/${siteId}/notifications?${queryParams}`;
    return this.makeAuthenticatedRequest(endpoint, token);
  }

  // อ่านการแจ้งเตือน
  async markNotificationAsRead(
    notificationId: string,
    token: string,
  ): Promise<ApiResponse<SubscriptionNotification>> {
    return this.makeAuthenticatedRequest(
      `/subscription/notifications/${notificationId}/read`,
      token,
      {
        method: 'PUT',
      },
    );
  }
}

export const subscriptionService = new SubscriptionService();
