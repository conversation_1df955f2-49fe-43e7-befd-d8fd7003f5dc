import type { ApiResponse } from '$lib/types/common';
import { BaseService } from './base';

// ✅ Dashboard Types
export interface DashboardStats {
  totalSales: number;
  totalOrders: number;
  totalVisitors: number;
  totalProducts: number;
  salesChange: string;
  ordersChange: string;
  visitorsChange: string;
  productsChange: string;
}

export interface DashboardChartData {
  sales: {
    labels: string[];
    datasets: Array<{
      label: string;
      data: number[];
      borderColor: string;
      backgroundColor: string;
      tension: number;
    }>;
  };
  visitors: {
    labels: string[];
    datasets: Array<{
      label: string;
      data: number[];
      borderColor: string;
      backgroundColor: string;
      tension: number;
    }>;
  };
  orders: {
    labels: string[];
    datasets: Array<{
      data: number[];
      backgroundColor: string[];
    }>;
  };
  products: {
    labels: string[];
    datasets: Array<{
      data: number[];
      backgroundColor: string[];
    }>;
  };
}

export interface RecentActivity {
  type: 'order' | 'payment' | 'product' | 'customer';
  message: string;
  time: string;
  icon: string;
  color: string;
}

export interface DashboardData {
  stats: DashboardStats;
  chartData: DashboardChartData;
  recentActivities: RecentActivity[];
}

export interface DashboardParams {
  period?: 'today' | 'week' | 'month' | 'year';
  startDate?: string;
  endDate?: string;
}

/**
 * ✅ Dashboard Service - ใช้ BaseService และ makeAuthenticatedRequest
 * - Validation ก่อนส่ง API
 * - Consistent error handling
 * - Type-safe responses
 */
class DashboardService extends BaseService {
  /**
   * ✅ Get dashboard data for specific site
   */
  async getDashboardData(
    siteId: string,
    token: string,
    params: DashboardParams = {},
  ): Promise<ApiResponse<DashboardData>> {
    if (!siteId?.trim()) {
      return {
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    if (!token?.trim()) {
      return {
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    // Validate parameters
    const validationError = this.validateDashboardParams(params);
    if (validationError) {
      return {
        success: false,
        error: validationError,
      };
    }

    return this.handleRequest(async () => {
      const queryString = this.buildQueryParams(params);
      const endpoint = `/dashboard/${siteId}/overview${queryString ? `?${queryString}` : ''}`;

      const result = await this.makeAuthenticatedRequest<{
        data: DashboardData;
      }>(endpoint, token);
      return result.data;
    });
  }

  /**
   * ✅ Get dashboard statistics only
   */
  async getDashboardStats(
    siteId: string,
    token: string,
    params: DashboardParams = {},
  ): Promise<ApiResponse<DashboardStats>> {
    if (!siteId?.trim()) {
      return {
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    if (!token?.trim()) {
      return {
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const queryString = this.buildQueryParams(params);
      const endpoint = `/dashboard/${siteId}/stats${queryString ? `?${queryString}` : ''}`;

      const result = await this.makeAuthenticatedRequest<{
        data: DashboardStats;
      }>(endpoint, token);
      return result.data;
    });
  }

  /**
   * ✅ Get recent activities
   */
  async getRecentActivities(
    siteId: string,
    token: string,
    limit: number = 10,
  ): Promise<ApiResponse<RecentActivity[]>> {
    if (!siteId?.trim()) {
      return {
        success: false,
        error: 'Site ID ไม่ถูกต้อง',
      };
    }

    if (!token?.trim()) {
      return {
        success: false,
        error: 'Token ไม่ถูกต้อง',
      };
    }

    return this.handleRequest(async () => {
      const endpoint = `/dashboard/${siteId}/activities?limit=${limit}`;

      const result = await this.makeAuthenticatedRequest<{
        data: RecentActivity[];
      }>(endpoint, token);
      return result.data;
    });
  }

  /**
   * ✅ Validate dashboard parameters
   */
  private validateDashboardParams(params: DashboardParams): string | null {
    if (params.period && !['today', 'week', 'month', 'year'].includes(params.period)) {
      return 'ช่วงเวลาไม่ถูกต้อง';
    }

    if (params.startDate && !this.isValidDate(params.startDate)) {
      return 'วันที่เริ่มต้นไม่ถูกต้อง';
    }

    if (params.endDate && !this.isValidDate(params.endDate)) {
      return 'วันที่สิ้นสุดไม่ถูกต้อง';
    }

    if (params.startDate && params.endDate) {
      const start = new Date(params.startDate);
      const end = new Date(params.endDate);
      if (start > end) {
        return 'วันที่เริ่มต้นต้องน้อยกว่าวันที่สิ้นสุด';
      }
    }

    return null;
  }

  /**
   * ✅ Build query parameters string
   */
  private buildQueryParams(params: DashboardParams): string {
    const queryParams = new URLSearchParams();

    if (params.period) {
      queryParams.append('period', params.period);
    }

    if (params.startDate) {
      queryParams.append('startDate', params.startDate);
    }

    if (params.endDate) {
      queryParams.append('endDate', params.endDate);
    }

    return queryParams.toString();
  }

  /**
   * ✅ Validate date format
   */
  private isValidDate(dateString: string): boolean {
    const date = new Date(dateString);
    return date instanceof Date && !Number.isNaN(date.getTime());
  }
}

export const dashboardService = new DashboardService();
