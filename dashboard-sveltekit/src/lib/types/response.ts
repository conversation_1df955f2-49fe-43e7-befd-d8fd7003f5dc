// ✅ Standard Response Types - ใช้ร่วมกันทั้งระบบ

/**
 * Standard Response Interface สำหรับทุก API response
 */
export interface StandardResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp: string;
  statusCode?: number;
}

/**
 * Form Action Response Interface สำหรับ SvelteKit form actions
 */
export interface FormActionResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  validationErrors?: Record<string, string>;
}

/**
 * API Error Response Interface
 */
export interface ApiErrorResponse {
  success: false;
  error: string;
  message?: string;
  timestamp: string;
  statusCode: number;
  detail?: any;
  stack?: string; // เฉพาะ development mode
}

/**
 * Pagination Response Interface
 */
export interface PaginatedResponse<T = any> {
  success: boolean;
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  message?: string;
  timestamp: string;
}

/**
 * Auth Response Interface
 */
export interface AuthResponse {
  success: boolean;
  data?: {
    user: any;
    token: string;
    refreshToken: string;
    sessionId?: string;
  };
  error?: string;
  message?: string;
  timestamp: string;
}

/**
 * Helper functions สำหรับสร้าง standard responses
 */
export class ResponseHelper {
  /**
   * สร้าง success response
   */
  static success<T>(data?: T, message?: string): StandardResponse<T> {
    return {
      success: true,
      data,
      message,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * สร้าง error response
   */
  static error(error: string, statusCode?: number): StandardResponse {
    return {
      success: false,
      error,
      timestamp: new Date().toISOString(),
      statusCode,
    };
  }

  /**
   * สร้าง form action success response
   */
  static formSuccess<T>(data?: T, message?: string): FormActionResponse<T> {
    return {
      success: true,
      data,
      message,
    };
  }

  /**
   * สร้าง form action error response
   */
  static formError(error: string, validationErrors?: Record<string, string>): FormActionResponse {
    return {
      success: false,
      error,
      validationErrors,
    };
  }

  /**
   * สร้าง paginated response
   */
  static paginated<T>(
    data: T[],
    page: number,
    limit: number,
    total: number,
    message?: string,
  ): PaginatedResponse<T> {
    const totalPages = Math.ceil(total / limit);

    return {
      success: true,
      data,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
      message,
      timestamp: new Date().toISOString(),
    };
  }
}

/**
 * Type guards สำหรับตรวจสอบ response type
 */
export function isSuccessResponse<T>(
  response: StandardResponse<T>,
): response is StandardResponse<T> & { success: true; } {
  return response.success === true;
}

export function isErrorResponse(
  response: StandardResponse,
): response is StandardResponse & { success: false; } {
  return response.success === false;
}

export function isFormSuccessResponse<T>(
  response: FormActionResponse<T>,
): response is FormActionResponse<T> & { success: true; } {
  return response.success === true;
}

export function isFormErrorResponse(
  response: FormActionResponse,
): response is FormActionResponse & { success: false; } {
  return response.success === false;
}
