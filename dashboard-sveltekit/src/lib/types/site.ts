export interface Site {
  _id: string;
  name: string; // ✅ ปรับให้ตรงกับ backend
  siteName?: string; // backward compatibility
  typeDomain: 'subdomain' | 'custom';
  subDomain?: string;
  mainDomain?: string;
  customDomain?: string;
  fullDomain?: string; // ✅ เพิ่ม fullDomain จาก backend
  plan: string; // ✅ ปรับให้ตรงกับ backend
  packageType?: string; // backward compatibility
  status: 'active' | 'inactive' | 'pending';
  isActive?: boolean; // ✅ เพิ่มจาก backend
  userId: string;
  expiredAt?: string; // ✅ เพิ่มจาก backend
  createdAt: string;
  updatedAt: string;
}

export interface CreateSiteData {
  siteName: string;
  typeDomain: 'subdomain' | 'custom';
  subDomain?: string;
  mainDomain?: string;
  customDomain?: string;
  packageType: string; // ✅ ปรับให้รองรับ package ID ต่างๆ
}

export type { ApiResponse } from './common';
