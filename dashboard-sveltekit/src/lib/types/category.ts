export interface Category {
  _id: string;
  target: 'product' | 'category' | 'page' | 'brand' | 'blog' | 'news';
  siteId: string;
  name: string;
  cover?: string;
  parentId?: string;
  createdAt: string;
  updatedAt: string;
  children?: Category[];
}

export interface CategoryListResponse {
  success: boolean;
  data?: Category[];
  error?: string;
}

export interface CategoryResponse {
  success: boolean;
  data?: Category | null;
  error?: string;
}
