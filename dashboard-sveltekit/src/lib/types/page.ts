import type { Site } from './site';
import type { PackageInfo, Subscription, SubscriptionNotification } from './subscription';

export interface PaginationData {
  page: number;
  totalPages: number;
  total: number;
  limit: number;
}

export interface NotificationPageData {
  site: Site | null;
  notifications: SubscriptionNotification[];
  pagination: PaginationData | null;
  error?: string | null;
  user?: any;
}

export interface SubscriptionPageData {
  site: Site | null;
  packages: PackageInfo[];
  subscriptions: Subscription[];
  pagination: PaginationData | null;
  notifications: SubscriptionNotification[];
  error?: string | null;
  user?: any;
}

export interface BasePageData {
  site: Site | null;
  error?: string | null;
  user?: any;
}

export interface PageProps<T = any> {
  data: T;
}
