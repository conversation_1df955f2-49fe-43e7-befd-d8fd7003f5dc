export type ProductType = 'physical' | 'digital' | 'service' | 'subscription';
export type SaleChannel = 'online' | 'offline' | 'both';

export interface ProductImage {
  url: string;
  alt?: string;
  isPrimary: boolean;
  variantId?: string; // ถ้าเป็นรูปของ variant เฉพาะ
}

export interface ProductVariant {
  _id: string;
  name: string; // เช่น "สีแดง ไซส์ M"
  sku?: string;
  price?: number; // ราคาเฉพาะ variant (ถ้าไม่มีจะใช้ราคาหลัก)
  stock?: number;
  attributes: Record<string, string>; // { "color": "red", "size": "M" }
  images?: string[];
  isActive: boolean;
}

export interface DigitalAsset {
  name: string;
  url: string;
  fileSize?: number;
  fileType?: string;
  downloadLimit?: number; // จำนวนครั้งที่ดาวน์โหลดได้
  expiryDays?: number; // วันหมดอายุหลังซื้อ
}

export interface Shipping {
  weight?: number; // กรัม
  dimensions?: {
    length: number; // ซม.
    width: number;
    height: number;
  };
  shippingClass?: string;
}

export interface Product {
  _id: string;
  siteId: string;
  name: string;
  slug: string; // URL-friendly name
  type: ProductType;
  saleChannel: SaleChannel;

  // ราคาและสต็อก
  price: number;
  compareAtPrice?: number; // ราคาเปรียบเทียบ (ราคาขีดฆ่า)
  costPrice?: number; // ต้นทุน
  stock?: number;
  trackStock: boolean;
  allowBackorder: boolean;

  // ข้อมูลพื้นฐาน
  categoryId?: string;
  description?: string;
  shortDescription?: string;
  tags?: string[];

  // รูปภาพ
  images: ProductImage[];

  // Variants (สี, ขนาด, etc.)
  hasVariants: boolean;
  variants: ProductVariant[];
  variantAttributes: string[]; // ["color", "size"]

  // Digital products
  digitalAssets: DigitalAsset[];

  // การจัดส่ง
  shipping: Shipping;

  // SEO
  seoTitle?: string;
  seoDescription?: string;

  // การแสดงผล
  featured: boolean;
  isActive: boolean;

  // Pre-order
  allowPreOrder: boolean;

  // ข้อมูลเพิ่มเติม
  customFields?: Record<string, any>;

  createdAt: string;
  updatedAt: string;
}

export interface ProductListResponse {
  success: boolean;
  data?: {
    products: Product[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
  error?: string;
}

export interface ProductResponse {
  success: boolean;
  data?: Product | null;
  error?: string;
}

export interface ProductStats {
  totalProducts: number;
  activeProducts: number;
  outOfStockProducts: number;
  lowStockProducts: number;
}

export interface ProductStatsResponse {
  success: boolean;
  data?: ProductStats;
  error?: string;
}
