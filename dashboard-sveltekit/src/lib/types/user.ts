export interface User {
  _id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  avatar?: string;
  cover?: string;
  isEmailVerified: boolean;
  moneyPoint: number;
  goldPoint: number;
  role?: 'admin' | 'user' | 'moderator';
  status?: 'active' | 'inactive';
  createdAt: string;
  updatedAt: string;
}

export interface UpdateUserData {
  firstName?: string;
  lastName?: string;
  avatar?: string;
  cover?: string;
}

export type { ApiResponse } from './common';
