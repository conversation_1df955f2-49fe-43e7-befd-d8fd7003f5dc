export interface DiscountValidationRequest {
  discountCode: string;
  target?: 'site' | 'product' | 'category' | 'package' | 'theme' | 'minigame';
  orderAmount?: number;
  items?: any[];
}

export interface DiscountInfo {
  code: string;
  discount: number;
  type: 'percentage' | 'fixed';
  name?: string;
  description?: string;
  minOrderAmount?: number;
  maxDiscountAmount?: number;
  isFirstTimeOnly?: boolean;
  validUntil?: string;
}

export type { ApiResponse } from './common';
