// ✅ Client-side configuration
export const clientConfig = {
  // App settings
  appName: 'WebShop',
  appVersion: '1.0.0',

  // Feature flags
  features: {
    analytics: true,
    notifications: true,
    multiLanguage: true,
    darkMode: true,
  },

  // UI settings
  ui: {
    defaultTheme: 'light',
    defaultLanguage: 'th',
    animationDuration: 300,
  },

  // Validation settings
  validation: {
    passwordMinLength: 6,
    emailRegex: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  },
} as const;

// ✅ Logger configuration
export const loggerConfig = {
  // Enable/disable different log levels
  levels: {
    debug: import.meta.env.VITE_LOG_DEBUG === 'true' || import.meta.env.DEV,
    info: import.meta.env.VITE_LOG_INFO === 'true' || import.meta.env.DEV,
    warn: import.meta.env.VITE_LOG_WARN === 'true' || true,
    error: import.meta.env.VITE_LOG_ERROR === 'true' || true,
    critical: import.meta.env.VITE_LOG_CRITICAL === 'true' || true,
  },

  // Enable/disable specific log categories
  categories: {
    auth: import.meta.env.VITE_LOG_AUTH === 'true' || import.meta.env.DEV,
    api: import.meta.env.VITE_LOG_API === 'true' || import.meta.env.DEV,
    security: import.meta.env.VITE_LOG_SECURITY === 'true' || true,
    performance: import.meta.env.VITE_LOG_PERFORMANCE === 'true' || import.meta.env.DEV,
    user: import.meta.env.VITE_LOG_USER === 'true' || import.meta.env.DEV,
    system: import.meta.env.VITE_LOG_SYSTEM === 'true' || import.meta.env.DEV,
  },

  // Console output settings
  console: {
    enabled: import.meta.env.VITE_LOG_CONSOLE === 'true' || import.meta.env.DEV,
    showTimestamp: import.meta.env.VITE_LOG_TIMESTAMP === 'true' || import.meta.env.DEV,
    showCategory: import.meta.env.VITE_LOG_CATEGORY === 'true' || import.meta.env.DEV,
  },

  // File output settings
  file: {
    enabled: import.meta.env.VITE_LOG_FILE === 'true' || false,
    maxSize: parseInt(import.meta.env.VITE_LOG_MAX_SIZE || '10') * 1024 * 1024, // MB
    maxFiles: parseInt(import.meta.env.VITE_LOG_MAX_FILES || '5'),
  },
} as const;

// ✅ API configuration - ใช้ได้ทั้ง client และ server
export const backendApiUrl = import.meta.env.VITE_BACKEND_API_URL + import.meta.env.VITE_BACKEND_VERSION
  || 'http://localhost:5000/v1';

// ✅ Computed API URL - ใช้ function เพื่อให้ได้ค่าที่ถูกต้อง
export const apiUrl = (() => {
  const baseUrl = import.meta.env.VITE_BACKEND_URL || 'http://localhost:5000';
  const version = import.meta.env.VITE_BACKEND_VERSION || '/v1';

  // ตรวจสอบว่า baseUrl ลงท้ายด้วย / หรือไม่
  const cleanBaseUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;
  // ตรวจสอบว่า version ขึ้นต้นด้วย / หรือไม่
  const cleanVersion = version.startsWith('/') ? version : `/${version}`;

  return `${cleanBaseUrl}${cleanVersion}`;
})();

// ✅ Environment configuration
export const env = {
  isDev: import.meta.env.DEV,
  isProd: import.meta.env.PROD,
  isTest: import.meta.env.MODE === 'test',
} as const;

// ✅ Helper functions for common configurations
export const getApiEndpoint = (path: string): string => {
  const cleanPath = path.startsWith('/') ? path : `/${path}`;
  return `${apiUrl}${cleanPath}`;
};

// ✅ Helper function to check if logging is enabled
export const isLoggingEnabled = (
  level: keyof typeof loggerConfig.levels,
  category?: keyof typeof loggerConfig.categories,
): boolean => {
  const levelEnabled = loggerConfig.levels[level];
  if (!levelEnabled) return false;

  if (category) {
    return loggerConfig.categories[category];
  }

  return true;
};

// ✅ Type exports
export type ClientConfig = typeof clientConfig;
export type LoggerConfig = typeof loggerConfig;
export type Env = typeof env;
