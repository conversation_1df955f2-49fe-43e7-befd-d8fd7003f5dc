/* SweetAlert2 Customization */
.swal2-popup {
	font-family: 'Sarabun', sans-serif !important;
	border-radius: 0.5rem !important;
}
div:where(.swal2-container) h2:where(.swal2-title) {
	@apply !text-2xl !font-bold !p-0 !mx-0 !mb-2;
}
div:where(.swal2-container) div:where(.swal2-html-container) {
	@apply !text-base !font-normal !p-1 !mx-0 !bg-base-100/10 !rounded-lg;
}
div:where(.swal2-container) div:where(.swal2-actions) {
	@apply !p-0 !mt-2;
}
div:where(.swal2-container) div:where(.swal2-popup) {
	@apply !p-2 !m-0 !max-w-sm !w-full;
}
div:where(.swal2-icon) {
	@apply !mt-2;
}
div:where(.swal2-container) button:where(.swal2-close) {
	@apply !text-white/50 hover:!text-white !font-bold !p-0 !m-0;
}
/* div:where(.swal2-container) button:where(.swal2-confirm) {
	@apply !text-white/50 hover:!text-white !font-bold !p-0 !m-0 !bg-success hover:!bg-success/90; 
}
div:where(.swal2-container) button:where(.swal2-cancel) {
	@apply !text-white/50 hover:!text-white !font-bold !p-0 !m-0 !bg-error hover:!bg-error/90 !border-error; 
} */

.swal2-title {
	font-family: 'Sarabun', sans-serif !important;
	font-weight: 600 !important;
}

.swal2-html-container {
	font-family: 'Sarabun', sans-serif !important;
}

.swal2-confirm {
	background-color: hsl(var(--primary)) !important;
	color: hsl(var(--primary-foreground)) !important;
	border-radius: 0.375rem !important;
	font-family: 'Sarabun', sans-serif !important;
	font-weight: 500 !important;
	padding: 0.5rem 1rem !important;
	border: none !important;
}

.swal2-cancel {
	background-color: hsl(var(--secondary)) !important;
	color: hsl(var(--secondary-foreground)) !important;
	border-radius: 0.375rem !important;
	font-family: 'Sarabun', sans-serif !important;
	font-weight: 500 !important;
	padding: 0.5rem 1rem !important;
	border: none !important;
}

.swal2-confirm:hover {
	background-color: hsl(var(--primary) / 0.9) !important;
}

.swal2-cancel:hover {
	background-color: hsl(var(--secondary) / 0.9) !important;
}

/* Status-based background colors for modals */
.swal2-popup.swal2-icon-error {
	background-color: var(--color-error) !important;
	border: 1px solid var(--color-error) !important;
	color: var(--color-error-content) !important;
}

.swal2-popup.swal2-icon-success {
	background-color: var(--color-success) !important;
	border: 1px solid var(--color-success) !important;
	color: var(--color-success-content) !important;
}

.swal2-popup.swal2-icon-warning {
	background-color: var(--color-warning) !important;
	border: 1px solid var(--color-warning) !important;
	color: var(--color-warning-content) !important;
}

.swal2-popup.swal2-icon-info {
	background-color: var(--color-info) !important;
	border: 1px solid var(--color-info) !important;
	color: var(--color-info-content) !important;
}

.swal2-popup.swal2-icon-question {
	background-color: var(--color-neutral) !important;
	border: 1px solid var(--color-neutral) !important;
	color: var(--color-neutral-content) !important;
}

/* สีไอคอนและข้อความเป็นสีขาว */
.swal2-popup .swal2-icon {
	color: #ffffff !important;
}

.swal2-popup .swal2-title {
	color: #ffffff !important;
}

.swal2-popup .swal2-html-container {
	color: #ffffff !important;
}

/* สีไอคอนเฉพาะแต่ละประเภท */
.swal2-popup .swal2-icon.swal2-error {
	color: #ffffff !important;
}

.swal2-popup .swal2-icon.swal2-error .swal2-x-mark {
	color: #ffffff !important;
}

.swal2-popup .swal2-icon.swal2-error .swal2-x-mark-line-left,
.swal2-popup .swal2-icon.swal2-error .swal2-x-mark-line-right {
	background-color: #ffffff !important;
}

.swal2-popup .swal2-icon.swal2-success {
	color: #ffffff !important;
}

.swal2-popup .swal2-icon.swal2-success .swal2-success-ring {
	border-color: #ffffff !important;
}

.swal2-popup .swal2-icon.swal2-success .swal2-success-fix {
	/* background-color: #ffffff !important; */
}

.swal2-popup .swal2-icon.swal2-success .swal2-success-line-tip,
.swal2-popup .swal2-icon.swal2-success .swal2-success-line-long {
	background-color: #ffffff !important;
}

.swal2-popup .swal2-icon.swal2-warning {
	color: #ffffff !important;
}

.swal2-popup .swal2-icon.swal2-warning .swal2-warning-body {
	color: #ffffff !important;
}

.swal2-popup .swal2-icon.swal2-info {
	color: #ffffff !important;
}

.swal2-popup .swal2-icon.swal2-info .swal2-info-body {
	color: #ffffff !important;
}

.swal2-popup .swal2-icon.swal2-question {
	color: #ffffff !important;
}

.swal2-popup .swal2-icon.swal2-question .swal2-question-body {
	color: #ffffff !important;
}

/* Status-based background colors for toasts */
div:where(.swal2-icon).swal2-error {
	background-color: var(--color-error) !important;
	border: 3px solid var(--color-error-content) !important;
	color: var(--color-error-content) !important;
}

div:where(.swal2-icon).swal2-success {
	background-color: var(--color-success) !important;
	border: 3px solid var(--color-success-content) !important;
	color: var(--color-success-content) !important;
}

div:where(.swal2-icon).swal2-success .swal2-success-ring {
	border: 3px solid var(--color-success-content) !important;
}

div:where(.swal2-icon).swal2-warning {
	background-color: var(--color-warning) !important;
	border: 3px solid var(--color-warning-content) !important;
	color: var(--color-warning-content) !important;
}

div:where(.swal2-icon).swal2-info {
	background-color: var(--color-info) !important;
	border: 3px solid var(--color-info-content) !important;
	color: var(--color-info-content) !important;
}

div:where(.swal2-icon).swal2-question {
	background-color: var(--color-neutral) !important;
	border: 3px solid var(--color-neutral-content) !important;
	color: var(--color-neutral-content) !important;
}

.swal2-toast.swal2-icon-error {
	background-color: var(--color-error) !important;
	border: 0px solid var(--color-error-content) !important;
	color: var(--color-error-content) !important;
}

.swal2-toast.swal2-icon-success {
	background-color: var(--color-success) !important;
	border: 0px solid var(--color-success-content) !important;
	color: var(--color-success-content) !important;
}

.swal2-toast.swal2-icon-warning {
	background-color: var(--color-warning) !important;
	border: 0px solid var(--color-warning-content) !important;
	color: var(--color-warning-content) !important;
}

.swal2-toast.swal2-icon-info {
	background-color: var(--color-info) !important;
	border: 0px solid var(--color-info-content) !important;
	color: var(--color-info-content) !important;
}

.swal2-toast.swal2-icon-question {
	background-color: var(--color-neutral) !important;
	border: 0px solid var(--color-neutral-content) !important;
	color: var(--color-neutral-content) !important;
}

/* สีไอคอนและข้อความเป็นสีขาวสำหรับ toast */
.swal2-toast .swal2-icon {
	color: #ffffff !important;
}

.swal2-toast .swal2-title {
	@apply !text-lg !font-semibold !p-0 !mx-0;
}

.swal2-toast .swal2-html-container {
	color: #ffffff !important;
}

/* สีไอคอนเฉพาะแต่ละประเภทสำหรับ toast */
.swal2-toast .swal2-icon.swal2-error {
	color: #ffffff !important;
}

.swal2-toast .swal2-icon.swal2-error .swal2-x-mark {
	color: #ffffff !important;
}

.swal2-toast .swal2-icon.swal2-error .swal2-x-mark-line-left,
.swal2-toast .swal2-icon.swal2-error .swal2-x-mark-line-right {
	background-color: #ffffff !important;
}

.swal2-toast .swal2-icon.swal2-success {
	color: #ffffff !important;
}

.swal2-toast .swal2-icon.swal2-success .swal2-success-ring {
	border-color: #ffffff !important;
}

.swal2-toast .swal2-icon.swal2-success .swal2-success-fix {
	/* background-color: #ffffff !important; */
}

.swal2-toast .swal2-icon.swal2-success .swal2-success-line-tip,
.swal2-toast .swal2-icon.swal2-success .swal2-success-line-long {
	background-color: #ffffff !important;
}

.swal2-toast .swal2-icon.swal2-warning {
	color: #ffffff !important;
}

.swal2-toast .swal2-icon.swal2-warning .swal2-warning-body {
	color: #ffffff !important;
}

.swal2-toast .swal2-icon.swal2-info {
	color: #ffffff !important;
}

.swal2-toast .swal2-icon.swal2-info .swal2-info-body {
	color: #ffffff !important;
}

.swal2-toast .swal2-icon.swal2-question {
	color: #ffffff !important;
}

.swal2-toast .swal2-icon.swal2-question .swal2-question-body {
	color: #ffffff !important;
}

/* Dark theme adjustments */
[data-theme='dark'] .swal2-popup {
	background-color: hsl(var(--card)) !important;
	color: hsl(var(--card-foreground)) !important;
	border: 1px solid hsl(var(--border)) !important;
}

[data-theme='dark'] .swal2-title {
	color: hsl(var(--card-foreground)) !important;
}

[data-theme='dark'] .swal2-html-container {
	color: hsl(var(--card-foreground)) !important;
	/* color: hsl(var(--muted-foreground)) !important; */
}

/* Dark theme status-based colors for modals */
[data-theme='dark'] .swal2-popup.swal2-icon-error {
	background-color: var(--color-error) !important;
	border: 0px solid var(--color-error-content) !important;
	color: var(--color-error-content) !important;
}

[data-theme='dark'] .swal2-popup.swal2-icon-success {
	background-color: var(--color-success) !important;
	border: 0px solid var(--color-success-content) !important;
	color: var(--color-success-content) !important;
}

[data-theme='dark'] .swal2-popup.swal2-icon-warning {
	background-color: var(--color-warning) !important;
	border: 0px solid var(--color-warning-content) !important;
	color: var(--color-warning-content) !important;
}

[data-theme='dark'] .swal2-popup.swal2-icon-info {
	background-color: var(--color-info) !important;
	border: 0px solid var(--color-info-content) !important;
	color: var(--color-info-content) !important;
}

[data-theme='dark'] .swal2-popup.swal2-icon-question {
	background-color: var(--color-neutral) !important;
	border: 0px solid var(--color-neutral-content) !important;
	color: var(--color-neutral-content) !important;
}

/* สีไอคอนเฉพาะแต่ละประเภทสำหรับ dark theme */
[data-theme='dark'] .swal2-popup .swal2-icon.swal2-error {
	color: #ffffff !important;
}

[data-theme='dark'] .swal2-popup .swal2-icon.swal2-error .swal2-x-mark {
	color: #ffffff !important;
}

[data-theme='dark'] .swal2-popup .swal2-icon.swal2-error .swal2-x-mark-line-left,
[data-theme='dark'] .swal2-popup .swal2-icon.swal2-error .swal2-x-mark-line-right {
	background-color: #ffffff !important;
}

[data-theme='dark'] .swal2-popup .swal2-icon.swal2-success {
	color: #ffffff !important;
}

[data-theme='dark'] .swal2-popup .swal2-icon.swal2-success .swal2-success-ring {
	border-color: #ffffff !important;
}

[data-theme='dark'] .swal2-popup .swal2-icon.swal2-success .swal2-success-fix {
	/* background-color: #ffffff !important; */
}

[data-theme='dark'] .swal2-popup .swal2-icon.swal2-success .swal2-success-line-tip,
[data-theme='dark'] .swal2-popup .swal2-icon.swal2-success .swal2-success-line-long {
	background-color: #ffffff !important;
}

[data-theme='dark'] .swal2-popup .swal2-icon.swal2-warning {
	color: #ffffff !important;
}

[data-theme='dark'] .swal2-popup .swal2-icon.swal2-warning .swal2-warning-body {
	color: #ffffff !important;
}

[data-theme='dark'] .swal2-popup .swal2-icon.swal2-info {
	color: #ffffff !important;
}

[data-theme='dark'] .swal2-popup .swal2-icon.swal2-info .swal2-info-body {
	color: #ffffff !important;
}

[data-theme='dark'] .swal2-popup .swal2-icon.swal2-question {
	color: #ffffff !important;
}

[data-theme='dark'] .swal2-popup .swal2-icon.swal2-question .swal2-question-body {
	color: #ffffff !important;
}

/* Dark theme status-based colors for toasts */
[data-theme='dark'] .swal2-toast.swal2-icon-error {
	background-color: var(--color-error) !important;
	border: 0px solid var(--color-error-content) !important;
	color: var(--color-error-content) !important;
}

[data-theme='dark'] .swal2-toast.swal2-icon-success {
	background-color: var(--color-success) !important;
	border: 0px solid var(--color-success-content) !important;
	color: var(--color-success-content) !important;
}

[data-theme='dark'] .swal2-toast.swal2-icon-warning {
	background-color: var(--color-warning) !important;
	border: 0px solid var(--color-warning-content) !important;
	color: var(--color-warning-content) !important;
}

[data-theme='dark'] .swal2-toast.swal2-icon-info {
	background-color: var(--color-info) !important;
	border: 0px solid var(--color-info-content) !important;
	color: var(--color-info-content) !important;
}

[data-theme='dark'] .swal2-toast.swal2-icon-question {
	background-color: var(--color-neutral) !important;
	border: 0px solid var(--color-neutral) !important;
	color: var(--color-neutral-content) !important;
}

/* สีไอคอนและข้อความเป็นสีขาวสำหรับ toast ใน dark theme */
[data-theme='dark'] .swal2-toast .swal2-icon {
	color: #ffffff !important;
}

[data-theme='dark'] .swal2-toast .swal2-title {
	@apply !text-lg !font-semibold !p-0 !mx-0;
}

[data-theme='dark'] .swal2-toast .swal2-html-container {
	color: #ffffff !important;
}

/* สีไอคอนเฉพาะแต่ละประเภทสำหรับ toast ใน dark theme */
[data-theme='dark'] .swal2-toast .swal2-icon.swal2-error {
	color: #ffffff !important;
}

[data-theme='dark'] .swal2-toast .swal2-icon.swal2-error .swal2-x-mark {
	color: #ffffff !important;
}

[data-theme='dark'] .swal2-toast .swal2-icon.swal2-error .swal2-x-mark-line-left,
[data-theme='dark'] .swal2-toast .swal2-icon.swal2-error .swal2-x-mark-line-right {
	background-color: #ffffff !important;
}

[data-theme='dark'] .swal2-toast .swal2-icon.swal2-success {
	color: #ffffff !important;
}

[data-theme='dark'] .swal2-toast .swal2-icon.swal2-success .swal2-success-ring {
	border-color: #ffffff !important;
}

[data-theme='dark'] .swal2-toast .swal2-icon.swal2-success .swal2-success-fix {
	/* background-color: #ffffff !important; */
}

[data-theme='dark'] .swal2-toast .swal2-icon.swal2-success .swal2-success-line-tip,
[data-theme='dark'] .swal2-toast .swal2-icon.swal2-success .swal2-success-line-long {
	background-color: #ffffff !important;
}

[data-theme='dark'] .swal2-toast .swal2-icon.swal2-warning {
	color: #ffffff !important;
}

[data-theme='dark'] .swal2-toast .swal2-icon.swal2-warning .swal2-warning-body {
	color: #ffffff !important;
}

[data-theme='dark'] .swal2-toast .swal2-icon.swal2-info {
	color: #ffffff !important;
}

[data-theme='dark'] .swal2-toast .swal2-icon.swal2-info .swal2-info-body {
	color: #ffffff !important;
}

[data-theme='dark'] .swal2-toast .swal2-icon.swal2-question {
	color: #ffffff !important;
}

[data-theme='dark'] .swal2-toast .swal2-icon.swal2-question .swal2-question-body {
	color: #ffffff !important;
}

/* Toast customization */
.swal2-toast {
	border-radius: 0.5rem !important;
	font-family: 'Sarabun', sans-serif !important;
	/* ตรวจสอบให้แน่ใจว่า toast จะปิดได้ */
	transition:
		opacity 0.3s ease,
		transform 0.3s ease !important;
}

/* เพิ่ม CSS สำหรับปิด toast */
.swal2-toast.swal2-hide {
	opacity: 0 !important;
	transform: translateX(100%) !important;
	pointer-events: none !important;
}

/* Animation improvements - ปิด animation เพื่อให้ toast ปิดได้ปกติ */
.swal2-popup {
	/* animation: swal2-show 0.3s ease-out !important; */
}

.swal2-toast {
	/* animation: swal2-toast-show 0.3s ease-out !important; */
}
