interface ToastItem {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title?: string;
  message: string;
  duration?: number;
  closable?: boolean;
}

class ToastStore {
  private toasts = $state<ToastItem[]>([]);

  get items() {
    return this.toasts;
  }

  private generateId(): string {
    return Math.random().toString(36).substr(2, 9);
  }

  add(toast: Omit<ToastItem, 'id'>): string {
    const id = this.generateId();
    const newToast: ToastItem = {
      id,
      duration: 5000,
      closable: true,
      ...toast,
    };

    this.toasts.push(newToast);

    // Auto remove after duration
    if (newToast.duration && newToast.duration > 0) {
      setTimeout(() => {
        this.remove(id);
      }, newToast.duration);
    }

    return id;
  }

  remove(id: string): void {
    this.toasts = this.toasts.filter(toast => toast.id !== id);
  }

  clear(): void {
    this.toasts = [];
  }

  // Convenience methods
  success(message: string, title?: string, options?: Partial<ToastItem>): string {
    return this.add({
      type: 'success',
      message,
      title,
      ...options,
    });
  }

  error(message: string, title?: string, options?: Partial<ToastItem>): string {
    return this.add({
      type: 'error',
      message,
      title,
      ...options,
    });
  }

  warning(message: string, title?: string, options?: Partial<ToastItem>): string {
    return this.add({
      type: 'warning',
      message,
      title,
      ...options,
    });
  }

  info(message: string, title?: string, options?: Partial<ToastItem>): string {
    return this.add({
      type: 'info',
      message,
      title,
      ...options,
    });
  }
}

export const toastStore = new ToastStore();
export type { ToastItem };
