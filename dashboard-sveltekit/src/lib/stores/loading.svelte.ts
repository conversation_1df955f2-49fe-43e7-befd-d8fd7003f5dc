import { type LoadingConfig, loadingConfig, type LoadingPreset, loadingPresets } from '$lib/config/loading';

// Loading state interface
interface LoadingState {
  isLoading: boolean;
  progress: number;
  message: string;
  type: LoadingConfig['type'];
  size: LoadingConfig['size'];
  color: LoadingConfig['color'];
  showProgress: boolean;
  overlay: boolean;
  blur: boolean;
  animation: LoadingConfig['animation'];
  duration: number;
  startTime: number;
  id: string;
}

// Loading queue item
interface LoadingQueueItem {
  id: string;
  message: string;
  progress?: number;
  options?: Partial<LoadingState>;
  priority?: number;
}

class LoadingStore {
  private state = $state<LoadingState>({
    isLoading: false,
    progress: 0,
    message: loadingConfig.message,
    type: loadingConfig.type,
    size: loadingConfig.size,
    color: loadingConfig.color,
    showProgress: loadingConfig.showProgress,
    overlay: loadingConfig.overlay,
    blur: loadingConfig.blur,
    animation: loadingConfig.animation,
    duration: loadingConfig.duration,
    startTime: 0,
    id: '',
  });

  private queue: LoadingQueueItem[] = [];
  private timeouts = new Map<string, NodeJS.Timeout>();

  // Getters for reactive state
  get isLoading() {
    return this.state.isLoading;
  }
  get progress() {
    return this.state.progress;
  }
  get message() {
    return this.state.message;
  }
  get type() {
    return this.state.type;
  }
  get size() {
    return this.state.size;
  }
  get color() {
    return this.state.color;
  }
  get showProgress() {
    return this.state.showProgress;
  }
  get overlay() {
    return this.state.overlay;
  }
  get blur() {
    return this.state.blur;
  }
  get animation() {
    return this.state.animation;
  }
  get duration() {
    return this.state.duration;
  }
  get currentId() {
    return this.state.id;
  }

  // Show loading with options
  show(message?: string, options?: Partial<LoadingState>): string {
    if (loadingConfig.disabled) return '';

    const id = this.generateId();
    const loadingItem: LoadingQueueItem = {
      id,
      message: message || loadingConfig.message,
      options: {
        ...options,
        startTime: Date.now(),
      },
    };

    // Add to queue
    this.queue.push(loadingItem);
    this.processQueue();

    return id;
  }

  // Show with preset configuration
  showWithPreset(preset: LoadingPreset, message?: string): string {
    const presetConfig = loadingPresets[preset];
    return this.show(message || presetConfig.message, presetConfig);
  }

  // Hide specific loading by ID
  async hide(id?: string): Promise<void> {
    if (!id) {
      // Hide current loading
      await this.hideCurrent();
      return;
    }

    // Remove from queue
    this.queue = this.queue.filter(item => item.id !== id);

    // Clear timeout if exists
    const timeout = this.timeouts.get(id);
    if (timeout) {
      clearTimeout(timeout);
      this.timeouts.delete(id);
    }

    // If hiding current loading, process next in queue
    if (this.state.id === id) {
      await this.hideCurrent();
    }
  }

  // Hide current loading
  private async hideCurrent(): Promise<void> {
    if (!this.state.isLoading) return;

    const elapsed = Date.now() - this.state.startTime;
    const remaining = Math.max(0, loadingConfig.minDisplayTime - elapsed);

    if (remaining > 0) {
      await new Promise(resolve => setTimeout(resolve, remaining));
    }

    this.state.isLoading = false;
    this.state.progress = 0;
    this.state.id = '';

    // Process next in queue
    this.processQueue();
  }

  // Hide all loading states
  hideAll(): void {
    this.queue = [];
    this.timeouts.forEach(timeout => clearTimeout(timeout));
    this.timeouts.clear();

    this.state.isLoading = false;
    this.state.progress = 0;
    this.state.id = '';
  }

  // Update progress
  setProgress(progress: number, message?: string, id?: string): void {
    // If ID specified, update queue item
    if (id) {
      const queueItem = this.queue.find(item => item.id === id);
      if (queueItem) {
        queueItem.progress = Math.max(0, Math.min(100, progress));
        if (message) queueItem.message = message;
      }
    }

    // Update current loading if it matches
    if (!id || this.state.id === id) {
      this.state.progress = Math.max(0, Math.min(100, progress));
      if (message) this.state.message = message;

      // Auto hide when progress reaches 100%
      if (progress >= 100 && loadingConfig.autoHide) {
        const timeoutId = setTimeout(() => {
          this.hide(id || this.state.id);
        }, loadingConfig.autoHideDelay);

        if (id) {
          this.timeouts.set(id, timeoutId);
        }
      }
    }
  }

  // Update message
  setMessage(message: string, id?: string): void {
    if (id) {
      const queueItem = this.queue.find(item => item.id === id);
      if (queueItem) {
        queueItem.message = message;
      }
    }

    if (!id || this.state.id === id) {
      this.state.message = message;
    }
  }

  // Update loading configuration
  updateConfig(config: Partial<LoadingState>, id?: string): void {
    if (id) {
      const queueItem = this.queue.find(item => item.id === id);
      if (queueItem) {
        queueItem.options = { ...queueItem.options, ...config };
      }
    }

    if (!id || this.state.id === id) {
      Object.assign(this.state, config);
    }
  }

  // Process loading queue
  private processQueue(): void {
    if (this.state.isLoading || this.queue.length === 0) return;

    // Sort by priority (higher priority first)
    this.queue.sort((a, b) => (b.priority || 0) - (a.priority || 0));

    const nextItem = this.queue.shift();
    if (!nextItem) return;

    // Apply configuration
    this.state.isLoading = true;
    this.state.message = nextItem.message;
    this.state.progress = nextItem.progress || 0;
    this.state.id = nextItem.id;
    this.state.startTime = Date.now();

    // Apply options
    if (nextItem.options) {
      Object.assign(this.state, nextItem.options);
    }
  }

  // Generate unique ID
  private generateId(): string {
    return `loading_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Utility methods for common loading scenarios
  showSaving(message = loadingConfig.customMessages.saving): string {
    return this.show(message, { type: 'spinner', color: 'success' });
  }

  showUploading(message = loadingConfig.customMessages.uploading): string {
    return this.show(message, { type: 'progress', showProgress: true });
  }

  showProcessing(message = loadingConfig.customMessages.processing): string {
    return this.show(message, { type: 'bars', color: 'info' });
  }

  showConnecting(message = loadingConfig.customMessages.connecting): string {
    return this.show(message, { type: 'pulse', color: 'warning' });
  }

  showAuthenticating(message = loadingConfig.customMessages.authenticating): string {
    return this.show(message, { type: 'dots', color: 'primary' });
  }

  showInitializing(message = loadingConfig.customMessages.initializing): string {
    return this.showWithPreset('fullscreen', message);
  }

  // Promise wrapper for async operations
  async withLoading<T>(
    promise: Promise<T>,
    message?: string,
    options?: Partial<LoadingState>,
  ): Promise<T> {
    const id = this.show(message, options);
    try {
      const result = await promise;
      await this.hide(id);
      return result;
    }
    catch (error) {
      await this.hide(id);
      throw error;
    }
  }

  // Progress wrapper for operations with progress tracking
  async withProgress<T>(
    operation: (updateProgress: (progress: number, message?: string) => void) => Promise<T>,
    message?: string,
    options?: Partial<LoadingState>,
  ): Promise<T> {
    const id = this.show(message, { ...options, showProgress: true });

    const updateProgress = (progress: number, msg?: string) => {
      this.setProgress(progress, msg, id);
    };

    try {
      const result = await operation(updateProgress);
      await this.hide(id);
      return result;
    }
    catch (error) {
      await this.hide(id);
      throw error;
    }
  }
}

// Create and export the loading store instance
export const loadingStore = new LoadingStore();

// Export helper functions for easier usage
export const loading = {
  show: (message?: string, options?: Partial<LoadingState>) => loadingStore.show(message, options),
  hide: (id?: string) => loadingStore.hide(id),
  hideAll: () => loadingStore.hideAll(),
  setProgress: (progress: number, message?: string, id?: string) => loadingStore.setProgress(progress, message, id),
  setMessage: (message: string, id?: string) => loadingStore.setMessage(message, id),

  // Preset methods
  saving: (message?: string) => loadingStore.showSaving(message),
  uploading: (message?: string) => loadingStore.showUploading(message),
  processing: (message?: string) => loadingStore.showProcessing(message),
  connecting: (message?: string) => loadingStore.showConnecting(message),
  authenticating: (message?: string) => loadingStore.showAuthenticating(message),
  initializing: (message?: string) => loadingStore.showInitializing(message),

  // Async wrappers
  withLoading: <T>(promise: Promise<T>, message?: string, options?: Partial<LoadingState>) =>
    loadingStore.withLoading(promise, message, options),
  withProgress: <T>(
    operation: (updateProgress: (progress: number, message?: string) => void) => Promise<T>,
    message?: string,
    options?: Partial<LoadingState>,
  ) => loadingStore.withProgress(operation, message, options),
};

// Export types
export type { LoadingQueueItem, LoadingState };
