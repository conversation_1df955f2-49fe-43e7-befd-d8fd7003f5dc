<script lang="ts">
	import Icon from '@iconify/svelte';

	interface Props {
		placeholder?: string;
		value?: string;
		disabled?: boolean;
		className?: string;
		onChange?: (value: string) => void;
		onSearch?: (value: string) => void;
		debounceMs?: number;
		showClearButton?: boolean;
	}

	const {
		placeholder = 'ค้นหา...',
		value = $bindable(''),
		disabled = false,
		className = '',
		onChange,
		onSearch,
		debounceMs = 300,
		showClearButton = true,
	}: Props = $props();

	let inputValue = $state(value);
	let debounceTimer: ReturnType<typeof setTimeout> | null = $state(null);

	// ฟังก์ชันสำหรับจัดการการเปลี่ยนแปลงค่า
	function handleInputChange(event: Event) {
		const target = event.target as HTMLInputElement;
		const newValue = target.value;
		inputValue = newValue;
		onChange?.(newValue);

		// ใช้ debounce สำหรับ onSearch
		if (onSearch && debounceMs > 0) {
			if (debounceTimer) {
				clearTimeout(debounceTimer);
			}
			debounceTimer = setTimeout(() => {
				onSearch(newValue);
			}, debounceMs);
		}
	}

	// ฟังก์ชันสำหรับล้างค่า
	function clearSearch() {
		inputValue = '';
		onChange?.('');
		onSearch?.('');
	}

	// ฟังก์ชันสำหรับจัดการการกด Enter
	function handleKeyDown(event: KeyboardEvent) {
		if (event.key === 'Enter') {
			onSearch?.(inputValue);
		}
	}

	// ฟังก์ชันสำหรับจัดการการคลิกปุ่มค้นหา
	function handleSearchClick() {
		onSearch?.(inputValue);
	}

	// ติดตามการเปลี่ยนแปลงของ value prop
	$effect(() => {
		if (value !== inputValue) {
			inputValue = value;
		}
	});
</script>

<div class="relative {className}">
	<div class="relative">
		<Icon
			icon="lucide:search"
			class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"
		/>
		<input
			type="text"
			placeholder={placeholder}
			bind:value={inputValue}
			{disabled}
			onchange={handleInputChange}
			onkeydown={handleKeyDown}
			class="w-full pl-10 pr-10 py-2 border border-input bg-background text-sm rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
		/>
		<div class="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center gap-1">
			{#if showClearButton && inputValue}
				<button
					type="button"
					onclick={clearSearch}
					class="p-1 hover:bg-muted rounded-sm transition-colors"
					title="ล้างการค้นหา"
				>
					<Icon icon="lucide:x" class="h-4 w-4 text-muted-foreground" />
				</button>
			{/if}
			<button
				type="button"
				onclick={handleSearchClick}
				class="p-1 hover:bg-muted rounded-sm transition-colors"
				title="ค้นหา"
			>
				<Icon icon="lucide:arrow-right" class="h-4 w-4 text-muted-foreground" />
			</button>
		</div>
	</div>
</div> 