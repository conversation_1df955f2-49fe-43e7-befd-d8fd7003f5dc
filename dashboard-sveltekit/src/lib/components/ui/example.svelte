<script lang="ts">
import Icon from '@iconify/svelte';
import {
	<PERSON><PERSON>,
	Badge,
	Button,
	Card,
	Checkbox,
	DateTime,
	Dropdown,
	Input,
	Label,
	Modal,
	Radio,
	Select,
	Table,
	Textarea,
	Toggle,
	Upload,
} from './index';

// State for examples
const showModal = $state(false);
const selectedValue = $state('');
const checkboxValue = $state(false);
const toggleValue = $state(false);
const dateValue = $state('');
const timeValue = $state('');
let uploadedFiles = $state<File[]>([]);

// Table data
const tableData = $state([
	{ id: 1, name: '<PERSON>', email: '<EMAIL>', status: 'active' },
	{ id: 2, name: '<PERSON>', email: '<EMAIL>', status: 'inactive' },
]);

const tableColumns = [
	{ key: 'name', label: 'ชื่อ', sortable: true },
	{ key: 'email', label: 'อีเมล' },
	{
		key: 'status',
		label: 'สถานะ',
		render: (value: string) =>
			`<span class="badge badge-${value === 'active' ? 'success' : 'neutral'}">${value}</span>`,
	},
];

// Select options
const selectOptions = [
	{ value: 'option1', label: 'ตัวเลือก 1', icon: 'mdi:star' },
	{ value: 'option2', label: 'ตัวเลือก 2', icon: 'mdi:heart' },
	{ value: 'option3', label: 'ตัวเลือก 3', icon: 'mdi:bookmark' },
];

// Dropdown items
const dropdownItems = [
	{ label: 'แก้ไข', icon: 'mdi:pencil', onClick: () => console.log('แก้ไข') },
	{ label: 'ลบ', icon: 'mdi:delete', onClick: () => console.log('ลบ') },
	{ divider: true },
	{ label: 'ส่งออก', icon: 'mdi:download', onClick: () => console.log('ส่งออก') },
];

function handleUpload(files: File[]) {
	uploadedFiles = files;
	console.log('อัปโหลดไฟล์:', files);
}

function handleTableSort(key: string, direction: 'asc' | 'desc') {
	console.log('เรียงลำดับ:', key, direction);
}
</script>

<div class="p-6 space-y-8">
	<h1 class="text-3xl font-bold mb-8">UI Components Examples</h1>

	<!-- Buttons -->
	<Card title="Buttons">
		<div class="flex flex-wrap gap-4">
			<Button color="primary">Primary</Button>
			<Button variant="secondary">Secondary</Button>
			<Button variant="accent">Accent</Button>
			<Button variant="outline">Outline</Button>
			<Button variant="ghost">Ghost</Button>
			<Button variant="link">Link</Button>
		</div>
	</Card>

	<!-- Inputs -->
	<Card title="Inputs">
		<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
			<Input 
				label="อีเมล" 
				placeholder="กรอกอีเมล"
				icon="mdi:email"
				required
			/>
			<Input 
				label="รหัสผ่าน" 
				type="password"
				placeholder="กรอกรหัสผ่าน"
				icon="mdi:lock"
				required
			/>
		</div>
	</Card>

	<!-- Select -->
	<Card title="Select">
		<Select 
			options={selectOptions}
			placeholder="เลือกตัวเลือก..."
			searchable
			onChange={(value) => selectedValue = value as string}
		/>
	</Card>

	<!-- Textarea -->
	<Card title="Textarea">
		<Textarea 
			label="คำอธิบาย"
			placeholder="กรอกคำอธิบาย..."
			rows={4}
			maxLength={500}
			showCharacterCount
			autoResize
		/>
	</Card>

	<!-- DateTime -->
	<Card title="Date & Time">
		<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
			<DateTime 
				type="date"
				label="วันที่"
				placeholder="เลือกวันที่"
			/>
			<DateTime 
				type="time"
				label="เวลา"
				placeholder="เลือกเวลา"
			/>
		</div>
	</Card>

	<!-- Radio & Checkbox -->
	<Card title="Radio & Checkbox">
		<div class="space-y-4">
			<div>
				<Label text="เลือกเพศ" required />
				<div class="flex gap-4 mt-2">
					<Radio name="gender" value="male" label="ชาย" />
					<Radio name="gender" value="female" label="หญิง" />
				</div>
			</div>
			<div>
				<Checkbox 
					label="ยอมรับเงื่อนไขการใช้งาน"
					bind:checked={checkboxValue}
				/>
			</div>
		</div>
	</Card>

	<!-- Toggle -->
	<Card title="Toggle">
		<div class="space-y-4">
			<Toggle 
				label="เปิดใช้งาน"
				bind:checked={toggleValue}
			/>
			<Toggle 
				label="การแจ้งเตือน"
				labelPosition="left"
			/>
		</div>
	</Card>

	<!-- Upload -->
	<Card title="File Upload">
		<Upload 
			accept=".jpg,.png,.pdf"
			multiple
			maxSize={5}
			onUpload={handleUpload}
		/>
	</Card>

	<!-- Table -->
	<Card title="Data Table">
		<Table 
			columns={tableColumns}
			data={tableData}
			sortable
			selectable
			pagination
			pageSize={10}
			onSort={handleTableSort}
		/>
	</Card>

	<!-- Badges -->
	<Card title="Badges">
		<div class="flex flex-wrap gap-2">
			<Badge label="Primary" color="primary" />
			<Badge label="Success" variant="success" />
			<Badge label="Warning" variant="warning" />
			<Badge label="Error" color="error" />
			<Badge label="Removable" variant="neutral" removable />
		</div>
	</Card>

	<!-- Alerts -->
	<Card title="Alerts">
		<div class="space-y-4">
			<Alert 
				variant="info"
				title="ข้อมูล"
				message="นี่คือข้อความแจ้งเตือนข้อมูล"
				dismissible
			/>
			<Alert 
				variant="success"
				title="สำเร็จ"
				message="ดำเนินการเสร็จสิ้น"
				dismissible
			/>
			<Alert 
				variant="warning"
				title="คำเตือน"
				message="กรุณาตรวจสอบข้อมูล"
				dismissible
			/>
			<Alert 
				color="error"
				title="ข้อผิดพลาด"
				message="เกิดข้อผิดพลาดในการดำเนินการ"
				dismissible
			/>
		</div>
	</Card>

	<!-- Dropdown -->
	<Card title="Dropdown">
		<Dropdown 
			trigger={() => (
				<Button variant="outline">
					<Icon icon="mdi:dots-vertical" />
					ตัวเลือก
				</Button>
			)}
			items={dropdownItems}
		/>
	</Card>

	<!-- Modal -->
	<Card title="Modal">
		<Button color="primary" onclick={() => showModal = true}>
			เปิด Modal
		</Button>

		<Modal 
			title="ตัวอย่าง Modal"
			show={showModal}
			onClose={() => showModal = false}
		>
			<p>นี่คือเนื้อหาของ Modal</p>
			<div class="modal-action">
				<Button variant="ghost" onclick={() => showModal = false}>
					ยกเลิก
				</Button>
				<Button color="primary" onclick={() => showModal = false}>
					ยืนยัน
				</Button>
			</div>
		</Modal>
	</Card>
</div> 