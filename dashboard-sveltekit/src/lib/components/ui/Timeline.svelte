<script lang="ts">
	import Icon from '@iconify/svelte';

	interface TimelineItem {
		id: string;
		title: string;
		description?: string;
		time: string;
		icon?: string;
		color?:
			| 'primary'
			| 'secondary'
			| 'accent'
			| 'neutral'
			| 'info'
			| 'success'
			| 'warning'
			| 'error';
		completed?: boolean;
	}

	interface Props {
		items: TimelineItem[];
		compact?: boolean;
		className?: string;
	}

	const { items, compact = false, className = '' }: Props = $props();
</script>

<ul class="timeline {compact ? 'timeline-compact' : ''} {className}">
	{#each items as item, index}
		<li>
			{#if index > 0}
				<hr class={item.completed ? 'bg-' + (item.color || 'primary') : 'bg-base-300'} />
			{/if}
			<div class="timeline-start {compact ? 'text-xs' : 'text-sm'} text-base-content/60">
				{item.time}
			</div>
			<div class="timeline-middle">
				<div
					class="w-4 h-4 rounded-full bg-{item.color || 'primary'} flex items-center justify-center"
				>
					{#if item.icon}
						<Icon icon={item.icon} class="w-2.5 h-2.5 text-{item.color || 'primary'}-content" />
					{:else if item.completed}
						<Icon icon="mdi:check" class="w-2.5 h-2.5 text-{item.color || 'primary'}-content" />
					{/if}
				</div>
			</div>
			<div class="timeline-end timeline-box">
				<h3 class="font-semibold {compact ? 'text-sm' : 'text-base'}">{item.title}</h3>
				{#if item.description}
					<p class="text-sm text-base-content/70 mt-1">{item.description}</p>
				{/if}
			</div>
			{#if index < items.length - 1}
				<hr
					class={items[index + 1]?.completed
						? 'bg-' + (items[index + 1]?.color || 'primary')
						: 'bg-base-300'}
				/>
			{/if}
		</li>
	{/each}
</ul>
