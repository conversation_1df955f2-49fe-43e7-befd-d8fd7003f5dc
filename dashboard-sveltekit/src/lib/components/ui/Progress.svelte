<script lang="ts">
	interface Props {
		value: number;
		max?: number;
		color?:
			| 'primary'
			| 'secondary'
			| 'accent'
			| 'neutral'
			| 'info'
			| 'success'
			| 'warning'
			| 'error';
		size?: 'xs' | 'sm' | 'md' | 'lg';
		label?: string;
		showValue?: boolean;
		className?: string;
	}

	const {
		value,
		max = 100,
		color = 'primary',
		size = 'md',
		label,
		showValue = false,
		className = '',
	}: Props = $props();

	const percentage = Math.min(Math.max((value / max) * 100, 0), 100);
	const sizeClass = `progress-${size}`;
</script>

<div class="w-full {className}">
	{#if label || showValue}
		<div class="flex justify-between items-center mb-1">
			{#if label}
				<span class="text-sm font-medium text-base-content">{label}</span>
			{/if}
			{#if showValue}
				<span class="text-sm text-base-content/60">
					{value}{#if max !== 100}/{max}{/if}
				</span>
			{/if}
		</div>
	{/if}
	<progress class="progress progress-{color} {sizeClass} w-full" {value} {max}></progress>
</div>
