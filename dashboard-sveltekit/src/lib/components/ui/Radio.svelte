<script lang="ts">
	import Icon from '@iconify/svelte';

	interface Option {
		value: string | number;
		label: string;
		description?: string;
		icon?: string;
		disabled?: boolean;
	}

	interface Props {
		options: Option[];
		value?: string | number;
		onChange?: (value: string | number) => void;
		name?: string;
		disabled?: boolean;
		className?: string;
		variant?: 'default' | 'card';
		cols?: 1 | 2 | 3 | 4;
		size?: 'sm' | 'md' | 'lg';
		label?: string;
		required?: boolean;
		id?: string;
	}

	let {
		options = [],
		value = '',
		onChange,
		name = 'radio-group',
		disabled = false,
		className = '',
		variant = 'card',
		cols = 2,
		size = 'sm',
		label,
		required = false,
		id = 'radio-group',
	}: Props = $props();

	// Functions
	function handleOptionClick(optionValue: string | number) {
		if (disabled || options.find(opt => opt.value === optionValue)?.disabled) return;

		value = optionValue;
		onChange?.(optionValue);
	}

	function handleKeyDown(event: KeyboardEvent, optionValue: string | number) {
		if (event.key === 'Enter' || event.key === ' ') {
			event.preventDefault();
			handleOptionClick(optionValue);
		}
	}

	// Computed classes
	const gridCols = $derived(`grid-cols-1 md:grid-cols-${cols}`);
	const sizeClasses = $derived(
		{
			sm: 'p-2 text-sm',
			md: 'p-4',
			lg: 'p-6 text-lg',
		}[size]
	);
</script>

<div class={className}>
	{#if label}
		<label for={id} class="label">
			<span class="label-text">{label}</span>
			{#if required}
				<span class="text-error">*</span>
			{/if}
		</label>
	{/if}

	{#if variant === 'card'}
		<div class="grid {gridCols} gap-3">
			{#each options as option}
				<button
					type="button"
					class="relative flex items-center gap-3 cursor-pointer rounded-xl border transition-colors select-none {sizeClasses}
							{value === option.value
						? 'border-primary bg-primary/5 ring-2 ring-primary/20'
						: 'border-base-300 hover:bg-base-200'}
							{option.disabled || disabled ? 'opacity-50 cursor-not-allowed' : ''}"
					onclick={() => handleOptionClick(option.value)}
					onkeydown={e => handleKeyDown(e, option.value)}
					disabled={option.disabled || disabled}
				>
					<input
						type="radio"
						{name}
						value={option.value}
						checked={value === option.value}
						disabled={option.disabled || disabled}
						class="sr-only"
					/>

					{#if option.icon}
						<Icon icon={option.icon} class="w-6 h-6 text-primary" />
					{/if}

					<div class="flex-1">
						<div class="font-semibold text-base-content text-base">
							{option.label}
						</div>
						{#if option.description}
							<div class="text-sm text-base-content/70">
								{option.description}
							</div>
						{/if}
					</div>
				</button>
			{/each}
		</div>
	{:else}
		<div class="space-y-2">
			{#each options as option}
				<label
					for={option.value.toString()}
					class="label cursor-pointer {sizeClasses} hover:bg-base-200 rounded-lg transition-colors
							{option.disabled || disabled ? 'opacity-50 cursor-not-allowed' : ''}"
				>
					<input
						type="radio"
						{name}
						value={option.value}
						checked={value === option.value}
						disabled={option.disabled || disabled}
						class="radio radio-{size}"
					/>
					<div class="flex items-center gap-2">
						{#if option.icon}
							<Icon icon={option.icon} class="w-4 h-4 text-primary" />
						{/if}
						<span class="label-text text-base">{option.label}</span>
						{#if option.description}
							<span class="text-xs text-base-content/70">({option.description})</span>
						{/if}
					</div>
				</label>
			{/each}
		</div>
	{/if}
</div>
