<script lang="ts">
	import Icon from '@iconify/svelte';
	import { createPaginationControls, type PaginationData } from '$lib/utils/pagination';

	const {
		pagination,
		page,
		totalPages,
		total,
		limit,
		onPageChange = () => {},
	} = $props<{
		pagination?: PaginationData;
		page?: number;
		totalPages?: number;
		total?: number;
		limit?: number;
		onPageChange?: (page: number) => void;
	}>();

	// Use either pagination object or individual props
	const paginationData = $derived(
		pagination || {
			page: page || 1,
			totalPages: totalPages || 1,
			total: total || 0,
			limit: limit || 10,
		}
	);

	const controls = $derived(createPaginationControls(paginationData));

	function handlePageChange(page: number) {
		if (page >= 1 && page <= paginationData.totalPages) {
			onPageChange(page);
		}
	}
</script>

{#if paginationData.totalPages > 1}
	<div
		class="flex items-center justify-between px-4 py-3 bg-base-100 border-t border-base-300 sm:px-6"
	>
		<!-- Pagination Info -->
		<div class="flex items-center text-sm text-base-content/70">
			<span>
				แสดง {(paginationData.page - 1) * paginationData.limit + 1} ถึง {Math.min(
					paginationData.page * paginationData.limit,
					paginationData.total
				)} จาก {paginationData.total} รายการ
			</span>
		</div>

		<!-- Pagination Controls -->
		<div class="flex items-center space-x-2">
			<!-- Previous Button -->
			<button
				onclick={() => handlePageChange(paginationData.page - 1)}
				disabled={!controls.hasPrev}
				class="btn btn-sm btn-ghost {!controls.hasPrev ? 'btn-disabled' : ''}"
				title="หน้าก่อนหน้า"
			>
				<Icon icon="mdi:chevron-left" class="w-4 h-4" />
				ก่อนหน้า
			</button>

			<!-- Page Numbers -->
			<div class="flex items-center space-x-1">
				{#if controls.pages[0] > 1}
					<button onclick={() => handlePageChange(1)} class="btn btn-sm btn-ghost" title="หน้าแรก">
						1
					</button>
					{#if controls.pages[0] > 2}
						<span class="px-2 text-base-content/50">...</span>
					{/if}
				{/if}

				{#each controls.pages as pageNum}
					<button
						onclick={() => handlePageChange(pageNum)}
						class="btn btn-sm {pageNum === paginationData.page ? 'btn-primary' : 'btn-ghost'}"
						title="หน้า {pageNum}"
					>
						{pageNum}
					</button>
				{/each}

				{#if controls.pages[controls.pages.length - 1] < paginationData.totalPages}
					{#if controls.pages[controls.pages.length - 1] < paginationData.totalPages - 1}
						<span class="px-2 text-base-content/50">...</span>
					{/if}
					<button
						onclick={() => handlePageChange(paginationData.totalPages)}
						class="btn btn-sm btn-ghost"
						title="หน้าสุดท้าย"
					>
						{paginationData.totalPages}
					</button>
				{/if}
			</div>

			<!-- Next Button -->
			<button
				onclick={() => handlePageChange(paginationData.page + 1)}
				disabled={!controls.hasNext}
				class="btn btn-sm btn-ghost {!controls.hasNext ? 'btn-disabled' : ''}"
				title="หน้าถัดไป"
			>
				ถัดไป
				<Icon icon="mdi:chevron-right" class="w-4 h-4" />
			</button>
		</div>
	</div>
{/if}
