<script lang="ts">
	import Icon from '@iconify/svelte';
	import { getImageUrl } from '$lib/utils/cloudinary';

	const {
		publicId = undefined,
		src = undefined,
		width = 100,
		height = 100,
		format = 'auto',
		cover = false,
		center = false,
		lazy = true,
		alt = '',
		className = '',
		class: classProp = '',
		fallbackIcon = 'solar:global-line-duotone',
		fallbackIconClass = 'w-12 h-12 text-primary',
		cloudName = 'dsoy8tgfc',
		crop = 'fill',
		quality = 80,
	} = $props<{
		publicId?: string;
		src?: string;
		width?: number;
		height?: number;
		format?: string;
		cover?: boolean;
		center?: boolean;
		lazy?: boolean;
		alt?: string;
		className?: string;
		class?: string;
		fallbackIcon?: string;
		fallbackIconClass?: string;
		cloudName?: string;
		crop?: string;
		quality?: number;
	}>();

	let imageError = $state(false);
	let imageLoading = $state(false);
	let imageLoaded = $state(false);

	// Get image URL
	function getImageUrlFromProps(): string {
		if (!publicId && !src) return '';

		try {
			if (publicId) {
				const config = {
					cloudName,
					defaultTransformation: `f_${format},q_${quality},c_${crop}`,
					secure: true,
				};

				const url = getImageUrl(publicId, width, height, {
					crop: cover ? crop : center ? 'center' : 'scale',
					quality,
					format,
					config,
				});
				console.log('Generated Cloudinary URL:', url);
				return url;
			} else if (src) {
				console.log('Original src:', src);
				if (src.includes('cloudinary.com')) {
					const extractedId = src.split('/').pop()?.split('.')[0];
					console.log('Extracted ID:', extractedId);
					if (extractedId) {
						const config = {
							cloudName,
							defaultTransformation: `f_${format},q_${quality}`,
							secure: true,
						};

						const url = getImageUrl(extractedId, width, height, {
							crop: cover ? crop : center ? 'center' : 'scale',
							quality,
							format,
							config,
						});
						console.log('Generated Cloudinary URL from src:', url);
						return url;
					}
				}
				console.log('Returning original src:', src);
				return src;
			}
		} catch (error) {
			console.error('Error generating image URL:', error);
		}

		return src || '';
	}

	function handleImageLoad() {
		imageLoading = false;
		imageLoaded = true;
		imageError = false;
	}

	function handleImageError() {
		imageLoading = false;
		imageLoaded = false;
		imageError = true;
	}

	// Start loading when image starts loading
	function handleImageStart() {
		imageLoading = true;
		imageError = false;
		imageLoaded = false;
	}

	// Get object-fit class based on cover prop
	const objectFitClass = $derived(
		cover ? 'object-cover' : center ? 'object-center' : 'object-contain'
	);
</script>

<div class={classProp}>
	{#if publicId || src}
		{#if imageError}
			<div
				class="flex items-center justify-center {className}"
				style="width: {width}px; height: {height}px;"
			>
				<Icon icon={fallbackIcon} class={fallbackIconClass} />
			</div>
		{:else if imageLoading}
			<div
				class="flex items-center justify-center {className}"
				style="width: {width}px; height: {height}px;"
			>
				<div class="loading loading-spinner loading-md text-primary"></div>
			</div>
		{:else}
			<img
				src={getImageUrlFromProps()}
				{alt}
				class="{className} {objectFitClass}"
				style="width: {width}px; height: {height}px;"
				loading={lazy ? 'lazy' : 'eager'}
				onload={handleImageLoad}
				onerror={handleImageError}
				onloadstart={handleImageStart}
			/>
		{/if}
	{:else}
		<div
			class="flex items-center justify-center {className}"
			style="width: {width}px; height: {height}px;"
		>
			<Icon icon={fallbackIcon} class={fallbackIconClass} />
		</div>
	{/if}
</div>
