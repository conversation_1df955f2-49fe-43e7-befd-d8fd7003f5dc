<script lang="ts">
	import Icon from '@iconify/svelte';

	interface Props {
		accept?: string;
		multiple?: boolean;
		maxSize?: number; // in MB
		disabled?: boolean;
		className?: string;
		onUpload?: (files: File[]) => void;
		onProgress?: (progress: number) => void;
	}

	const {
		accept = '*/*',
		multiple = false,
		maxSize,
		disabled = false,
		className = '',
		onUpload,
		onProgress,
	}: Props = $props();

	const dispatch = createEventDispatcher();

	// State
	let isDragOver = $state(false);
	let isUploading = $state(false);
	let progress = $state(0);
	let uploadedFiles = $state<File[]>([]);

	function handleFileSelect(event: Event) {
		const target = event.target as HTMLInputElement;
		const files = Array.from(target.files || []);
		processFiles(files);
	}

	function handleDragOver(event: DragEvent) {
		event.preventDefault();
		isDragOver = true;
	}

	function handleDragLeave(event: DragEvent) {
		event.preventDefault();
		isDragOver = false;
	}

	function handleDrop(event: DragEvent) {
		event.preventDefault();
		isDragOver = false;

		const files = Array.from(event.dataTransfer?.files || []);
		processFiles(files);
	}

	function processFiles(files: File[]) {
		if (disabled) return;

		// Validate file size
		if (maxSize) {
			const oversizedFiles = files.filter(file => file.size > maxSize * 1024 * 1024);
			if (oversizedFiles.length > 0) {
				dispatch('error', {
					message: `ไฟล์บางไฟล์มีขนาดเกิน ${maxSize}MB`,
				});
				return;
			}
		}

		// Validate file type
		if (accept !== '*/*') {
			const acceptedTypes = accept.split(',').map(type => type.trim());
			const invalidFiles = files.filter(file => {
				return !acceptedTypes.some(type => {
					if (type.startsWith('.')) {
						return file.name.toLowerCase().endsWith(type);
					}
					return file.type.match(new RegExp(type.replace('*', '.*')));
				});
			});

			if (invalidFiles.length > 0) {
				dispatch('error', {
					message: 'ไฟล์บางไฟล์ไม่ใช่ประเภทที่รองรับ',
				});
				return;
			}
		}

		uploadedFiles = multiple ? [...uploadedFiles, ...files] : files;
		onUpload?.(uploadedFiles);
		dispatch('upload', { files: uploadedFiles });

		// Simulate upload progress
		if (files.length > 0) {
			simulateUpload();
		}
	}

	function simulateUpload() {
		isUploading = true;
		progress = 0;

		const interval = setInterval(() => {
			progress += Math.random() * 20;
			onProgress?.(progress);

			if (progress >= 100) {
				progress = 100;
				isUploading = false;
				clearInterval(interval);
				dispatch('complete', { files: uploadedFiles });
			}
		}, 200);
	}

	function removeFile(index: number) {
		uploadedFiles = uploadedFiles.filter((_, i) => i !== index);
		onUpload?.(uploadedFiles);
		dispatch('remove', { files: uploadedFiles });
	}

	function formatFileSize(bytes: number): string {
		if (bytes === 0) return '0 Bytes';
		const k = 1024;
		const sizes = ['Bytes', 'KB', 'MB', 'GB'];
		const i = Math.floor(Math.log(bytes) / Math.log(k));
		return parseFloat((bytes / k ** i).toFixed(2)) + ' ' + sizes[i];
	}
</script>

<div class="form-control w-full">
	<div
		class="border-2 border-dashed border-base-300 rounded-lg p-6 text-center transition-colors {isDragOver
			? 'border-primary bg-primary/10'
			: ''} {disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'} {className}"
		onclick={() => !disabled && document.getElementById('file-input')?.click()}
		onkeydown={e => {
			if ((e.key === 'Enter' || e.key === ' ') && !disabled) {
				e.preventDefault();
				document.getElementById('file-input')?.click();
			}
		}}
		ondragover={handleDragOver}
		ondragleave={handleDragLeave}
		ondrop={handleDrop}
		role="button"
		tabindex="0"
		aria-label="อัปโหลดไฟล์"
	>
		<input
			id="file-input"
			type="file"
			{accept}
			{multiple}
			{disabled}
			class="hidden"
			onchange={handleFileSelect}
		/>

		<Icon icon="mdi:cloud-upload" class="w-12 h-12 mx-auto mb-4 text-base-content/50" />

		<p class="text-lg font-medium mb-2">
			{isDragOver ? 'วางไฟล์ที่นี่' : 'คลิกเพื่อเลือกไฟล์ หรือลากไฟล์มาวาง'}
		</p>

		<p class="text-sm text-base-content/70">
			รองรับไฟล์: {accept === '*/*' ? 'ทุกประเภท' : accept}
			{#if maxSize}
				, ขนาดสูงสุด: {maxSize}MB
			{/if}
		</p>

		{#if isUploading}
			<div class="mt-4">
				<progress class="progress progress-primary w-full" value={progress} max="100"></progress>
				<p class="text-sm mt-2">อัปโหลด {progress.toFixed(0)}%</p>
			</div>
		{/if}
	</div>

	{#if uploadedFiles.length > 0}
		<div class="mt-4 space-y-2">
			{#each uploadedFiles as file, index}
				<div class="flex items-center justify-between p-3 bg-base-200 rounded-lg">
					<div class="flex items-center gap-3">
						<Icon icon="mdi:file" class="w-5 h-5 text-primary" />
						<div>
							<p class="font-medium">{file.name}</p>
							<p class="text-sm text-base-content/70">{formatFileSize(file.size)}</p>
						</div>
					</div>
					<button class="btn btn-ghost btn-sm" onclick={() => removeFile(index)}>
						<Icon icon="mdi:close" class="w-4 h-4" />
					</button>
				</div>
			{/each}
		</div>
	{/if}
</div>
