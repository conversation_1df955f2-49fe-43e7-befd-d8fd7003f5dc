<script lang="ts">
	import Icon from '@iconify/svelte';

	interface Props {
		label?: string;
		color?:
			| 'primary'
			| 'secondary'
			| 'accent'
			| 'neutral'
			| 'info'
			| 'success'
			| 'warning'
			| 'error';
		variant?: 'solid' | 'outline' | 'ghost';
		size?: 'xs' | 'sm' | 'md' | 'lg';
		icon?: string;
		iconPosition?: 'left' | 'right';
		removable?: boolean;
		class?: string;
		onRemove?: () => void;
		children?: any;
	}

	const {
		label,
		color = 'neutral',
		variant = 'solid',
		size = 'md',
		icon,
		iconPosition = 'left',
		removable = false,
		class: classNames = '',
		onRemove,
		children,
	}: Props = $props();

	function handleRemove() {
		onRemove?.();
	}
</script>

<span class="badge badge-{color} badge-{variant} badge-{size} {classNames}">
	{#if icon && iconPosition === 'left'}
		<Icon {icon} class="w-3 h-3" />
	{/if}

	{#if children}
		{@render children()}
	{:else}
		{label}
	{/if}

	{#if icon && iconPosition === 'right'}
		<Icon {icon} class="w-3 h-3" />
	{/if}

	{#if removable}
		<button class="ml-1 hover:bg-base-content/20 rounded-full p-0.5" onclick={handleRemove}>
			<Icon icon="mdi:close" class="w-2.5 h-2.5" />
		</button>
	{/if}
</span>
