<script lang="ts">
	import Icon from '@iconify/svelte';

	interface Props {
		title?: string;
		message?: string;
		color?: 'info' | 'success' | 'warning' | 'error';
		dismissible?: boolean;
		icon?: string;
		className?: string;
		onDismiss?: () => void;
		ondismiss?: () => void;
		children?: any;
	}

	const {
		title,
		message,
		color = 'info',
		dismissible = false,
		icon,
		className = '',
		onDismiss,
		ondismiss,
		children,
	}: Props = $props();

	// Default icons for each variant
	const defaultIcons = {
		info: 'mdi:information',
		success: 'mdi:check-circle',
		warning: 'mdi:alert',
		error: 'mdi:close-circle',
	};

	const alertIcon = icon || defaultIcons[color];

	function handleDismiss() {
		onDismiss?.();
		ondismiss?.();
	}
</script>

<div class="alert alert-{color} {className}">
	{#if alertIcon}
		<Icon icon={alertIcon} class="w-5 h-5" />
	{/if}

	<div class="flex-1">
		{#if title}
			<h3 class="font-bold">{title}</h3>
		{/if}
		<div class="text-sm">
			{#if children}
				{@render children()}
			{:else}
				{message}
			{/if}
		</div>
	</div>

	{#if dismissible}
		<button class="btn btn-sm btn-ghost btn-circle" onclick={handleDismiss}>
			<Icon icon="mdi:close" class="w-4 h-4" />
		</button>
	{/if}
</div>
