<script lang="ts">
	import Icon from '@iconify/svelte';

	interface Option {
		value: string | number;
		label: string;
		disabled?: boolean;
		icon?: string;
		description?: string;
	}

	interface Props {
		options?: Option[];
		value?: string | number | (string | number)[];
		placeholder?: string;
		multiple?: boolean;
		disabled?: boolean;
		required?: boolean;
		error?: string;
		size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
		variant?: 'bordered' | 'ghost' | 'filled' | 'primary' | 'secondary' | 'accent' | 'neutral';
		color?:
			| 'primary'
			| 'secondary'
			| 'accent'
			| 'success'
			| 'warning'
			| 'error'
			| 'info'
			| 'neutral';
		class?: string;
		onChange?: (value: string | number | (string | number)[] | undefined) => void;
		id?: string;
		name?: string;
		icon?: string;
		iconPosition?: 'left' | 'right';
		showIcon?: boolean;
		children?: any;
		label?: string;
		helpText?: string;
		loading?: boolean;
		readonly?: boolean;
		autofocus?: boolean;
		form?: string;
		tabindex?: number;
	}

	let {
		options = [],
		value = $bindable(undefined),
		placeholder = 'เลือกตัวเลือก...',
		multiple = false,
		disabled = false,
		required = false,
		error = '',
		size = 'md',
		variant = 'bordered',
		color = 'primary',
		class: classNames = '',
		onChange,
		id = 'select',
		name = '',
		icon = 'mdi:chevron-down',
		iconPosition = 'right',
		showIcon = true,
		children,
		label = '',
		helpText = '',
		loading = false,
		readonly = false,
		autofocus = false,
		form = '',
		tabindex,
	}: Props = $props();

	// State
	let selectedValue = $state(value);

	// Effects
	$effect(() => {
		selectedValue = value;
	});

	$effect(() => {
		if (selectedValue !== value && selectedValue !== undefined) {
			onChange?.(selectedValue);
		}
	});

	// Computed
	const sizeClasses = {
		xs: 'select-xs text-xs',
		sm: 'select-sm text-sm',
		md: 'select-md text-base',
		lg: 'select-lg text-lg',
		xl: 'select-xl text-xl',
	};

	const variantClasses = {
		bordered: 'select-bordered',
		ghost: 'select-ghost',
		filled: 'select-filled',
		primary: 'select-primary',
		secondary: 'select-secondary',
		accent: 'select-accent',
		neutral: 'select-neutral',
	};

	const colorClasses = {
		primary: 'select-primary',
		secondary: 'select-secondary',
		accent: 'select-accent',
		success: 'select-success',
		warning: 'select-warning',
		error: 'select-error',
		info: 'select-info',
		neutral: 'select-neutral',
	};

	const classes = $derived(
		[
			'select w-full',
			sizeClasses[size],
			variantClasses[variant],
			color !== 'primary' ? colorClasses[color] : '',
			error ? 'select-error' : '',
			loading ? 'select-disabled' : '',
			classNames,
		]
			.filter(Boolean)
			.join(' ')
	);

	// Functions
	function handleChange(event: Event) {
		const target = event.target as HTMLSelectElement;
		if (multiple) {
			const selectedOptions = Array.from(target.selectedOptions).map(option => option.value);
			selectedValue = selectedOptions;
		} else {
			selectedValue = target.value;
		}
		onChange?.(selectedValue);
	}

	function getDisplayValue() {
		if (!selectedValue) return placeholder;

		if (multiple && Array.isArray(selectedValue)) {
			const selectedLabels = selectedValue.map(
				val => options.find(opt => opt.value === val)?.label || val
			);
			return selectedLabels.join(', ');
		} else {
			const option = options.find(opt => opt.value === selectedValue);
			return option?.label || selectedValue;
		}
	}
</script>

<div class="form-control w-full">
	{#if label}
		<label for={id} class="label">
			<span class="label-text">{label}</span>
			{#if required}
				<span class="label-text-alt text-error">*</span>
			{/if}
		</label>
	{/if}

	<div class="relative">
		{#if showIcon && iconPosition === 'left'}
			<div class="absolute left-3 top-1/2 transform -translate-y-1/2 pointer-events-none z-10">
				<Icon {icon} class="w-4 h-4 text-base-content/50" />
			</div>
		{/if}

		<select
			{id}
			{name}
			class={classes}
			class:pl-10={showIcon && iconPosition === 'left'}
			class:pr-10={showIcon && iconPosition === 'right'}
			{disabled}
			{required}  
			{form}
			{tabindex}
			bind:value={selectedValue}
			onchange={handleChange}
		>
			{#if children}
				{@render children()}
			{:else}
				{#if placeholder && !required}
					<option value="" disabled selected={!selectedValue}>
						{placeholder}
					</option>
				{/if}

				{#each options as option}
					<option
						value={option.value}
						disabled={option.disabled}
						selected={multiple
							? Array.isArray(selectedValue) && selectedValue.includes(option.value)
							: selectedValue === option.value}
					>
						{option.label}
						{#if option.description}
							- {option.description}
						{/if}
					</option>
				{/each}
			{/if}
		</select>

		{#if showIcon && iconPosition === 'right'}
			<div class="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
				<Icon {icon} class="w-4 h-4 text-base-content/50" />
			</div>
		{/if}

		{#if loading}
			<div class="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
				<div class="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
			</div>
		{/if}
	</div>

	{#if error}
		<label for={id} class="label">
			<span class="label-text-alt text-error">{error}</span>
		</label>
	{:else if helpText}
		<label for={id} class="label">
			<span class="label-text-alt text-base-content/70">{helpText}</span>
		</label>
	{/if}
</div>

<style>
	select {
		appearance: none;
		background-image: none;
	}

	select:focus {
		outline: none;
		box-shadow: 0 0 0 2px hsl(var(--p) / 0.2);
	}

	select option {
		padding: 0.5rem;
	}

	select option:checked {
		background-color: hsl(var(--p));
		color: hsl(var(--pc));
	}

	select option:hover {
		background-color: hsl(var(--p) / 0.1);
	}

	.select-xs {
		height: 1.5rem;
		min-height: 1.5rem;
		padding-left: 0.5rem;
		padding-right: 0.5rem;
		font-size: 0.75rem;
	}

	.select-sm {
		height: 2rem;
		min-height: 2rem;
		padding-left: 0.75rem;
		padding-right: 0.75rem;
		font-size: 0.875rem;
	}

	.select-md {
		height: 2.5rem;
		min-height: 2.5rem;
		padding-left: 1rem;
		padding-right: 1rem;
		font-size: 1rem;
	}

	.select-lg {
		height: 3rem;
		min-height: 3rem;
		padding-left: 1.25rem;
		padding-right: 1.25rem;
		font-size: 1.125rem;
	}

	.select-xl {
		height: 3.5rem;
		min-height: 3.5rem;
		padding-left: 1.5rem;
		padding-right: 1.5rem;
		font-size: 1.25rem;
	}
</style>
