<script lang="ts">
	import Icon from '@iconify/svelte';

	interface Props {
		checked?: boolean;
		disabled?: boolean;
		size?: 'xs' | 'sm' | 'md' | 'lg';
		label?: string;
		labelPosition?: 'left' | 'right';
		className?: string;
		onChange?: (checked: boolean) => void;
	}

	const {
		checked = false,
		disabled = false,
		size = 'md',
		label,
		labelPosition = 'right',
		className = '',
		onChange,
	}: Props = $props();

	const dispatch = createEventDispatcher();

	function handleToggle() {
		if (!disabled) {
			const newChecked = !checked;
			onChange?.(newChecked);
			dispatch('change', { checked: newChecked });
		}
	}
</script>

<label class="label cursor-pointer {className}">
	{#if label && labelPosition === 'left'}
		<span class="label-text mr-2">{label}</span>
	{/if}

	<input
		type="checkbox"
		{checked}
		{disabled}
		class="toggle toggle-{size}"
		onchange={handleToggle}
	/>

	{#if label && labelPosition === 'right'}
		<span class="label-text ml-2">{label}</span>
	{/if}
</label>
