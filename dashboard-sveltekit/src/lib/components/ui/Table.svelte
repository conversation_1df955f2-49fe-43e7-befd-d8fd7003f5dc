<script lang="ts">
	interface Props {
		loading?: boolean;
		emptyMessage?: string;
		className?: string;
		children?: any;
	}

	const {
		loading = false,
		emptyMessage = 'ไม่พบข้อมูล',
		className = '',
		children,
	}: Props = $props();
</script>

<div class="card bg-base-100 shadow-sm {className}">
	<div class="card-body p-0">
		{#if loading}
			<div class="flex items-center justify-center p-8">
				<div class="loading loading-spinner loading-lg"></div>
				<span class="ml-2">กำลังโหลด...</span>
			</div>
		{:else}
			<div class="overflow-x-auto">
				{#if children}
					{@html children}
				{:else}
					<table class="table table-zebra w-full">
						<thead>
							<tr>
								<!-- Placeholder for table headers -->
							</tr>
						</thead>
						<tbody>
							<!-- Placeholder for table rows -->
						</tbody>
					</table>
				{/if}
			</div>
		{/if}
	</div>
</div>
