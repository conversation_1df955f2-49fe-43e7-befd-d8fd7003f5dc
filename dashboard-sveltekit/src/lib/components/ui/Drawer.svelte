<script lang="ts">
	import Icon from '@iconify/svelte';

	interface Props {
		open: boolean;
		title?: string;
		position?: 'left' | 'right' | 'top' | 'bottom';
		size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
		overlay?: boolean;
		closable?: boolean;
		className?: string;
		onClose?: () => void;
		children?: any;
	}

	let {
		open = $bindable(),
		title,
		position = 'right',
		size = 'md',
		overlay = true,
		closable = true,
		className = '',
		onClose,
		children,
	}: Props = $props();

	function handleClose() {
		open = false;
		onClose?.();
	}

	function handleOverlayClick() {
		if (overlay && closable) {
			handleClose();
		}
	}

	const sizeClasses = {
		sm: position === 'left' || position === 'right' ? 'w-80' : 'h-80',
		md: position === 'left' || position === 'right' ? 'w-96' : 'h-96',
		lg: position === 'left' || position === 'right' ? 'w-[32rem]' : 'h-[32rem]',
		xl: position === 'left' || position === 'right' ? 'w-[40rem]' : 'h-[40rem]',
		full: position === 'left' || position === 'right' ? 'w-full' : 'h-full',
	};

	const positionClasses = {
		left: 'drawer-side left-0',
		right: 'drawer-side right-0',
		top: 'drawer-side top-0',
		bottom: 'drawer-side bottom-0',
	};
</script>

{#if open}
	<div class="drawer drawer-open">
		{#if overlay}
			<div 
				class="drawer-overlay" 
				onclick={handleOverlayClick}
				onkeydown={(e) => e.key === 'Escape' && handleClose()}
				role="button"
				tabindex="0"
			></div>
		{/if}
		<div class="drawer-side">
			<aside class="min-h-full bg-base-200 {sizeClasses[size]} {className}">
				{#if title || closable}
					<div class="flex items-center justify-between p-4 border-b border-base-300">
						{#if title}
							<h2 class="text-lg font-semibold">{title}</h2>
						{/if}
						{#if closable}
							<button class="btn btn-sm btn-ghost btn-circle" onclick={handleClose}>
								<Icon icon="mdi:close" class="w-4 h-4" />
							</button>
						{/if}
					</div>
				{/if}
				<div class="p-4">
					{#if children}
						{@render children()}
					{/if}
				</div>
			</aside>
		</div>
	</div>
{/if}
