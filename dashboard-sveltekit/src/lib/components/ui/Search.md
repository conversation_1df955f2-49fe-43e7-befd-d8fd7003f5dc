# Search Component

Component สำหรับการค้นหาที่มีฟีเจอร์ครบครัน

## การใช้งาน

### Basic Usage

```svelte
<script>
	import { Search } from '$lib/components/ui';

	let searchValue = '';
	let searchResults = [];

	function handleSearch(value) {
		console.log('Searching for:', value);
		// ทำการค้นหา
	}

	function handleChange(value) {
		console.log('Value changed:', value);
	}
</script>

<Search
	placeholder="ค้นหาสินค้า..."
	value={searchValue}
	onChange={handleChange}
	onSearch={handleSearch}
/>
```

### With Debounce

```svelte
<script>
	import { Search } from '$lib/components/ui';

	let searchValue = '';

	function handleSearch(value) {
		// จะถูกเรียกหลังจากผู้ใช้หยุดพิมพ์ 500ms
		console.log('Searching for:', value);
	}
</script>

<Search
	placeholder="ค้นหาลูกค้า..."
	value={searchValue}
	onSearch={handleSearch}
	debounceMs={500}
/>
```

### Disabled State

```svelte
<script>
	import { Search } from '$lib/components/ui';
</script>

<Search
	placeholder="ค้นหา..."
	disabled={true}
/>
```

### Without Clear Button

```svelte
<script>
	import { Search } from '$lib/components/ui';
</script>

<Search
	placeholder="ค้นหา..."
	showClearButton={false}
/>
```

## Props

| Prop              | Type                      | Default     | Description                   |
| ----------------- | ------------------------- | ----------- | ----------------------------- |
| `placeholder`     | `string`                  | `'ค้นหา...'` | ข้อความ placeholder            |
| `value`           | `string`                  | `''`        | ค่าที่แสดงในช่องค้นหา              |
| `disabled`        | `boolean`                 | `false`     | ปิดการใช้งานช่องค้นหา             |
| `className`       | `string`                  | `''`        | CSS class เพิ่มเติม              |
| `onChange`        | `(value: string) => void` | -           | เรียกเมื่อค่ามีการเปลี่ยนแปลง        |
| `onSearch`        | `(value: string) => void` | -           | เรียกเมื่อทำการค้นหา              |
| `debounceMs`      | `number`                  | `300`       | เวลาหน่วงก่อนเรียก onSearch (ms) |
| `showClearButton` | `boolean`                 | `true`      | แสดงปุ่มล้างค่า                   |

## Events

### onChange

เรียกเมื่อผู้ใช้พิมพ์ในช่องค้นหา

```svelte
<Search onChange={(value) => console.log('Value changed:', value)} />
```

### onSearch

เรียกเมื่อ:

- ผู้ใช้กด Enter
- ผู้ใช้คลิกปุ่มค้นหา
- หลังจาก debounce time (หากตั้งค่า)

```svelte
<Search onSearch={(value) => console.log('Searching:', value)} />
```

## Keyboard Shortcuts

- **Enter**: เรียก onSearch
- **Escape**: ล้างค่า (หาก showClearButton = true)

## Features

- ✅ **Debounce**: ป้องกันการเรียก API บ่อยเกินไป
- ✅ **Clear Button**: ปุ่มล้างค่า
- ✅ **Keyboard Support**: รองรับ Enter และ Escape
- ✅ **Accessibility**: รองรับ screen readers
- ✅ **Responsive**: ทำงานได้ดีในทุกขนาดหน้าจอ
- ✅ **Customizable**: ปรับแต่งได้ตามต้องการ

## Styling

Component ใช้ Tailwind CSS classes และสามารถปรับแต่งได้ผ่าน `className` prop:

```svelte
<Search className="w-96 bg-gray-100" />
```

## Examples

### Search with Results

```svelte
<script>
	import { Search } from '$lib/components/ui';

	let searchValue = '';
	let results = [];

	function handleSearch(value) {
		// จำลองการค้นหา
		results = [
			{ id: 1, name: 'สินค้า A' },
			{ id: 2, name: 'สินค้า B' },
		].filter(item => 
			item.name.toLowerCase().includes(value.toLowerCase())
		);
	}
</script>

<Search
	placeholder="ค้นหาสินค้า..."
	value={searchValue}
	onSearch={handleSearch}
/>

{#if results.length > 0}
	<div class="mt-4">
		{#each results as result}
			<div class="p-2 border rounded">
				{result.name}
			</div>
		{/each}
	</div>
{/if}
```

### Search with Loading State

```svelte
<script>
	import { Search } from '$lib/components/ui';

	let searchValue = '';
	let loading = false;

	async function handleSearch(value) {
		loading = true;
		// จำลองการเรียก API
		await new Promise(resolve => setTimeout(resolve, 1000));
		loading = false;
	}
</script>

<Search
	placeholder="ค้นหา..."
	value={searchValue}
	onSearch={handleSearch}
	disabled={loading}
/>

{#if loading}
	<div class="mt-2 text-sm text-muted-foreground">
		กำลังค้นหา...
	</div>
{/if}
```
