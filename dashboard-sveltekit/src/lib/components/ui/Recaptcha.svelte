<!-- ประกาศ global types สำหรับ TypeScript -->
<script module lang="ts">
	declare global {
		interface Window {
			grecaptcha: {
				render: (container: HTMLElement, options: any) => number;
				reset: (widgetId: number) => void;
				getResponse: (widgetId: number) => string;
				execute: (siteKey: string, options: { action: string }) => Promise<string>;
			};
		}
	}
</script>

<script lang="ts">
	import { onMount } from 'svelte';
	import { languageStore } from '$lib/stores/language.svelte';
	import { themeStore } from '$lib/stores/theme.svelte';
	import { getSiteKey, debugRecaptchaConfig } from '$lib/config/recaptcha';

	interface Props {
		siteKey?: string;
		version?: 'v2' | 'v3';
		theme?: 'light' | 'dark';
		size?: 'normal' | 'compact' | 'invisible';
		action?: string;
		badge?: 'bottomright' | 'bottomleft' | 'inline';
		language?: string;
		tabindex?: number;
		callback?: (token: string) => void;
		expiredCallback?: () => void;
		errorCallback?: () => void;
		disabled?: boolean;
	}

	const {
		siteKey = getSiteKey(), // ใช้ function จาก config
		version = 'v2', // v2 เป็นค่าเริ่มต้น
		theme: propTheme,
		size = version === 'v3' ? 'invisible' : 'normal', // auto-adjust size based on version
		action = 'submit',
		badge = 'bottomright',
		language: propLanguage,
		tabindex = 0,
		callback,
		expiredCallback,
		errorCallback,
		disabled = false,
	}: Props = $props();

	// Debug log สำหรับตรวจสอบ configuration
	debugRecaptchaConfig();
	console.log('[reCAPTCHA Component] Final Configuration:', {
		siteKey: siteKey ? siteKey.substring(0, 10) + '...' : 'NOT_SET',
		version,
		theme: propTheme || 'auto',
		size,
		action
	});

	let container: HTMLDivElement;
	let widgetId: number | null = null;
	let isLoaded = false;
	let currentLanguage = $state(propLanguage || languageStore.language);
	let currentTheme = $state(propTheme || themeStore.getEffectiveThemeValue());

	// ฟังก์ชันสำหรับแปลงภาษาให้ตรงกับ reCAPTCHA
	function getRecaptchaLanguage(lang: string): string {
		const languageMap: Record<string, string> = {
			th: 'th',
			en: 'en',
			lo: 'lo',
		};
		return languageMap[lang] || 'en';
	}

	// ตรวจสอบว่า Google reCAPTCHA script ถูกโหลดแล้วหรือยัง
	function loadRecaptchaScript(): Promise<void> {
		return new Promise((resolve, reject) => {
			// ตรวจสอบว่ามี script อยู่แล้วหรือไม่
			const existingScript = document.querySelector('script[src*="recaptcha/api.js"]');
			if (existingScript) {
				console.log('[reCAPTCHA] Script already exists, waiting for grecaptcha...');
				const checkGrecaptcha = () => {
					if ((window as any).grecaptcha && (window as any).grecaptcha.render) {
						console.log('[reCAPTCHA] grecaptcha ready (existing script)');
						resolve();
					} else {
						setTimeout(checkGrecaptcha, 100);
					}
				};
				checkGrecaptcha();
				return;
			}

			if ((window as any).grecaptcha && (window as any).grecaptcha.render) {
				console.log('[reCAPTCHA] grecaptcha already available');
				resolve();
				return;
			}

			console.log('[reCAPTCHA] Loading script for version:', version);
			const script = document.createElement('script');
			// ใช้ URL ที่แตกต่างกันตาม version
			if (version === 'v2') {
				// v2 ไม่ต้องใส่ siteKey ใน URL
				script.src = `https://www.google.com/recaptcha/api.js?hl=${getRecaptchaLanguage(currentLanguage)}`;
			} else {
				// v3 ต้องใส่ siteKey ใน URL
				script.src = `https://www.google.com/recaptcha/api.js?render=${siteKey}&hl=${getRecaptchaLanguage(currentLanguage)}`;
			}
			script.async = true;
			script.defer = true;
			script.onload = () => {
				console.log('[reCAPTCHA] Script loaded, waiting for grecaptcha...');
				// รอให้ grecaptcha พร้อมใช้งาน
				const checkGrecaptcha = () => {
					if ((window as any).grecaptcha && (window as any).grecaptcha.render) {
						console.log('[reCAPTCHA] grecaptcha ready');
						resolve();
					} else {
						setTimeout(checkGrecaptcha, 100);
					}
				};
				checkGrecaptcha();
			};
			script.onerror = (error) => {
				console.error('[reCAPTCHA] Failed to load script:', error);
				reject(error);
			};
			document.head.appendChild(script);
		});
	}

	// รีโหลด reCAPTCHA script เมื่อภาษาหรือ theme มีการเปลี่ยนแปลง
	async function reloadRecaptchaScript() {
		// ลบ script เดิม
		const existingScript = document.querySelector('script[src*="recaptcha/api.js"]');
		if (existingScript) {
			existingScript.remove();
		}

		// ลบ grecaptcha object เดิม
		if ((window as any).grecaptcha) {
			delete (window as any).grecaptcha;
		}

		// โหลด script ใหม่
		await loadRecaptchaScript();
		isLoaded = true;

		// รอสักครู่แล้วสร้าง reCAPTCHA ใหม่
		setTimeout(() => {
			if (version === 'v2') {
				createV2Recaptcha();
			}
		}, 100);
	}

	// สร้าง reCAPTCHA v2
	function createV2Recaptcha() {
		if (!(window as any).grecaptcha || !(window as any).grecaptcha.render || !container) return;

		// ตรวจสอบ siteKey
		if (!siteKey || siteKey.trim() === '') {
			console.error('[reCAPTCHA v2] siteKey is missing or empty:', siteKey);
			if (errorCallback) errorCallback();
			return;
		}

		console.log('[reCAPTCHA v2] Creating widget with siteKey:', siteKey.substring(0, 15) + '...');

		try {
			widgetId = (window as any).grecaptcha.render(container, {
				sitekey: siteKey,
				theme: currentTheme,
				size: size,
				tabindex: tabindex,
				callback: (token: string) => {
					console.log('[reCAPTCHA v2] Token received:', token.substring(0, 50) + '...');
					if (callback) callback(token);
				},
				'expired-callback': () => {
					console.log('[reCAPTCHA v2] Token expired');
					if (expiredCallback) expiredCallback();
				},
				'error-callback': () => {
					if (errorCallback) errorCallback();
				},
			});
			console.log('[reCAPTCHA v2] Widget created successfully with ID:', widgetId);
		} catch (error) {
			console.error('[reCAPTCHA v2] Error creating widget:', error);
			console.error('[reCAPTCHA v2] siteKey used:', siteKey);
			console.error('[reCAPTCHA v2] container:', container);
			if (errorCallback) errorCallback();
		}
	}

	// สร้าง reCAPTCHA v3
	async function createV3Recaptcha(): Promise<string> {
		if (!(window as any).grecaptcha) {
			await loadRecaptchaScript();
		}

		// ตรวจสอบ siteKey
		if (!siteKey || siteKey.trim() === '') {
			console.error('reCAPTCHA siteKey is missing or empty');
			if (errorCallback) errorCallback();
			throw new Error('reCAPTCHA siteKey is missing or empty');
		}

		try {
			const token = await (window as any).grecaptcha.execute(siteKey, {
				action,
			});
			if (callback) callback(token);
			return token;
		} catch (error) {
			console.error('Error creating reCAPTCHA v3:', error);
			if (errorCallback) errorCallback();
			throw error;
		}
	}

	// รีเซ็ต reCAPTCHA
	export function reset(): void {
		if (version === 'v2' && widgetId !== null && (window as any).grecaptcha) {
			(window as any).grecaptcha.reset(widgetId);
		}
	}

	// รับ token สำหรับ v3
	export async function execute(): Promise<string> {
		if (version === 'v3') {
			return await createV3Recaptcha();
		}
		throw new Error('execute() is only available for reCAPTCHA v3');
	}

	// ตรวจสอบว่า reCAPTCHA ถูกตรวจสอบแล้วหรือยัง (สำหรับ v2)
	export function getResponse(): string {
		if (version === 'v2' && widgetId !== null && (window as any).grecaptcha) {
			return (window as any).grecaptcha.getResponse(widgetId);
		}
		return '';
	}

	// ติดตามการเปลี่ยนแปลงของภาษา
	$effect(() => {
		const newLanguage = propLanguage || languageStore.language;
		if (newLanguage !== currentLanguage && isLoaded) {
			currentLanguage = newLanguage;
			reloadRecaptchaScript();
		}
	});

	onMount(async () => {
		if (disabled) return;

		// ตรวจสอบ siteKey ก่อน
		if (!siteKey || siteKey.trim() === '') {
			console.error('reCAPTCHA siteKey is missing or empty. Please set VITE_RECAPTCHA_SITE_KEY in your .env file');
			console.log('Current siteKey:', siteKey);
			console.log('Environment variable:', import.meta.env.VITE_RECAPTCHA_SITE_KEY);
			if (errorCallback) errorCallback();
			return;
		}

		try {
			await loadRecaptchaScript();
			isLoaded = true;

			if (version === 'v2') {
				createV2Recaptcha();
				// ซ่อน reCAPTCHA badge สำหรับ v2
				hideRecaptchaBadge();
			} else if (version === 'v3' && size === 'invisible') {
				// สำหรับ v3 ที่เป็น invisible จะไม่แสดง UI
				// ต้องเรียก execute() เอง
			}
		} catch (error) {
			console.error('Error loading reCAPTCHA:', error);
			if (errorCallback) errorCallback();
		}
	});

	// ฟังก์ชันซ่อน reCAPTCHA badge สำหรับ v2
	function hideRecaptchaBadge() {
		if (version === 'v2') {
			// รอให้ reCAPTCHA โหลดเสร็จแล้วซ่อน badge
			setTimeout(() => {
				const badge = document.querySelector('.grecaptcha-badge');
				if (badge) {
					(badge as HTMLElement).style.display = 'none';
				}
			}, 1000);
		}
	}
</script>

<div
	bind:this={container}
	class="g-recaptcha"
	class:invisible={version === 'v3' && size === 'invisible'}
	class:disabled
>
	{#if !siteKey || siteKey.trim() === ''}
		<!-- แสดงข้อความแจ้งเตือนเมื่อไม่มี siteKey -->
		<div class="p-4 bg-yellow-50 border border-yellow-200 rounded-md">
			<div class="flex">
				<div class="flex-shrink-0">
					<svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
						<path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
					</svg>
				</div>
				<div class="ml-3">
					<h3 class="text-sm font-medium text-yellow-800">
						reCAPTCHA ไม่พร้อมใช้งาน
					</h3>
					<div class="mt-2 text-sm text-yellow-700">
						<p>กรุณาตั้งค่า VITE_RECAPTCHA_SITE_KEY ในไฟล์ .env</p>
						<p class="mt-1">สำหรับ development สามารถใช้ test key: 6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI</p>
					</div>
				</div>
			</div>
		</div>
	{:else if version === 'v2'}
		<!-- reCAPTCHA v2 จะถูก render ใน container นี้ -->
	{:else if version === 'v3' && size !== 'invisible'}
		<!-- สำหรับ v3 ที่ไม่ใช่ invisible จะแสดง badge -->
		<div class="hidden" data-badge={badge}>
			<div data-theme={currentTheme}></div>
		</div>
	{/if}
</div>

<style>
	.invisible {
		display: none;
	}

	.disabled {
		opacity: 0.5;
		pointer-events: none;
	}

	/* ซ่อน reCAPTCHA badge สำหรับ v2 */
	:global(.grecaptcha-badge) {
		visibility: hidden !important;
		opacity: 0 !important;
		pointer-events: none !important;
	}

	/* รองรับการซ่อน badge แบบเฉพาะเจาะจง */
	:global(.recaptcha-v2 .grecaptcha-badge) {
		display: none !important;
	}

	@media only screen and (max-width: 500px) {
		.g-recaptcha {
			transform: scale(0.77);
			transform-origin: 0 0;
		}
	}
</style>
