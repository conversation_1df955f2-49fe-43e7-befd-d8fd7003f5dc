<script lang="ts">
	import { createEventDispatcher } from 'svelte';

	interface $$Props {
		checked?: boolean;
		disabled?: boolean;
		class?: string;
	}

	const { checked = false, disabled = false, class: className = '' } = $props();

	const dispatch = createEventDispatcher();

	function handleToggle() {
		if (!disabled) {
			dispatch('change', !checked);
		}
	}
</script>

<button
	type="button"
	role="switch"
	aria-checked={checked}
	aria-disabled={disabled}
	aria-label="Toggle switch"
	{disabled}
	class="relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 {checked
		? 'bg-primary'
		: 'bg-gray-200'} {className}"
	onclick={handleToggle}
>
	<span
		class="inline-block h-4 w-4 transform rounded-full bg-white transition-transform {checked
			? 'translate-x-6'
			: 'translate-x-1'}"
	></span>
</button>
