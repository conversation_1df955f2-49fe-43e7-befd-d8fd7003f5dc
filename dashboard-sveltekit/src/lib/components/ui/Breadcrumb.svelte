<script lang="ts">
	import Icon from '@iconify/svelte';

	interface BreadcrumbItem {
		label: string;
		href?: string;
		icon?: string;
	}

	interface Props {
		items: BreadcrumbItem[];
		separator?: string;
		className?: string;
	}

	const { items, separator = '/', className = '' }: Props = $props();
</script>

<div class="breadcrumbs text-sm {className}">
	<ul>
		{#each items as item, index}
			<li>
				{#if item.href && index < items.length - 1}
					<a href={item.href} class="flex items-center gap-1 hover:text-primary">
						{#if item.icon}
							<Icon icon={item.icon} class="w-4 h-4" />
						{/if}
						{item.label}
					</a>
				{:else}
					<span
						class="flex items-center gap-1 {index === items.length - 1
							? 'text-base-content font-medium'
							: ''}"
					>
						{#if item.icon}
							<Icon icon={item.icon} class="w-4 h-4" />
						{/if}
						{item.label}
					</span>
				{/if}
			</li>
		{/each}
	</ul>
</div>
