<script lang="ts">
	interface Props {
		id?: string;
		name?: string;
		value?: string;
		placeholder?: string;
		label?: string;
		error?: string;
		disabled?: boolean;
		required?: boolean;
		readonly?: boolean;
		rows?: number;
		maxLength?: number;
		minLength?: number;
		autoResize?: boolean;
		showCharacterCount?: boolean;
		class?: string;
		onInput?: (event: Event) => void;
		onChange?: (event: Event) => void;
		onFocus?: (event: Event) => void;
		onBlur?: (event: Event) => void;
	}

	let {
		id = '',
		name = '',
		value = '',
		placeholder = '',
		label = '',
		error = '',
		disabled = false,
		required = false,
		readonly = false,
		rows = 3,
		maxLength,
		minLength,
		autoResize = false,
		showCharacterCount = false,
		class: classNames = '',
		onInput,
		onChange,
		onFocus,
		onBlur,
	}: Props = $props();
 

	// Internal state
	let textareaElement: HTMLTextAreaElement;
	let characterCount = $state(0);

	// Computed values
	const classes = $derived(
		[
			'textarea textarea-bordered w-full resize-none',
			error ? 'textarea-error' : '',
			disabled ? 'opacity-50 cursor-not-allowed' : '',
			classNames,
		]
			.filter(Boolean)
			.join(' ')
	);

	const isOverLimit = $derived(maxLength ? characterCount > maxLength : false);
	const remainingChars = $derived(maxLength ? maxLength - characterCount : 0);



	// Functions
	function handleInput(event: Event) {
		const target = event.target as HTMLTextAreaElement;
		characterCount = target.value.length;

		if (autoResize) {
			resizeTextarea();
		}

		onInput?.(event);
	}

	function handleChange(event: Event) {
		onChange?.(event);
	}

	function handleFocus(event: Event) {
		onFocus?.(event);
	}

	function handleBlur(event: Event) {
		onBlur?.(event);
	}

	function resizeTextarea() {
		if (textareaElement) {
			textareaElement.style.height = 'auto';
			textareaElement.style.height = textareaElement.scrollHeight + 'px';
		}
	}

	// Effects
	$effect(() => {
		if (autoResize && textareaElement) {
			resizeTextarea();
		}
	});

	$effect(() => {
		characterCount = value.length;
	});
</script>

<div class="form-control w-full">
	{#if label}
		<label class="label" for={id}>
			<span class="label-text">{label}</span>
			{#if required}
				<span class="text-error">*</span>
			{/if}
		</label>
	{/if}

	<div class="relative">
		<textarea
			bind:this={textareaElement}
			bind:value={value}
			{name}
			{placeholder}
			{disabled}
			{required}
			{readonly}
			{rows}
			maxlength={maxLength}
			minlength={minLength}
			id={id}
			class={classes}
			oninput={handleInput}
			onchange={handleChange}
			onfocus={handleFocus}
			onblur={handleBlur}
		></textarea>

		{#if showCharacterCount && maxLength}
			<div class="absolute bottom-2 right-2 text-xs text-base-content/50">
				<span class={isOverLimit ? 'text-error' : ''}>
					{characterCount}/{maxLength}
				</span>
			</div>
		{/if}
	</div>

	{#if error}
		<div class="label">
			<span class="label-text-alt text-error">{error}</span>
		</div>
	{:else if showCharacterCount && maxLength && remainingChars <= 10}
		<div class="label">
			<span class="label-text-alt {isOverLimit ? 'text-error' : 'text-warning'}">
				เหลือ {remainingChars} ตัวอักษร
			</span>
		</div>
	{/if}
</div>
