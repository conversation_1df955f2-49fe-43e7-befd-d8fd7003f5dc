<script lang="ts">
	import Icon from '@iconify/svelte';

	interface Props {
		src?: string;
		alt?: string;
		size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
		shape?: 'circle' | 'square';
		placeholder?: string;
		online?: boolean;
		offline?: boolean;
		className?: string;
		children?: any;
	}

	const {
		src,
		alt = '',
		size = 'md',
		shape = 'circle',
		placeholder,
		online = false,
		offline = false,
		className = '',
		children,
	}: Props = $props();

	const sizeClasses = {
		xs: 'w-6 h-6',
		sm: 'w-8 h-8',
		md: 'w-12 h-12',
		lg: 'w-16 h-16',
		xl: 'w-24 h-24',
	};

	const shapeClass = shape === 'circle' ? 'rounded-full' : 'rounded-lg';
	const statusClass = online ? 'online' : offline ? 'offline' : '';
</script>

<div class="avatar {statusClass} {className}">
	<div class="{sizeClasses[size]} {shapeClass}">
		{#if src}
			<img {src} {alt} class="object-cover" />
		{:else if placeholder}
			<div class="bg-neutral text-neutral-content flex items-center justify-center">
				<span class="text-sm font-medium">{placeholder}</span>
			</div>
		{:else if children}
			{@render children()}
		{:else}
			<div class="bg-neutral text-neutral-content flex items-center justify-center">
				<Icon icon="mdi:account" class="w-1/2 h-1/2" />
			</div>
		{/if}
	</div>
</div>
