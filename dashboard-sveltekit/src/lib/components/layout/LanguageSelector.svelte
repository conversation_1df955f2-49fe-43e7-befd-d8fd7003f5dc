<script lang="ts">
	import Icon from '@iconify/svelte';
	import { locale } from 'svelte-i18n';
	import { setLocale, supportedLocales } from '$lib/i18n';
	import { languageStore } from '$lib/stores/language.svelte';

	let isOpen = $state(false);

	function handleLocaleChange(newLocale: string) {
		setLocale(newLocale);
		languageStore.setLanguage(newLocale as any);
		isOpen = false;
	}

	function toggleDropdown() {
		isOpen = !isOpen;
	}

	const currentLocale = $derived(
		supportedLocales.find(l => l.code === $locale) || supportedLocales[0]
	);
</script>

<div class="dropdown dropdown-end">
	<button
		id="language-button"
		type="button"
		class="btn btn-circle btn-soft m-1"
		aria-haspopup="menu"
		aria-controls="language-dropdown"
		onclick={toggleDropdown}
		onkeydown={e => {
			if (e.key === 'Enter' || e.key === ' ') {
				toggleDropdown();
			}
		}}
	>
		<Icon icon={currentLocale.flag} class="size-7" />
	</button>

	{#if isOpen}
		<ul class="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-fit">
			{#each supportedLocales as lang}
				<li>
					<button
						class="flex items-center gap-2 {$locale === lang.code ? 'active' : ''}"
						onclick={() => handleLocaleChange(lang.code)}
					>
						<Icon icon={lang.flag} class="w-6 h-6" />
						<span>{lang.name}</span>
						{#if $locale === lang.code}
							<Icon icon="mdi:check" class="w-4 h-4 ml-auto" />
						{/if}
					</button>
				</li>
			{/each}
		</ul>
	{/if}
</div>
