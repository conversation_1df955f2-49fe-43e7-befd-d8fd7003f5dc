<script lang="ts">
	import Icon from '@iconify/svelte';
	import UserMenu from './UserMenu.svelte';

	// ✅ รับ data prop จาก parent component
	const { data = { user: null, needsAuth: false } } = $props<{
		data?: { user?: any; needsAuth?: boolean };
	}>();
</script>

<header class="navbar">
	<!-- Mobile menu button -->
	<div class="navbar-start">
		<label for="drawer-toggle" class="btn btn-square btn-ghost lg:hidden">
			<Icon icon="mdi:menu" class="w-6 h-6" />
		</label>
		<!-- <h1 class="text-xl font-semibold hidden lg:flex">Dashboard</h1> -->
	</div>

	<!-- Title -->
	<!-- <div class="navbar-center lg:navbar-start">
        <h1 class="text-xl font-semibold">Dashboard</h1>
    </div> -->

	<!-- Actions -->
	<div class="navbar-end">
		<UserMenu />
	</div>
</header>
