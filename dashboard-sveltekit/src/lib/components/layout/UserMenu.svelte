<script lang="ts">
	import Icon from '@iconify/svelte';
	import { t } from 'svelte-i18n';
	import LanguageSelector from '$lib/components/layout/LanguageSelector.svelte';
	import ThemeToggle from '$lib/components/layout/ThemeToggle.svelte';
	import Image from '$lib/components/ui/Image.svelte';
	import { authStore } from '$lib/stores/auth.svelte';

	// ใช้เฉพาะ authStore.user เพื่อให้ sync กับสถานะการ auth ที่ถูกต้อง
	const user = $derived(authStore.user);

	// Debug log เพื่อดูการเปลี่ยนแปลง
	$effect(() => {
		console.log('UserMenu: User data changed', {
			authStoreUser: authStore.user,
			currentUser: user,
			moneyPoint: user?.moneyPoint,
		});
	});
	async function signout() {
		console.log('UserMenu: Signout button clicked');

		try {
			// ✅ Use authStore.signout() which now handles everything properly
			await authStore.signout();
		} catch (error) {
			console.error('UserMenu: Signout failed:', error);

			// ✅ Force redirect as last resort
			window.location.href = '/signin?signout=error';
		}
	}
</script>

<div class="flex items-center gap-2">
	<LanguageSelector />
	<ThemeToggle />
	<div class="dropdown dropdown-end">
		<button tabindex="0" class="btn btn-primary rounded-full p-2 leading-none">
			<span class="text-primary-content">฿{user?.moneyPoint?.toLocaleString() || '0'}</span>
			<span class="text-primary-content text-xl font-medium">
				<!-- {user?.email?.charAt(0) || "U"} -->
				<Image
					publicId={user?.avatar}
					width={26}
					height={26}
					cover={true}
					className="w-26 h-26 rounded-full object-cover"
					alt="โลโก้"
					fallbackIcon="solar:global-line-duotone"
					fallbackIconClass="w-12 h-12 text-primary"
				/>
			</span>
		</button>
		<ul class="dropdown-content menu bg-base-100 rounded-box z-1 w-52 p-2 mt-2 shadow-sm gap-1">
			<li>
				<a href="/dashboard/profile">
					<Icon icon="solar:user-line-duotone" class="size-6" />
					{user?.email || 'User'}
				</a>
			</li>
			<li>
				<a class="text-success bg-success/10 hover:bg-success/20" href="/dashboard/topup">
					<Icon icon="solar:wallet-money-line-duotone" class="size-6 text-success" />{$t(
						'auth.topup'
					)}
				</a>
			</li>
			<li>
				<a class="text-warning bg-warning/10 hover:bg-warning/20" href="/dashboard/test-auth">
					<Icon icon="solar:user-line-duotone" class="size-6 text-warning" />ทดสอบ ระบบสมาชิก
				</a>
			</li>
			<li>
				<button class="text-error bg-error/10 hover:bg-error/20" onclick={signout}>
					<Icon icon="solar:logout-3-line-duotone" class="size-6 text-error" />{$t('auth.signout')}
				</button>
			</li>
		</ul>
	</div>
</div>
