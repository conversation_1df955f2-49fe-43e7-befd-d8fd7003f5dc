<script lang="ts">
	import Icon from '@iconify/svelte';
	import { formatDistanceToNow } from 'date-fns';
	import { th } from 'date-fns/locale';
	import type { Notification } from '$lib/types';

	interface Props {
		notification: Notification;
		onClick?: () => void;
		onDelete?: () => void;
	}

	const { notification, onClick, onDelete }: Props = $props();

	// ไอคอนสำหรับแต่ละประเภทการแจ้งเตือน
	const typeIcons: Record<string, string> = {
		order: 'solar:bag-smile-bold',
		product: 'solar:box-bold',
		promotion: 'solar:gift-bold',
		system: 'solar:settings-bold',
		chat: 'solar:chat-round-dots-bold',
		affiliate: 'solar:users-group-rounded-bold',
		topup: 'solar:wallet-money-bold',
		membership: 'solar:user-plus-bold',
		expiry: 'solar:calendar-mark-bold',
		inventory: 'solar:box-minimalistic-bold',
		payment: 'solar:card-bold',
		security: 'solar:shield-check-bold',
		marketing: 'solar:megaphone-loud-bold',
	};

	// สีสำหรับแต่ละระดับความสำคัญ
	const priorityColors: Record<string, string> = {
		low: 'text-info',
		medium: 'text-warning',
		high: 'text-error',
		urgent: 'text-error animate-pulse',
	};

	// ฟอร์แมตเวลา
	function formatTime(date: Date | string) {
		const dateObj = typeof date === 'string' ? new Date(date) : date;
		return formatDistanceToNow(dateObj, {
			addSuffix: true,
			locale: th,
		});
	}

	// จัดการคลิก
	function handleClick() {
		onClick?.();
	}

	// จัดการลบ
	function handleDelete(event: Event) {
		event.stopPropagation();
		onDelete?.();
	}

	// ตรวจสอบว่ามี URL ใน data หรือไม่
	const hasUrl = $derived(notification.data?.url);
</script>

<button
	class="notification-item p-3 rounded-lg border transition-all duration-200 hover:shadow-md cursor-pointer w-full text-left"
	class:bg-base-200={notification.status === 'unread'}
	class:bg-base-100={notification.status === 'read'}
	class:border-primary={notification.status === 'unread'}
	class:border-base-300={notification.status === 'read'}
	onclick={handleClick}
	type="button"
>
	<div class="flex items-start gap-3">
		<!-- Icon -->
		<div class="flex-shrink-0">
			<div class="w-10 h-10 rounded-full bg-base-300 flex items-center justify-center">
				<Icon
					icon={typeIcons[notification.type] || 'solar:bell-bold'}
					class="size-5 {priorityColors[notification.priority] || 'text-base-content'}"
				/>
			</div>
		</div>

		<!-- Content -->
		<div class="flex-1 min-w-0">
			<div class="flex items-start justify-between gap-2">
				<div class="flex-1 min-w-0">
					<!-- Title -->
					<h4 class="font-medium text-sm line-clamp-1 mb-1">
						{notification.title}
					</h4>

					<!-- Message -->
					<p class="text-sm text-base-content/70 line-clamp-2 mb-2">
						{notification.message}
					</p>

					<!-- Meta info -->
					<div class="flex items-center gap-2 text-xs text-base-content/50">
						<span>{formatTime(notification.createdAt)}</span>

						{#if notification.priority === 'urgent'}
							<span class="badge badge-error badge-xs">ด่วน</span>
						{:else if notification.priority === 'high'}
							<span class="badge badge-warning badge-xs">สำคัญ</span>
						{/if}

						{#if notification.status === 'unread'}
							<span class="w-2 h-2 bg-primary rounded-full"></span>
						{/if}
					</div>
				</div>

				<!-- Actions -->
				<div class="flex-shrink-0 flex items-center gap-1">
					{#if hasUrl}
						<Icon icon="solar:external-link-bold" class="size-3 text-base-content/40" />
					{/if}

					<div
						onclick={handleDelete}
						class="btn btn-ghost btn-xs opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer"
						title="ลบ"
						role="button"
						tabindex="0"
						onkeydown={e => e.key === 'Enter' && handleDelete(e)}
					>
						<Icon icon="solar:trash-bin-minimalistic-bold" class="size-3" />
					</div>
				</div>
			</div>
		</div>
	</div>
</button>

<style>
	.notification-item {
		position: relative;
	}

	.notification-item:hover .btn {
		opacity: 1;
	}

	.line-clamp-1 {
		display: -webkit-box;
		-line-clamp: 1;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}

	.line-clamp-2 {
		display: -webkit-box;
		-line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}
</style>
