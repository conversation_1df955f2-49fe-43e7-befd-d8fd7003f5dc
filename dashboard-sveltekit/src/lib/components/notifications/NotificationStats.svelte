<script lang="ts">
	import Icon from '@iconify/svelte';
	import type { NotificationStats } from '$lib/types';

	interface Props {
		stats: NotificationStats[];
	}

	const { stats }: Props = $props();

	// ไอคอนและสีสำหรับแต่ละประเภท
	const typeConfig: Record<string, { icon: string; color: string; label: string }> = {
		order: {
			icon: 'solar:bag-smile-bold',
			color: 'text-success',
			label: 'คำสั่งซื้อ',
		},
		product: {
			icon: 'solar:box-bold',
			color: 'text-info',
			label: 'สินค้า',
		},
		promotion: {
			icon: 'solar:gift-bold',
			color: 'text-secondary',
			label: 'โปรโมชั่น',
		},
		system: {
			icon: 'solar:settings-bold',
			color: 'text-warning',
			label: 'ระบบ',
		},
		chat: {
			icon: 'solar:chat-round-dots-bold',
			color: 'text-primary',
			label: 'แชท',
		},
		affiliate: {
			icon: 'solar:users-group-rounded-bold',
			color: 'text-accent',
			label: 'พันธมิตร',
		},
		topup: {
			icon: 'solar:wallet-money-bold',
			color: 'text-success',
			label: 'เติมเงิน',
		},
		membership: {
			icon: 'solar:user-plus-bold',
			color: 'text-info',
			label: 'สมาชิก',
		},
		expiry: {
			icon: 'solar:calendar-mark-bold',
			color: 'text-error',
			label: 'วันหมดอายุ',
		},
		inventory: {
			icon: 'solar:box-minimalistic-bold',
			color: 'text-warning',
			label: 'คลังสินค้า',
		},
		payment: {
			icon: 'solar:card-bold',
			color: 'text-success',
			label: 'การชำระเงิน',
		},
		security: {
			icon: 'solar:shield-check-bold',
			color: 'text-error',
			label: 'ความปลอดภัย',
		},
		marketing: {
			icon: 'solar:megaphone-loud-bold',
			color: 'text-secondary',
			label: 'การตลาด',
		},
	};

	// คำนวณสถิติรวม
	const totalNotifications = $derived(stats.reduce((sum, stat) => sum + stat.count, 0));
	const totalUnread = $derived(stats.reduce((sum, stat) => sum + stat.unread, 0));
	const readPercentage = $derived(
		totalNotifications > 0
			? Math.round(((totalNotifications - totalUnread) / totalNotifications) * 100)
			: 0
	);

	// เรียงลำดับสถิติตามจำนวน unread (มากไปน้อย)
	const sortedStats = $derived([...stats].sort((a, b) => b.unread - a.unread));
</script>

<div class="grid grid-cols-1 lg:grid-cols-4 gap-4">
	<!-- Overview Cards -->
	<div class="lg:col-span-4 grid grid-cols-1 sm:grid-cols-3 gap-4 mb-2">
		<!-- Total Notifications -->
		<div class="stat bg-base-100 rounded-lg shadow-sm">
			<div class="stat-figure text-primary">
				<Icon icon="solar:bell-bold" class="size-8" />
			</div>
			<div class="stat-title">การแจ้งเตือนทั้งหมด</div>
			<div class="stat-value text-primary">
				{totalNotifications.toLocaleString()}
			</div>
			<div class="stat-desc">รายการทั้งหมดในระบบ</div>
		</div>

		<!-- Unread Notifications -->
		<div class="stat bg-base-100 rounded-lg shadow-sm">
			<div class="stat-figure text-error">
				<Icon icon="solar:eye-closed-bold" class="size-8" />
			</div>
			<div class="stat-title">ยังไม่อ่าน</div>
			<div class="stat-value text-error">
				{totalUnread.toLocaleString()}
			</div>
			<div class="stat-desc">
				{totalNotifications > 0
					? `${Math.round((totalUnread / totalNotifications) * 100)}% ของทั้งหมด`
					: 'ไม่มีข้อมูล'}
			</div>
		</div>

		<!-- Read Percentage -->
		<div class="stat bg-base-100 rounded-lg shadow-sm">
			<div class="stat-figure text-success">
				<Icon icon="solar:eye-bold" class="size-8" />
			</div>
			<div class="stat-title">อ่านแล้ว</div>
			<div class="stat-value text-success">{readPercentage}%</div>
			<div class="stat-desc">
				{(totalNotifications - totalUnread).toLocaleString()} รายการ
			</div>
		</div>
	</div>

	<!-- Detailed Stats by Type -->
	{#if sortedStats.length > 0}
		<div class="lg:col-span-4">
			<h3 class="text-lg font-semibold mb-4 flex items-center gap-2">
				<Icon icon="solar:chart-square-bold" class="size-5" />
				สถิติตามประเภท
			</h3>

			<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3">
				{#each sortedStats as stat}
					{@const config = typeConfig[stat._id] || {
						icon: 'solar:bell-bold',
						color: 'text-base-content',
						label: stat._id,
					}}

					<div class="card bg-base-100 shadow-sm hover:shadow-md transition-shadow">
						<div class="card-body p-4">
							<div class="flex items-center justify-between mb-2">
								<div class="flex items-center gap-2">
									<Icon icon={config.icon} class="size-5 {config.color}" />
									<span class="text-sm font-medium">{config.label}</span>
								</div>

								{#if stat.unread > 0}
									<div class="badge badge-error badge-sm">
										{stat.unread}
									</div>
								{/if}
							</div>

							<div class="flex items-center justify-between">
								<div>
									<div class="text-2xl font-bold">
										{stat.count}
									</div>
									<div class="text-xs text-base-content/60">รายการทั้งหมด</div>
								</div>

								<div class="text-right">
									<div class="text-sm font-medium text-error">
										{stat.unread} ยังไม่อ่าน
									</div>
									<div class="text-xs text-base-content/60">
										{stat.count > 0 ? `${Math.round((stat.unread / stat.count) * 100)}%` : '0%'}
									</div>
								</div>
							</div>

							<!-- Progress Bar -->
							<div class="mt-3">
								<div class="flex justify-between text-xs text-base-content/60 mb-1">
									<span>ความคืบหน้าการอ่าน</span>
									<span>
										{stat.count > 0
											? Math.round(((stat.count - stat.unread) / stat.count) * 100)
											: 0}%
									</span>
								</div>
								<progress
									class="progress progress-success w-full h-2"
									value={stat.count - stat.unread}
									max={stat.count}
								></progress>
							</div>
						</div>
					</div>
				{/each}
			</div>
		</div>
	{/if}
</div>
