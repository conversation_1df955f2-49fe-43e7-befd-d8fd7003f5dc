<script lang="ts">
	import Icon from '@iconify/svelte';

	interface Props {
		selectedType: string;
		selectedStatus: string;
		onChange: () => void;
	}

	let { selectedType = $bindable(), selectedStatus = $bindable(), onChange }: Props = $props();

	// ตัวเลือกประเภทการแจ้งเตือน
	const typeOptions = [
		{ value: '', label: 'ทุกประเภท', icon: 'solar:list-bold' },
		{ value: 'order', label: 'คำสั่งซื้อ', icon: 'solar:bag-smile-bold' },
		{ value: 'product', label: 'สินค้า', icon: 'solar:box-bold' },
		{ value: 'promotion', label: 'โปรโมชั่น', icon: 'solar:gift-bold' },
		{ value: 'system', label: 'ระบบ', icon: 'solar:settings-bold' },
		{ value: 'chat', label: 'แชท', icon: 'solar:chat-round-dots-bold' },
		{
			value: 'affiliate',
			label: 'พันธมิตร',
			icon: 'solar:users-group-rounded-bold',
		},
		{ value: 'topup', label: 'เติมเงิน', icon: 'solar:wallet-money-bold' },
		{ value: 'membership', label: 'สมาชิก', icon: 'solar:user-plus-bold' },
		{
			value: 'expiry',
			label: 'วันหมดอายุ',
			icon: 'solar:calendar-mark-bold',
		},
		{
			value: 'inventory',
			label: 'คลังสินค้า',
			icon: 'solar:box-minimalistic-bold',
		},
		{ value: 'payment', label: 'การชำระเงิน', icon: 'solar:card-bold' },
		{
			value: 'security',
			label: 'ความปลอดภัย',
			icon: 'solar:shield-check-bold',
		},
		{
			value: 'marketing',
			label: 'การตลาด',
			icon: 'solar:megaphone-loud-bold',
		},
	];

	// ตัวเลือกสถานะ
	const statusOptions = [
		{ value: '', label: 'ทุกสถานะ', icon: 'solar:list-bold' },
		{ value: 'unread', label: 'ยังไม่อ่าน', icon: 'solar:eye-closed-bold' },
		{ value: 'read', label: 'อ่านแล้ว', icon: 'solar:eye-bold' },
		{ value: 'archived', label: 'เก็บถาวร', icon: 'solar:archive-bold' },
	];

	// จัดการการเปลี่ยนแปลงตัวกรอง
	function handleTypeChange(event: Event) {
		const target = event.target as HTMLSelectElement;
		selectedType = target.value;
		onChange();
	}

	function handleStatusChange(event: Event) {
		const target = event.target as HTMLSelectElement;
		selectedStatus = target.value;
		onChange();
	}

	// ล้างตัวกรองทั้งหมด
	function clearFilters() {
		selectedType = '';
		selectedStatus = '';
		onChange();
	}

	// ตรวจสอบว่ามีตัวกรองที่เลือกหรือไม่
	const hasActiveFilters = $derived(selectedType || selectedStatus);
</script>

<div class="card bg-base-100 shadow-sm">
	<div class="card-body p-4">
		<div class="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
			<!-- Filters -->
			<div class="flex flex-col sm:flex-row gap-3 flex-1">
				<!-- Type Filter -->
				<div class="form-control">
					<label for="handleTypeChange" class="label">
						<span class="label-text text-sm font-medium">ประเภท</span>
					</label>
					<select
						class="select select-bordered select-sm w-full sm:w-48"
						value={selectedType}
						onchange={handleTypeChange}
					>
						{#each typeOptions as option}
							<option value={option.value}>
								{option.label}
							</option>
						{/each}
					</select>
				</div>

				<!-- Status Filter -->
				<div class="form-control">
					<label for="selectedStatus" class="label">
						<span class="label-text text-sm font-medium">สถานะ</span>
					</label>
					<select
						class="select select-bordered select-sm w-full sm:w-40"
						value={selectedStatus}
						onchange={handleStatusChange}
					>
						{#each statusOptions as option}
							<option value={option.value}>
								{option.label}
							</option>
						{/each}
					</select>
				</div>
			</div>

			<!-- Actions -->
			<div class="flex items-center gap-2">
				<!-- Active Filters Indicator -->
				{#if hasActiveFilters}
					<div class="flex items-center gap-2">
						<span class="text-xs text-base-content/70">ตัวกรองที่เลือก:</span>
						<div class="flex gap-1">
							{#if selectedType}
								<span class="badge badge-primary badge-sm">
									{typeOptions.find(t => t.value === selectedType)?.label}
								</span>
							{/if}
							{#if selectedStatus}
								<span class="badge badge-secondary badge-sm">
									{statusOptions.find(s => s.value === selectedStatus)?.label}
								</span>
							{/if}
						</div>
					</div>
				{/if}

				<!-- Clear Filters Button -->
				{#if hasActiveFilters}
					<button onclick={clearFilters} class="btn btn-ghost btn-sm" title="ล้างตัวกรอง">
						<Icon icon="solar:close-circle-bold" class="size-4" />
						ล้าง
					</button>
				{/if}
			</div>
		</div>

		<!-- Quick Filter Buttons -->
		<div class="divider my-2"></div>
		<div class="flex flex-wrap gap-2">
			<span class="text-xs text-base-content/70 self-center">ตัวกรองด่วน:</span>

			<button
				onclick={() => {
					selectedStatus = 'unread';
					onChange();
				}}
				class="btn btn-ghost btn-xs"
				class:btn-primary={selectedStatus === 'unread'}
			>
				<Icon icon="solar:eye-closed-bold" class="size-3" />
				ยังไม่อ่าน
			</button>

			<button
				onclick={() => {
					selectedType = 'order';
					onChange();
				}}
				class="btn btn-ghost btn-xs"
				class:btn-primary={selectedType === 'order'}
			>
				<Icon icon="solar:bag-smile-bold" class="size-3" />
				คำสั่งซื้อ
			</button>

			<button
				onclick={() => {
					selectedType = 'system';
					onChange();
				}}
				class="btn btn-ghost btn-xs"
				class:btn-primary={selectedType === 'system'}
			>
				<Icon icon="solar:settings-bold" class="size-3" />
				ระบบ
			</button>

			<button
				onclick={() => {
					selectedType = 'topup';
					onChange();
				}}
				class="btn btn-ghost btn-xs"
				class:btn-primary={selectedType === 'topup'}
			>
				<Icon icon="solar:wallet-money-bold" class="size-3" />
				เติมเงิน
			</button>
		</div>
	</div>
</div>
