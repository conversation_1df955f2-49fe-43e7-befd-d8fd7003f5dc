<script lang="ts">
	import Icon from '@iconify/svelte';

	interface Props {
		quietHours: {
			enabled: boolean;
			startTime: string;
			endTime: string;
			timezone: string;
		};
		onSave: (quietHours: any) => void;
		isSaving: boolean;
	}

	const { quietHours, onSave, isSaving }: Props = $props();

	// สร้าง local state สำหรับการแก้ไข
	let localQuietHours = $state({ ...quietHours });

	// อัปเดต local state เมื่อ props เปลี่ยน
	$effect(() => {
		localQuietHours = { ...quietHours };
	});

	// ตัวเลือก timezone
	const timezoneOptions = [
		{ value: 'Asia/Bangkok', label: 'เวลาไทย (GMT+7)' },
		{ value: 'Asia/Jakarta', label: 'เวลาอินโดนีเซีย (GMT+7)' },
		{ value: 'Asia/Singapore', label: 'เวลาสิงคโปร์ (GMT+8)' },
		{ value: 'Asia/Tokyo', label: 'เวลาญี่ปุ่น (GMT+9)' },
		{ value: 'UTC', label: 'UTC (GMT+0)' },
	];

	// จัดการการเปลี่ยนแปลง
	function handleToggleEnabled() {
		localQuietHours.enabled = !localQuietHours.enabled;
	}

	function handleStartTimeChange(event: Event) {
		const target = event.target as HTMLInputElement;
		localQuietHours.startTime = target.value;
	}

	function handleEndTimeChange(event: Event) {
		const target = event.target as HTMLInputElement;
		localQuietHours.endTime = target.value;
	}

	function handleTimezoneChange(event: Event) {
		const target = event.target as HTMLSelectElement;
		localQuietHours.timezone = target.value;
	}

	// บันทึกการตั้งค่า
	function handleSave() {
		onSave(localQuietHours);
	}

	// ตรวจสอบว่ามีการเปลี่ยนแปลงหรือไม่
	const hasChanges = $derived(() => {
		return JSON.stringify(quietHours) !== JSON.stringify(localQuietHours);
	});

	// คำนวณระยะเวลา quiet hours
	const quietDuration = $derived(() => {
		if (!localQuietHours.enabled) return null;

		const start = new Date(`2000-01-01T${localQuietHours.startTime}:00`);
		const end = new Date(`2000-01-01T${localQuietHours.endTime}:00`);

		let duration: number;
		if (end > start) {
			duration = end.getTime() - start.getTime();
		} else {
			// ข้ามวัน
			duration = 24 * 60 * 60 * 1000 - (start.getTime() - end.getTime());
		}

		const hours = Math.floor(duration / (60 * 60 * 1000));
		const minutes = Math.floor((duration % (60 * 60 * 1000)) / (60 * 1000));

		return { hours, minutes };
	});

	// ตรวจสอบว่าเวลาปัจจุบันอยู่ในช่วง quiet hours หรือไม่
	const isCurrentlyQuiet = $derived(() => {
		if (!localQuietHours.enabled) return false;

		const now = new Date();
		const currentTime = now.toTimeString().slice(0, 5); // HH:MM format

		const start = localQuietHours.startTime;
		const end = localQuietHours.endTime;

		if (start <= end) {
			return currentTime >= start && currentTime <= end;
		} else {
			// ข้ามวัน
			return currentTime >= start || currentTime <= end;
		}
	});

	// ตัวอย่างเวลา
	const timeExamples = [
		{ start: '22:00', end: '08:00', label: 'ช่วงกลางคืน (10 ชั่วโมง)' },
		{ start: '23:00', end: '07:00', label: 'ช่วงนอน (8 ชั่วโมง)' },
		{ start: '12:00', end: '13:00', label: 'ช่วงพักเที่ยง (1 ชั่วโมง)' },
		{ start: '18:00', end: '19:00', label: 'ช่วงทานข้าวเย็น (1 ชั่วโมง)' },
	];

	function applyTimeExample(start: string, end: string) {
		localQuietHours.startTime = start;
		localQuietHours.endTime = end;
		localQuietHours.enabled = true;
	}
</script>

<div class="card bg-base-100 shadow-xl">
	<div class="card-body">
		<div class="flex items-center justify-between mb-4">
			<h2 class="card-title flex items-center gap-2">
				<Icon icon="solar:moon-sleep-bold" class="size-6" />
				ช่วงเวลาเงียบ
			</h2>

			{#if hasChanges()}
				<button
					onclick={handleSave}
					class="btn btn-primary btn-sm"
					class:loading={isSaving}
					disabled={isSaving}
				>
					{#if !isSaving}
						<Icon icon="solar:diskette-bold" class="size-4" />
					{/if}
					บันทึก
				</button>
			{/if}
		</div>

		<p class="text-base-content/70 mb-6">กำหนดช่วงเวลาที่ไม่ต้องการรับการแจ้งเตือน</p>

		<!-- Enable/Disable Toggle -->
		<div class="form-control mb-6">
			<label class="label cursor-pointer justify-start gap-4">
				<input
					type="checkbox"
					class="toggle toggle-primary"
					checked={localQuietHours.enabled}
					onchange={handleToggleEnabled}
				/>
				<div>
					<div class="font-medium">เปิดใช้งานช่วงเวลาเงียบ</div>
					<div class="text-sm text-base-content/70">ระบบจะไม่ส่งการแจ้งเตือนในช่วงเวลาที่กำหนด</div>
				</div>
			</label>
		</div>

		{#if localQuietHours.enabled}
			<div class="space-y-6">
				<!-- Time Settings -->
				<div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
					<!-- Start Time -->
					<div class="form-control">
						<label for="startTime" class="label">
							<span class="label-text font-medium">เวลาเริ่มต้น</span>
						</label>
						<input
							type="time"
							class="input input-bordered"
							value={localQuietHours.startTime}
							onchange={handleStartTimeChange}
						/>
					</div>

					<!-- End Time -->
					<div class="form-control">
						<label for="endTime" class="label">
							<span class="label-text font-medium">เวลาสิ้นสุด</span>
						</label>
						<input
							type="time"
							class="input input-bordered"
							value={localQuietHours.endTime}
							onchange={handleEndTimeChange}
						/>
					</div>
				</div>

				<!-- Timezone -->
				<div class="form-control">
					<label for="timezone" class="label">
						<span class="label-text font-medium">เขตเวลา</span>
					</label>
					<select
						class="select select-bordered"
						value={localQuietHours.timezone}
						onchange={handleTimezoneChange}
					>
						{#each timezoneOptions as option}
							<option value={option.value}>{option.label}</option>
						{/each}
					</select>
				</div>

				<!-- Duration Info -->
				{#if quietDuration()}
					{@const duration = quietDuration()}
					<div class="alert alert-info">
						<Icon icon="solar:clock-circle-bold" class="size-6" />
						<div>
							<h3 class="font-bold">ระยะเวลาเงียบ</h3>
							<div class="text-xs">
								{duration?.hours} ชั่วโมง {duration?.minutes}
								นาที
								{#if localQuietHours.startTime > localQuietHours.endTime}
									(ข้ามวัน)
								{/if}
							</div>
						</div>
					</div>
				{/if}

				<!-- Current Status -->
				{#if isCurrentlyQuiet()}
					<div class="alert alert-warning">
						<Icon icon="solar:moon-sleep-bold" class="size-6" />
						<div>
							<h3 class="font-bold">ขณะนี้อยู่ในช่วงเวลาเงียบ</h3>
							<div class="text-xs">
								คุณจะไม่ได้รับการแจ้งเตือนจนถึงเวลา {localQuietHours.endTime}
							</div>
						</div>
					</div>
				{/if}

				<!-- Time Examples -->
				<div>
					<h3 class="font-medium mb-3">ตัวอย่างการตั้งค่า</h3>
					<div class="grid grid-cols-1 sm:grid-cols-2 gap-2">
						{#each timeExamples as example}
							<button
								onclick={() => applyTimeExample(example.start, example.end)}
								class="btn btn-ghost btn-sm justify-start"
							>
								<Icon icon="solar:clock-circle-bold" class="size-4" />
								{example.start} - {example.end}
								<span class="text-xs opacity-70">({example.label})</span>
							</button>
						{/each}
					</div>
				</div>
			</div>
		{:else}
			<!-- Disabled State -->
			<div class="text-center py-8 text-base-content/50">
				<Icon icon="solar:bell-off-bold" class="size-12 mx-auto mb-3" />
				<p>ช่วงเวลาเงียบถูกปิดใช้งาน</p>
				<p class="text-sm">คุณจะได้รับการแจ้งเตือนตลอดเวลา</p>
			</div>
		{/if}

		<!-- Summary -->
		<div class="divider"></div>
		<div class="flex items-center justify-between text-sm text-base-content/70">
			<span>
				{#if localQuietHours.enabled}
					การแจ้งเตือนจะถูกระงับในช่วงเวลาที่กำหนด
				{:else}
					การแจ้งเตือนจะทำงานตลอดเวลา
				{/if}
			</span>

			{#if hasChanges()}
				<span class="text-warning">มีการเปลี่ยนแปลงที่ยังไม่ได้บันทึก</span>
			{/if}
		</div>
	</div>
</div>
