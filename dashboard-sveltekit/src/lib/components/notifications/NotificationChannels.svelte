<script lang="ts">
	import Icon from '@iconify/svelte';

	interface Props {
		channels: {
			inApp: boolean;
			email: boolean;
			push: boolean;
			sms: boolean;
		};
		onSave: (channels: any) => void;
		isSaving: boolean;
	}

	const { channels, onSave, isSaving }: Props = $props();

	// สร้าง local state สำหรับการแก้ไข
	let localChannels = $state({ ...channels });

	// อัปเดต local state เมื่อ props เปลี่ยน
	$effect(() => {
		localChannels = { ...channels };
	});

	// ตัวเลือกช่องทางการแจ้งเตือน
	const channelOptions = [
		{
			key: 'inApp',
			label: 'การแจ้งเตือนในแอป',
			description: 'แสดงการแจ้งเตือนภายในแอปพลิเคชัน',
			icon: 'solar:bell-bold',
			color: 'text-primary',
			available: true,
			note: 'แนะนำให้เปิดใช้งานเสมอ',
		},
		{
			key: 'email',
			label: 'อีเมล',
			description: 'ส่งการแจ้งเตือนไปยังอีเมลของคุณ',
			icon: 'solar:letter-bold',
			color: 'text-info',
			available: true,
			note: 'ต้องยืนยันอีเมลก่อนใช้งาน',
		},
		{
			key: 'push',
			label: 'Push Notification',
			description: 'ส่งการแจ้งเตือนผ่านเบราว์เซอร์',
			icon: 'solar:bell-bing-bold',
			color: 'text-warning',
			available: false,
			note: 'กำลังพัฒนา - เร็วๆ นี้',
		},
		{
			key: 'sms',
			label: 'SMS',
			description: 'ส่งข้อความแจ้งเตือนไปยังโทรศัพท์',
			icon: 'solar:phone-bold',
			color: 'text-success',
			available: false,
			note: 'กำลังพัฒนา - เร็วๆ นี้',
		},
	];

	// จัดการการเปลี่ยนแปลง
	function handleToggle(key: string) {
		if (channelOptions.find(option => option.key === key)?.available) {
			localChannels[key as keyof typeof localChannels] =
				!localChannels[key as keyof typeof localChannels];
		}
	}

	// บันทึกการตั้งค่า
	function handleSave() {
		onSave(localChannels);
	}

	// ตรวจสอบว่ามีการเปลี่ยนแปลงหรือไม่
	const hasChanges = $derived(() => {
		return JSON.stringify(channels) !== JSON.stringify(localChannels);
	});

	// นับจำนวนช่องทางที่เปิดใช้งาน
	const enabledChannels = $derived(() => {
		return Object.values(localChannels).filter(Boolean).length;
	});
</script>

<div class="card bg-base-100 shadow-xl">
	<div class="card-body">
		<div class="flex items-center justify-between mb-4">
			<h2 class="card-title flex items-center gap-2">
				<Icon icon="solar:routing-2-bold" class="size-6" />
				ช่องทางการแจ้งเตือน
			</h2>

			{#if hasChanges()}
				<button
					onclick={handleSave}
					class="btn btn-primary btn-sm"
					class:loading={isSaving}
					disabled={isSaving}
				>
					{#if !isSaving}
						<Icon icon="solar:diskette-bold" class="size-4" />
					{/if}
					บันทึก
				</button>
			{/if}
		</div>

		<p class="text-base-content/70 mb-6">เลือกช่องทางที่คุณต้องการรับการแจ้งเตือน</p>

		<div class="space-y-4">
			{#each channelOptions as option}
				<div class="form-control">
					<label
						class="label cursor-pointer justify-start gap-4 p-4 rounded-lg border transition-colors"
						class:hover:bg-base-50={option.available}
						class:opacity-60={!option.available}
						class:cursor-not-allowed={!option.available}
					>
						<input
							type="checkbox"
							class="checkbox checkbox-primary"
							checked={localChannels[option.key as keyof typeof localChannels]}
							onchange={() => handleToggle(option.key)}
							disabled={!option.available}
						/>

						<div class="flex items-center gap-3 flex-1">
							<Icon icon={option.icon} class="size-6 {option.color}" />

							<div class="flex-1">
								<div class="flex items-center gap-2">
									<span class="font-medium">{option.label}</span>
									{#if !option.available}
										<span class="badge badge-ghost badge-sm">ไม่พร้อมใช้งาน</span>
									{/if}
								</div>
								<div class="text-sm text-base-content/70">
									{option.description}
								</div>
								{#if option.note}
									<div class="text-xs text-base-content/50 mt-1">
										{option.note}
									</div>
								{/if}
							</div>
						</div>
					</label>
				</div>
			{/each}
		</div>

		<!-- Channel Status -->
		<div class="divider"></div>
		<div class="grid grid-cols-2 gap-4">
			<div class="stat">
				<div class="stat-title">ช่องทางที่เปิดใช้งาน</div>
				<div class="stat-value text-primary">{enabledChannels()}</div>
				<div class="stat-desc">
					จาก {channelOptions.filter(o => o.available).length} ช่องทางที่พร้อมใช้
				</div>
			</div>

			<div class="stat">
				<div class="stat-title">สถานะ</div>
				<div class="stat-value text-sm">
					{#if enabledChannels() === 0}
						<span class="text-error">ไม่มีช่องทาง</span>
					{:else if enabledChannels() === 1}
						<span class="text-warning">ช่องทางเดียว</span>
					{:else}
						<span class="text-success">หลายช่องทาง</span>
					{/if}
				</div>
				<div class="stat-desc">
					{#if enabledChannels() === 0}
						คุณจะไม่ได้รับการแจ้งเตือน
					{:else if enabledChannels() === 1}
						แนะนำให้เปิดช่องทางเพิ่มเติม
					{:else}
						การตั้งค่าที่ดี
					{/if}
				</div>
			</div>
		</div>

		<!-- Warning for no channels -->
		{#if enabledChannels() === 0}
			<div class="alert alert-warning">
				<Icon icon="solar:danger-triangle-bold" class="size-6" />
				<div>
					<h3 class="font-bold">คำเตือน</h3>
					<div class="text-xs">
						คุณไม่ได้เลือกช่องทางการแจ้งเตือนใดๆ คุณจะไม่ได้รับการแจ้งเตือนจากระบบ
					</div>
				</div>
			</div>
		{/if}

		<!-- Summary -->
		<div class="flex items-center justify-between text-sm text-base-content/70">
			<span> การแจ้งเตือนจะส่งผ่านช่องทางที่เลือกไว้เท่านั้น </span>

			{#if hasChanges()}
				<span class="text-warning">มีการเปลี่ยนแปลงที่ยังไม่ได้บันทึก</span>
			{/if}
		</div>
	</div>
</div>

<style>
	.hover\:bg-base-50:hover {
		background-color: hsl(var(--b1) / 0.5);
	}
</style>
