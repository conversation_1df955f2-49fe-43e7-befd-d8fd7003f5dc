<script lang="ts">
	import Icon from '@iconify/svelte';
	import { format, formatDistanceToNow } from 'date-fns';
	import { th } from 'date-fns/locale';
	import type { Notification } from '$lib/types';

	interface Props {
		notification: Notification;
		onClick?: () => void;
		onDelete?: () => void;
		onToggleRead?: () => void;
	}

	const { notification, onClick, onDelete, onToggleRead }: Props = $props();

	// ไอคอนสำหรับแต่ละประเภทการแจ้งเตือน
	const typeIcons: Record<string, { icon: string; color: string }> = {
		order: { icon: 'solar:bag-smile-bold', color: 'text-success' },
		product: { icon: 'solar:box-bold', color: 'text-info' },
		promotion: { icon: 'solar:gift-bold', color: 'text-secondary' },
		system: { icon: 'solar:settings-bold', color: 'text-warning' },
		chat: { icon: 'solar:chat-round-dots-bold', color: 'text-primary' },
		affiliate: {
			icon: 'solar:users-group-rounded-bold',
			color: 'text-accent',
		},
		topup: { icon: 'solar:wallet-money-bold', color: 'text-success' },
		membership: { icon: 'solar:user-plus-bold', color: 'text-info' },
		expiry: { icon: 'solar:calendar-mark-bold', color: 'text-error' },
		inventory: {
			icon: 'solar:box-minimalistic-bold',
			color: 'text-warning',
		},
		payment: { icon: 'solar:card-bold', color: 'text-success' },
		security: { icon: 'solar:shield-check-bold', color: 'text-error' },
		marketing: {
			icon: 'solar:megaphone-loud-bold',
			color: 'text-secondary',
		},
	};

	// ป้ายกำกับประเภท
	const typeLabels: Record<string, string> = {
		order: 'คำสั่งซื้อ',
		product: 'สินค้า',
		promotion: 'โปรโมชั่น',
		system: 'ระบบ',
		chat: 'แชท',
		affiliate: 'พันธมิตร',
		topup: 'เติมเงิน',
		membership: 'สมาชิก',
		expiry: 'วันหมดอายุ',
		inventory: 'คลังสินค้า',
		payment: 'การชำระเงิน',
		security: 'ความปลอดภัย',
		marketing: 'การตลาด',
	};

	// สีสำหรับแต่ละระดับความสำคัญ
	const priorityConfig: Record<string, { badge: string; text: string }> = {
		low: { badge: 'badge-ghost', text: 'ต่ำ' },
		medium: { badge: 'badge-info', text: 'ปานกลาง' },
		high: { badge: 'badge-warning', text: 'สูง' },
		urgent: { badge: 'badge-error', text: 'ด่วน' },
	};

	// ฟอร์แมตเวลา
	function formatTime(date: Date | string) {
		const dateObj = typeof date === 'string' ? new Date(date) : date;
		return formatDistanceToNow(dateObj, {
			addSuffix: true,
			locale: th,
		});
	}

	function formatFullTime(date: Date | string) {
		const dateObj = typeof date === 'string' ? new Date(date) : date;
		return format(dateObj, 'dd/MM/yyyy HH:mm', { locale: th });
	}

	// จัดการคลิก
	function handleClick() {
		onClick?.();
	}

	// จัดการลบ
	function handleDelete(event: Event) {
		event.stopPropagation();
		onDelete?.();
	}

	// จัดการ toggle read
	function handleToggleRead(event: Event) {
		event.stopPropagation();
		onToggleRead?.();
	}

	// ตรวจสอบว่ามี URL หรือไม่
	const hasUrl = $derived(notification.data?.url);
	const typeConfig = $derived(
		typeIcons[notification.type] || {
			icon: 'solar:bell-bold',
			color: 'text-base-content',
		}
	);
	const priorityInfo = $derived(priorityConfig[notification.priority] || priorityConfig.medium);
</script>

<button
	class="notification-item p-6 transition-all duration-200 hover:bg-base-50 cursor-pointer group w-full text-left"
	class:bg-base-200={notification.status === 'unread'}
	class:bg-base-100={notification.status === 'read'}
	onclick={handleClick}
	type="button"
>
	<div class="flex items-start gap-4">
		<!-- Icon & Status Indicator -->
		<div class="flex-shrink-0 relative">
			<div class="w-12 h-12 rounded-full bg-base-300 flex items-center justify-center">
				<Icon icon={typeConfig.icon} class="size-6 {typeConfig.color}" />
			</div>

			<!-- Unread Indicator -->
			{#if notification.status === 'unread'}
				<div
					class="absolute -top-1 -right-1 w-4 h-4 bg-primary rounded-full border-2 border-base-100"
				></div>
			{/if}
		</div>

		<!-- Content -->
		<div class="flex-1 min-w-0">
			<!-- Header -->
			<div class="flex items-start justify-between gap-4 mb-2">
				<div class="flex-1 min-w-0">
					<div class="flex items-center gap-2 mb-1">
						<span class="badge badge-outline badge-sm">
							{typeLabels[notification.type] || notification.type}
						</span>

						{#if notification.priority !== 'medium'}
							<span class="badge {priorityInfo.badge} badge-sm">
								{priorityInfo.text}
							</span>
						{/if}

						{#if hasUrl}
							<Icon icon="solar:external-link-bold" class="size-3 text-base-content/40" />
						{/if}
					</div>

					<h3 class="font-semibold text-base line-clamp-1 mb-1">
						{notification.title}
					</h3>
				</div>

				<!-- Actions -->
				<div
					class="flex-shrink-0 flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity"
				>
					<!-- Toggle Read Status -->
					<div
						onclick={handleToggleRead}
						class="btn btn-ghost btn-sm cursor-pointer"
						title={notification.status === 'unread'
							? 'ทำเครื่องหมายว่าอ่านแล้ว'
							: 'ทำเครื่องหมายว่ายังไม่อ่าน'}
						role="button"
						tabindex="0"
						onkeydown={e => e.key === 'Enter' && handleToggleRead(e)}
					>
						<Icon
							icon={notification.status === 'unread'
								? 'solar:check-read-bold'
								: 'solar:eye-closed-bold'}
							class="size-4"
						/>
					</div>

					<!-- Delete -->
					<div
						onclick={handleDelete}
						class="btn btn-ghost btn-sm text-error cursor-pointer"
						title="ลบ"
						role="button"
						tabindex="0"
						onkeydown={e => e.key === 'Enter' && handleDelete(e)}
					>
						<Icon icon="solar:trash-bin-minimalistic-bold" class="size-4" />
					</div>
				</div>
			</div>

			<!-- Message -->
			<p class="text-sm text-base-content/80 line-clamp-2 mb-3">
				{notification.message}
			</p>

			<!-- Additional Data -->
			{#if notification.data}
				<div class="grid grid-cols-1 sm:grid-cols-2 gap-2 mb-3">
					{#if notification.data.amount}
						<div class="text-xs text-base-content/60">
							<span class="font-medium">จำนวนเงิน:</span>
							{notification.data.amount.toLocaleString()} บาท
						</div>
					{/if}

					{#if notification.data.balance}
						<div class="text-xs text-base-content/60">
							<span class="font-medium">ยอดคงเหลือ:</span>
							{notification.data.balance.toLocaleString()} บาท
						</div>
					{/if}

					{#if notification.data.stockLevel !== undefined}
						<div class="text-xs text-base-content/60">
							<span class="font-medium">สต็อกคงเหลือ:</span>
							{notification.data.stockLevel} ชิ้น
						</div>
					{/if}

					{#if notification.data.daysLeft !== undefined}
						<div class="text-xs text-base-content/60">
							<span class="font-medium">เหลือเวลา:</span>
							{notification.data.daysLeft} วัน
						</div>
					{/if}
				</div>
			{/if}

			<!-- Footer -->
			<div class="flex items-center justify-between text-xs text-base-content/50">
				<div class="flex items-center gap-3">
					<span title={formatFullTime(notification.createdAt)}>
						{formatTime(notification.createdAt)}
					</span>

					{#if notification.readAt}
						<span class="flex items-center gap-1">
							<Icon icon="solar:eye-bold" class="size-3" />
							อ่านแล้ว {formatTime(notification.readAt)}
						</span>
					{/if}
				</div>

				<!-- Delivery Status -->
				<div class="flex items-center gap-2">
					{#if notification.deliveryStatus.inApp.delivered}
						<Icon icon="solar:check-circle-bold" class="size-3 text-success" />
					{/if}

					{#if notification.channels.email && notification.deliveryStatus.email.sent}
						<Icon icon="solar:letter-bold" class="size-3 text-info" />
					{/if}

					{#if notification.channels.push && notification.deliveryStatus.push.sent}
						<Icon icon="solar:bell-bing-bold" class="size-3 text-warning" />
					{/if}
				</div>
			</div>
		</div>
	</div>
</button>

<style>
	.bg-base-50 {
		background-color: hsl(var(--b1) / 0.5);
	}

	.line-clamp-1 {
		display: -webkit-box;
		-line-clamp: 1;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}

	.line-clamp-2 {
		display: -webkit-box;
		-line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}
</style>
