<!doctype html>
<html lang="en">
	<head>
		<meta charset="utf-8" />
		<link rel="icon" href="%sveltekit.assets%/favicon.svg" />
		<meta name="viewport" content="width=device-width, initial-scale=1" />
		<title>WebShop Admin Dashboard</title>

		<!-- Theme script - ป้องกันการกระพริบ -->
		<script>
			(function () {
				try {
					const savedTheme = localStorage.getItem('theme');
					let effectiveTheme = 'light'; // default fallback

					if (savedTheme) {
						if (savedTheme === 'auto') {
							// ตรวจสอบ system preference สำหรับ auto mode
							if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
								effectiveTheme = 'dark';
							} else {
								effectiveTheme = 'light';
							}
						} else if (savedTheme === 'dark' || savedTheme === 'light') {
							effectiveTheme = savedTheme;
						}
					} else {
						// ไม่มี saved theme ให้ตรวจสอบ system preference
						if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
							effectiveTheme = 'dark';
						}
					}

					document.documentElement.setAttribute('data-theme', effectiveTheme);
				} catch (error) {
					// Fallback ในกรณีที่เกิด error
					document.documentElement.setAttribute('data-theme', 'light');
				}
			})();
		</script>

		%sveltekit.head%
	</head>

	<body data-sveltekit-preload-data="hover" class="min-h-screen bg-base-100">
		<div style="display: contents">%sveltekit.body%</div>
	</body>
</html>
