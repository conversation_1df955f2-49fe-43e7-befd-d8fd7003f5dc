import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';

export const GET: RequestHandler = async ({ locals, cookies }) => {
  try {
    // ตรวจสอบ authentication จาก locals (ที่ถูกตั้งค่าใน hooks.server.ts)
    const hasUser = !!locals.user;
    const hasToken = !!locals.token;

    // ตรวจสอบ cookies
    const authToken = cookies.get('auth_token');
    const refreshToken = cookies.get('refreshToken');
    const sessionId = cookies.get('session_id');

    const result = {
      success: true,
      message: 'Server-side auth test completed',
      data: {
        // Locals data (from hooks.server.ts)
        hasUserInLocals: hasUser,
        hasTokenInLocals: hasToken,
        userIdFromLocals: locals.user?._id,
        userEmailFromLocals: locals.user?.email,

        // Cookies data
        hasAuthTokenCookie: !!authToken,
        hasRefreshTokenCookie: !!refreshToken,
        hasSessionIdCookie: !!sessionId,
        authTokenLength: authToken?.length || 0,
        refreshTokenLength: refreshToken?.length || 0,

        // Server info
        timestamp: new Date().toISOString(),
        nodeEnv: process.env.NODE_ENV,
      },
    };

    return json(result);
  }
  catch (error) {
    console.error('Test auth API error:', error);

    return json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown server error',
      timestamp: new Date().toISOString(),
    }, { status: 500 });
  }
};
