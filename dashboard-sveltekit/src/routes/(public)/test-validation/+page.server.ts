import { fail } from '@sveltejs/kit';
import { z } from 'zod';
import type { Actions } from './$types';

// สร้าง schema สำหรับทดสอบ
const testSchema = z.object({
  name: z.string().min(2, 'ชื่อต้องมีอย่างน้อย 2 ตัวอักษร'),
  email: z.email('รูปแบบอีเมลไม่ถูกต้อง'),
  age: z.coerce.number().min(18, 'อายุต้องมากกว่า 18 ปี').optional(),
});

// Validation function ที่ส่งคืน field-level errors
function validateTestForm(data: unknown) {
  const result = testSchema.safeParse(data);
  if (result.success) {
    return {
      success: true,
      data: result.data,
      errors: {},
    };
  }

  const errors: Record<string, string> = {};
  result.error.issues.forEach(issue => {
    const path = issue.path.join('.');
    if (path) {
      errors[path] = issue.message;
    }
  });

  return {
    success: false,
    errors,
  };
}

export const actions: Actions = {
  default: async ({ request }) => {
    const formData = await request.formData();
    const name = formData.get('name') as string;
    const email = formData.get('email') as string;
    const ageStr = formData.get('age') as string;

    const data = {
      name: name?.trim() || '',
      email: email?.trim() || '',
      age: ageStr ? parseInt(ageStr) : undefined,
    };

    console.log('Server received data:', data);

    // Validate input with detailed field errors
    const validation = validateTestForm(data);
    if (!validation.success) {
      console.log('Validation failed:', validation.errors);

      return fail(400, {
        message: 'กรุณาตรวจสอบข้อมูลที่กรอก',
        type: 'validation',
        errors: validation.errors,
        name: data.name,
        email: data.email,
        age: data.age,
      });
    }

    console.log('Validation passed:', validation.data);

    // Simulate success
    return {
      success: true,
      message: 'บันทึกข้อมูลสำเร็จ!',
      data: validation.data,
    };
  },
};
