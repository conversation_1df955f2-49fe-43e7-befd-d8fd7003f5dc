<script lang="ts">
	import { enhance } from '$app/forms';

	const { form } = $props<{
		form?: any;
	}>();

	// Form state variables
	let nameValue = $state(form?.name || '');
	let emailValue = $state(form?.email || '');
	let ageValue = $state<number | undefined>(form?.age || undefined);
	let isSubmitting = $state(false);
</script>

<div class="container mx-auto max-w-md p-6">
	<h1 class="text-2xl font-bold mb-6">ทดสอบ Validation System</h1>

	<!-- Debug Info -->
	<div class="bg-base-200 p-4 rounded-lg mb-6 text-sm">
		<h3 class="font-semibold mb-2">Server Response:</h3>
		{#if form}
			<div class="space-y-1">
				<div>Success: <span class="badge {form.success ? 'badge-success' : 'badge-error'}">{form.success || false}</span></div>
				<div>Message: {form.message || 'ไม่มี'}</div>
				{#if form.errors}
					<div>Errors: {JSON.stringify(form.errors)}</div>
				{/if}
			</div>
		{:else}
			<div class="text-gray-500">ยังไม่ได้ส่งข้อมูล</div>
		{/if}
		<div class="mt-2">
			<div>Name: "{nameValue}"</div>
			<div>Email: "{emailValue}"</div>
			<div>Age: {ageValue}</div>
		</div>
	</div>

	<!-- Form -->
	<form method="POST" use:enhance={() => {
		isSubmitting = true;
		return async ({ update }) => {
			await update();
			isSubmitting = false;
		};
	}} class="space-y-4">
		<!-- Name Input -->
		<div class="form-control">
			<label class="label" for="name">
				<span class="label-text">ชื่อ</span>
			</label>
			<input
				id="name"
				name="name"
				type="text"
				bind:value={nameValue}
				class="input input-bordered {form?.errors?.name ? 'input-error' : ''}"
				placeholder="กรอกชื่อของคุณ"
				disabled={isSubmitting}
			/>
			{#if form?.errors?.name}
				<label for="name" class="label">
					<span class="label-text-alt text-error">{form.errors.name}</span>
				</label>
			{/if}
		</div>

		<!-- Email Input -->
		<div class="form-control">
			<label class="label" for="email">
				<span class="label-text">อีเมล</span>
			</label>
			<input
				id="email"
				name="email"
				type="email"
				bind:value={emailValue}
				class="input input-bordered {form?.errors?.email ? 'input-error' : ''}"
				placeholder="<EMAIL>"
				disabled={isSubmitting}
			/>
			{#if form?.errors?.email}
				<label for="email" class="label">
					<span class="label-text-alt text-error">{form.errors.email}</span>
				</label>
			{/if}
		</div>

		<!-- Age Input -->
		<div class="form-control">
			<label class="label" for="age">
				<span class="label-text">อายุ (ไม่บังคับ)</span>
			</label>
			<input
				id="age"
				name="age"
				type="number"
				bind:value={ageValue}
				class="input input-bordered {form?.errors?.age ? 'input-error' : ''}"
				placeholder="กรอกอายุของคุณ"
				disabled={isSubmitting}
			/>
			{#if form?.errors?.age}
				<label for="age" class="label">
					<span class="label-text-alt text-error">{form.errors.age}</span>
				</label>
			{/if}
		</div>

		<!-- Submit Button -->
		<button
			type="submit"
			class="btn btn-primary w-full"
			disabled={isSubmitting}
		>
			{isSubmitting ? 'กำลังส่ง...' : 'ส่งข้อมูล'}
		</button>

		<!-- Reset Button -->
		<button
			type="button"
			class="btn btn-outline w-full"
			disabled={isSubmitting}
			onclick={() => {
				nameValue = '';
				emailValue = '';
				ageValue = undefined;
			}}
		>
			รีเซ็ต
		</button>
	</form>

	<!-- Instructions -->
	<div class="mt-8 text-sm text-gray-600">
		<h3 class="font-semibold mb-2">วิธีทดสอบ:</h3>
		<ul class="list-disc list-inside space-y-1">
			<li>พิมพ์ชื่อสั้นกว่า 2 ตัวอักษร → จะเห็น error</li>
			<li>พิมพ์อีเมลไม่ครบ → จะเห็น error</li>
			<li>พิมพ์อายุน้อยกว่า 18 → จะเห็น error</li>
			<li>กรอกข้อมูลให้ถูกต้อง → ปุ่มส่งข้อมูลจะใช้งานได้</li>
			<li>คลิกจุดอื่น (blur) → จะ validate ทันที</li>
		</ul>
	</div>
</div>
