<script lang="ts">
	import Icon from '@iconify/svelte';
	import { onMount } from 'svelte';
	import { t } from 'svelte-i18n';
	import { enhance } from '$app/forms';
	import { goto } from '$app/navigation';
	import SEO from '$lib/components/layout/SEO.svelte';
	import Button from '$lib/components/ui/Button.svelte';
	import Card from '$lib/components/ui/Card.svelte';
	import Input from '$lib/components/ui/Input.svelte';
	import Recaptcha from '$lib/components/ui/Recaptcha.svelte';
	import { type SignupData, signupSchema } from '$lib/schemas/auth.schema';
	import { authStore } from '$lib/stores/auth.svelte';
	import { createFormValidator } from '$lib/utils/validation';
	import {
		showRegisterError,
		showRegisterSuccess,
		showValidationError,
	} from '$lib/utils/sweetalert';

	const { form } = $props<{
		form?: any;
	}>();

	// สร้าง form validator ที่ง่ายและ clean
	const formValidator = createFormValidator(signupSchema, {
		email: '',
		password: '',
		confirmPassword: '',
		agreeToTerms: false,
	});

	// สร้าง field binders
	const emailField = formValidator.createFieldBinder('email');
	const passwordField = formValidator.createFieldBinder('password');
	const confirmPasswordField = formValidator.createFieldBinder('confirmPassword');

	let recaptchaToken = $state<string>('');
	let recaptchaComponent: Recaptcha;
	let debounceTimer: NodeJS.Timeout;

	// Form action result
	const formResult = $derived(form);

	// Realtime validation with debouncing
	$effect(() => {
		clearTimeout(debounceTimer);
		debounceTimer = setTimeout(() => {
			const emailError = emailField.validateOnInput(formData.email);
			if (emailError !== errors.email) {
				errors = { ...errors, email: emailError || '' };
			}
		}, 300); // รอ 300ms หลังหยุดพิมพ์
	});

	$effect(() => {
		clearTimeout(debounceTimer);
		debounceTimer = setTimeout(() => {
			const passwordError = passwordField.validateOnInput(formData.password);
			if (passwordError !== errors.password) {
				errors = { ...errors, password: passwordError || '' };
			}
		}, 300); // รอ 300ms หลังหยุดพิมพ์
	});

	$effect(() => {
		clearTimeout(debounceTimer);
		debounceTimer = setTimeout(() => {
			const confirmPasswordError = confirmPasswordField.validateOnInput(formData.confirmPassword);
			if (confirmPasswordError !== errors.confirmPassword) {
				errors = { ...errors, confirmPassword: confirmPasswordError || '' };
			}
		}, 300); // รอ 300ms หลังหยุดพิมพ์
	});

	onMount(() => {
		// ถ้า login แล้วให้ redirect ไป dashboard
		if (authStore.isAuthenticated) {
			goto('/dashboard');
		}
	});

	// Redirect after successful signup
	$effect(() => {
		if (formResult?.success) {
			showRegisterSuccess();
			setTimeout(() => {
				goto('/dashboard');
			}, 1500);
		}
	});

	function handleKeydown(event: KeyboardEvent) {
		if (event.key === 'Enter') {
			const target = event.target as HTMLElement;
			const form = target?.closest('form') as HTMLFormElement;
			if (form) {
				form.requestSubmit();
			}
		}
	}
</script>

<SEO title={$t('auth.signup')} />

<Card centered variant="default" shadow="lg" size="sm" title={$t('auth.signup')}>
	<!-- Form Action Messages -->
	{#if formResult?.success}
		<div class="alert alert-success">
			<Icon icon="mdi:check-circle" class="w-5 h-5" />
			<span>{formResult.message}</span>
		</div>
	{:else if formResult?.error}
		<div class="alert alert-error">
			<Icon icon="mdi:alert-circle" class="w-5 h-5" />
			<span>{formResult.error}</span>
		</div>
	{/if}

	<!-- Register Form -->
	<form
		method="POST"
		action="?/signup"
		class="space-y-6"
		use:enhance={() => {
			return async ({ result }) => {
				if (result.type === 'failure') {
					showRegisterError(`${result.data?.error || 'ลงทะเบียนล้มเหลว'}`);
				}
			};
		}}
	>
		<Input
			id="email"
			name="email"
			type="email"
			variant="bordered"
			bind:value={emailField.value}
			label={$t('auth.email')}
			placeholder="<EMAIL>"
			icon="mdi:email"
			validate={emailField.error}
			required
			autocomplete="email"
			onkeydown={handleKeydown}
			showRequired={true}
			onblur={emailField.onBlur}
		/>

		<Input
			id="password"
			name="password"
			type="password"
			bind:value={passwordField.value}
			label={$t('auth.password')}
			placeholder="••••••••"
			icon="mdi:lock"
			validate={passwordField.error}
			required
			showPasswordToggle
			autocomplete="new-password"
			onkeydown={handleKeydown}
			showRequired={true}
			onblur={passwordField.onBlur}
		/>

		<Input
			id="confirmPassword"
			name="confirmPassword"
			type="password"
			bind:value={confirmPasswordField.value}
			label={$t('auth.confirmPassword')}
			placeholder="••••••••"
			icon="mdi:lock-check"
			validate={confirmPasswordField.error}
			required
			showPasswordToggle
			autocomplete="new-password"
			onkeydown={handleKeydown}
			showRequired={true}
			onblur={confirmPasswordField.onBlur}
		/>

		<div class="flex items-start gap-2">
			<input
				id="agreeToTerms"
				name="agreeToTerms"
				type="checkbox"
				bind:checked={formValidator.data.agreeToTerms}
				class="checkbox checkbox-primary checkbox-sm mt-1"
			/>
			<label for="agreeToTerms" class="label cursor-pointer">
				<span class="label-text text-sm">
					{$t('auth.agreeToTerms')}
					<a href="/terms" class="link link-primary">{$t('auth.termsOfService')}</a>
					{$t('common.and')}
					<a href="/privacy" class="link link-primary">{$t('auth.privacyPolicy')}</a>
				</span>
			</label>
		</div>

		{#if authStore.error}
			<div class="alert alert-error">
				<Icon icon="mdi:alert-circle" class="w-5 h-5" />
				<span>{authStore.error}</span>
			</div>
		{/if}

		<!-- reCAPTCHA - Flexible (v2 default, can easily switch to v3) -->
		<Recaptcha
			bind:this={recaptchaComponent}
			version="v2"
			action="signup"
			callback={token => {
				console.log('reCAPTCHA token received:', token.substring(0, 50) + '...');
				recaptchaToken = token;
			}}
			expiredCallback={() => {
				console.log('reCAPTCHA expired');
				recaptchaToken = '';
			}}
			errorCallback={() => {
				console.log('reCAPTCHA error');
				recaptchaToken = '';
				showRegisterError('เกิดข้อผิดพลาดในการตรวจสอบ reCAPTCHA กรุณาลองใหม่อีกครั้ง');
			}}
		/>

		<!-- Hidden field สำหรับส่ง reCAPTCHA token และ version -->
		<input type="hidden" name="recaptchaToken" value={recaptchaToken} />
		<input type="hidden" name="recaptchaVersion" value="v2" />

		<Button
			type="submit"
			color="primary"
			size="lg"
			block
			loading={authStore.isLoading}
			disabled={authStore.isLoading || !recaptchaToken || !formValidator.isValid}
		>
			{$t('auth.signup')}
		</Button>

		{#if !recaptchaToken}
			<div class="text-center text-sm text-warning">
				<Icon icon="mdi:shield-alert" class="w-4 h-4 inline mr-1" />
				กรุณายืนยัน reCAPTCHA ก่อนลงทะเบียน
			</div>
		{/if}
	</form>

	<!-- Login Link -->
	<div class="text-center mt-6">
		<span class="text-base-content/60">{$t('auth.hasAccount')} </span>
		<a href="/signin" class="link link-primary">
			{$t('auth.signin')}
		</a>
	</div>
</Card>
