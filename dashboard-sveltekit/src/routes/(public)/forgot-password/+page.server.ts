import { authService } from '$lib/services/auth';
import { LogCategory, logger } from '$lib/utils/logger';
import { fail, redirect } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';

export const prerender = false;

export const load: PageServerLoad = async ({ locals }) => {
  // ถ้ามี user อยู่แล้ว ให้ redirect ไป dashboard
  if (locals.user) {
    throw redirect(302, '/dashboard');
  }

  return {
    user: null,
  };
};

export const actions: Actions = {
  forgotPassword: async ({ request, getClientAddress }) => {
    const clientIP = getClientAddress();
    const userAgent = request.headers.get('user-agent') || 'unknown';

    try {
      const formData = await request.formData();
      const email = formData.get('email') as string;
      const recaptchaToken = formData.get('recaptchaToken') as string;

      // Validation
      if (!email?.trim()) {
        return fail(400, {
          error: 'กรุณากรอกอีเมล',
        });
      }

      // ✅ ตรวจสอบ reCAPTCHA token ก่อน
      if (!recaptchaToken) {
        logger.warn(
          LogCategory.AUTH,
          'forgot_password_no_recaptcha',
          'No reCAPTCHA token provided',
          {
            email: email?.substring(0, 3) + '***',
            clientIP,
            userAgent,
          },
        );

        return fail(400, {
          error: 'กรุณายืนยัน reCAPTCHA',
        });
      }

      // ✅ ตรวจสอบ reCAPTCHA token กับ Google (Smart Verification - รองรับทั้ง v2 และ v3)
      try {
        const { verifyRecaptchaSmart } = await import('$lib/utils/recaptcha-verify');
        const recaptchaResult = await verifyRecaptchaSmart(
          recaptchaToken,
          clientIP,
          'forgot_password', // action
          {
            v3MinScore: 0.3, // lower minimum score for forgot password (less strict)
            strictActionCheck: true, // ตรวจสอบ action อย่างเข้มงวดสำหรับ v3
          },
        );

        if (!recaptchaResult.success) {
          let errorMessage = 'การตรวจสอบ reCAPTCHA ไม่สำเร็จ กรุณาลองใหม่อีกครั้ง';

          // Provide specific error messages based on failure reason
          switch (recaptchaResult.reason) {
            case 'low_score':
              errorMessage = `คะแนนความปลอดภัยต่ำเกินไป (${recaptchaResult.score?.toFixed(2)}) กรุณาลองใหม่อีกครั้ง`;
              break;
            case 'action_mismatch':
              errorMessage = 'การดำเนินการไม่ถูกต้อง กรุณาลองใหม่อีกครั้ง';
              break;
            case 'verification_failed':
              errorMessage = 'การตรวจสอบ reCAPTCHA ล้มเหลว กรุณายืนยัน reCAPTCHA อีกครั้ง';
              break;
            case 'no_token':
              errorMessage = 'กรุณายืนยัน reCAPTCHA ก่อนส่งอีเมล';
              break;
            case 'config_error':
              errorMessage = 'ระบบ reCAPTCHA ไม่พร้อมใช้งาน กรุณาลองใหม่ภายหลัง';
              break;
            default:
              errorMessage = 'การตรวจสอบ reCAPTCHA ไม่สำเร็จ กรุณาลองใหม่อีกครั้ง';
          }

          logger.warn(
            LogCategory.AUTH,
            'forgot_password_recaptcha_failed',
            'reCAPTCHA verification failed',
            {
              email: email?.substring(0, 3) + '***',
              reason: recaptchaResult.reason,
              score: recaptchaResult.score,
              action: recaptchaResult.action,
              clientIP,
              userAgent,
            },
          );

          return fail(400, {
            error: errorMessage,
          });
        }

        logger.info(
          LogCategory.AUTH,
          'forgot_password_recaptcha_success',
          'reCAPTCHA verification successful',
          {
            email: email?.substring(0, 3) + '***',
            detectedVersion: recaptchaResult.detectedVersion,
            score: recaptchaResult.score,
            action: recaptchaResult.action,
            clientIP,
            userAgent,
          },
        );
      }
      catch (error) {
        logger.error(
          LogCategory.AUTH,
          'forgot_password_recaptcha_error',
          'reCAPTCHA verification error',
          {
            error: error instanceof Error ? error.message : 'Unknown error',
            email: email?.substring(0, 3) + '***',
            clientIP,
            userAgent,
          },
        );

        return fail(500, {
          error: 'เกิดข้อผิดพลาดในการตรวจสอบ reCAPTCHA',
        });
      }

      // เรียก authService โดยตรง
      const result = await authService.forgotPassword({
        email: email.trim(),
        recaptchaToken,
      });

      if (result.success) {
        return {
          success: true,
          message: 'ส่งอีเมลรีเซ็ตรหัสผ่านแล้ว กรุณาตรวจสอบกล่องจดหมายของคุณ',
        };
      }
      else {
        return {
          success: false,
          error: result.error || 'ไม่สามารถส่งอีเมลรีเซ็ตรหัสผ่านได้',
        };
      }
    }
    catch (error) {
      console.error('Forgot password action error:', error);
      return {
        success: false,
        error: 'เกิดข้อผิดพลาดในการส่งอีเมลรีเซ็ตรหัสผ่าน',
      };
    }
  },
};
