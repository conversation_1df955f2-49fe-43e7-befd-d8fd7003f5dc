import { sanitizeAuthData, validateSigninForm } from '$lib/schemas/auth.schema';
import { authService } from '$lib/services/auth';
import { <PERSON><PERSON>r<PERSON><PERSON><PERSON> } from '$lib/utils/error-handler';
import { LogCategory, logger } from '$lib/utils/logger';
import { checkRateLimit, clearRateLimit } from '$lib/utils/rate-limit';
import { fail, redirect } from '@sveltejs/kit';
import crypto from 'crypto';
import type { Actions, PageServerLoad } from './$types';
// Token rotation imports removed - using backend Token Rotation System

export const prerender = false;

export const load: PageServerLoad = async ({ locals }) => {
  // ถ้ามี user อยู่แล้ว ให้ redirect ไป dashboard
  if (locals.user) {
    throw redirect(302, '/dashboard');
  }

  return {
    user: null,
  };
};

export const actions: Actions = {
  signin: async ({ request, cookies, getClientAddress }) => {
    const clientIP = getClientAddress();
    const userAgent = request.headers.get('user-agent') || 'unknown';
    const sessionId = cookies.get('session_id') || crypto.randomUUID();

    // Add debugging to track server requests
    console.log('🚀 Server received signin request', {
      timestamp: new Date().toISOString(),
      clientIP,
      userAgent: userAgent.substring(0, 50) + '...',
      sessionId: sessionId.substring(0, 8) + '...',
    });

    try {
      const formData = await request.formData();
      const email = formData.get('email') as string;
      const password = formData.get('password') as string;
      const rememberMe = formData.get('rememberMe') === 'true';
      const recaptchaToken = formData.get('recaptchaToken') as string;

      // // ✅ ตรวจสอบ reCAPTCHA token ก่อน
      // if (!recaptchaToken) {
      // 	logger.warn(LogCategory.AUTH, 'signin_no_recaptcha', 'No reCAPTCHA token provided', {
      // 		email: email?.substring(0, 3) + '***',
      // 		clientIP,
      // 		userAgent,
      // 	});

      // 	return fail(400, {
      // 		message: 'กรุณายืนยัน reCAPTCHA',
      // 		type: 'recaptcha_required',
      // 	});
      // }

      // // ✅ ตรวจสอบ reCAPTCHA token กับ Google
      // try {
      // 	const { verifyRecaptcha } = await import('$lib/utils/recaptcha-verify');
      // 	const isRecaptchaValid = await verifyRecaptcha(recaptchaToken, clientIP);

      // 	if (!isRecaptchaValid) {
      // 		logger.warn(
      // 			LogCategory.AUTH,
      // 			'signin_recaptcha_failed',
      // 			'reCAPTCHA verification failed',
      // 			{
      // 				email: email?.substring(0, 3) + '***',
      // 				clientIP,
      // 				userAgent,
      // 			}
      // 		);

      // 		return fail(400, {
      // 			message: 'การตรวจสอบ reCAPTCHA ไม่สำเร็จ กรุณาลองใหม่อีกครั้ง',
      // 			type: 'recaptcha_failed',
      // 		});
      // 	}

      // 	logger.info(
      // 		LogCategory.AUTH,
      // 		'signin_recaptcha_success',
      // 		'reCAPTCHA verification successful',
      // 		{
      // 			email: email?.substring(0, 3) + '***',
      // 			clientIP,
      // 			userAgent,
      // 		}
      // 	);
      // } catch (error) {
      // 	logger.error(LogCategory.AUTH, 'signin_recaptcha_error', 'reCAPTCHA verification error', {
      // 		error: error instanceof Error ? error.message : 'Unknown error',
      // 		email: email?.substring(0, 3) + '***',
      // 		clientIP,
      // 		userAgent,
      // 	});

      // 	return fail(500, {
      // 		message: 'เกิดข้อผิดพลาดในการตรวจสอบ reCAPTCHA',
      // 		type: 'recaptcha_error',
      // 	});
      // }

      // Sanitize input
      const sanitizedData = sanitizeAuthData({
        email: email?.trim() || '',
        password: password?.trim() || '',
      });

      // ✅ Check rate limiting for signin attempts
      const rateLimitId = `${clientIP}:${sanitizedData.email}`;
      const rateLimitResult = checkRateLimit('AUTH_SIGNIN', rateLimitId);

      if (!rateLimitResult.allowed && !rateLimitResult.blocked && import.meta.env.NODE_ENV === 'production') {
        logger.warn(LogCategory.AUTH, 'signin_rate_limited', 'Signin rate limit exceeded', {
          email: sanitizedData.email?.substring(0, 3) + '***',
          clientIP,
          userAgent,
          remaining: rateLimitResult.remaining,
          resetTime: rateLimitResult.resetTime,
          blocked: rateLimitResult.blocked,
        });

        const resetTimeFormatted = new Date(rateLimitResult.resetTime).toLocaleTimeString('th-TH');
        const errorMessage = rateLimitResult.blocked
          ? `บัญชีถูกล็อกชั่วคราว กรุณารอจนถึง ${resetTimeFormatted}`
          : `ลองเข้าสู่ระบบหลายครั้งเกินไป กรุณารอ ${Math.ceil((rateLimitResult.resetTime - Date.now()) / 60000)} นาที`;

        // ใช้ ErrorHandler สำหรับการจัดการ error ที่สอดคล้องกัน
        const userMessage = ErrorHandler.toUserMessage(errorMessage);

        return fail(429, {
          message: userMessage,
          type: 'rate_limit',
        });
      }

      // Validate input with detailed field errors
      const validation = validateSigninForm(sanitizedData);
      if (!validation.success) {
        logger.warn(LogCategory.AUTH, 'signin_validation_failed', 'Signin validation failed', {
          email: sanitizedData.email?.substring(0, 3) + '***',
          errors: validation.errors,
          clientIP,
          userAgent,
        });

        return fail(400, {
          message: 'กรุณาตรวจสอบข้อมูลที่กรอก',
          type: 'validation',
          errors: validation.errors,
          email: sanitizedData.email,
          password: '', // ไม่ส่งรหัสผ่านกลับ
          rememberMe: formData.get('rememberMe') === 'true',
        });
      }

      // Log signin attempt
      logger.info(LogCategory.AUTH, 'signin_attempt', 'User attempting signin', {
        email: sanitizedData.email?.substring(0, 3) + '***',
        clientIP,
        userAgent,
        sessionId,
      });

      // เรียก authService พร้อม reCAPTCHA token
      const result = await authService.signin({
        ...sanitizedData,
        recaptchaToken,
        rememberMe,
      });

      if (result.success && result.data) {
        // ✅ Clear rate limit on successful signin
        clearRateLimit('AUTH_SIGNIN', rateLimitId);

        // ✅ จัดการ cookie ที่ปลอดภัย
        const cookieOptions = {
          path: '/',
          httpOnly: true,
          secure: import.meta.env.NODE_ENV === 'production',
          sameSite: 'strict' as const,
        };

        if (rememberMe) {
          cookies.set('remember_me', 'true', {
            ...cookieOptions,
            maxAge: 60 * 60 * 24 * 30, // 30 วัน
          });
        }
        else {
          cookies.delete('remember_me', { path: '/' });
        }

        // Set auth token cookie
        cookies.set('auth_token', result.data.token, {
          ...cookieOptions,
          maxAge: 60 * 60 * 24 * 7, // 7 วัน
        });

        // ✅ ใช้ refresh token จาก backend (Token Rotation System)
        console.log('[signin] Using Backend Token Rotation System');
        console.log('[signin] Backend response data:', {
          hasUser: !!result.data.user,
          hasToken: !!result.data.token,
          hasRefreshToken: !!result.data.refreshToken,
          refreshTokenLength: result.data.refreshToken?.length,
          refreshTokenType: typeof result.data.refreshToken,
        });

        if (result.data.refreshToken) {
          console.log('[signin] Setting Backend Token Rotation refresh token cookie');
          console.log(
            '[signin] Refresh token preview:',
            result.data.refreshToken.substring(0, 50) + '...',
          );

          cookies.set('refreshToken', result.data.refreshToken, {
            ...cookieOptions,
            maxAge: 60 * 60 * 24 * 30, // 30 วัน
          });
          console.log('[signin] Backend Token Rotation refresh token cookie set successfully');
        }
        else {
          console.error('[signin] No refresh token received from backend');
        }

        // ✅ Set session cookie with rotation
        const newSessionId = crypto.randomUUID();
        cookies.set('session_id', newSessionId, {
          ...cookieOptions,
          maxAge: 60 * 60 * 24, // 1 วัน
        });

        // ✅ Set CSRF token
        const csrfToken = crypto.randomBytes(32).toString('hex');
        cookies.set('csrf_token', csrfToken, {
          ...cookieOptions,
          maxAge: 60 * 60 * 24, // 1 วัน
        });

        // Log successful signin
        logger.info(LogCategory.AUTH, 'signin_success', 'User signed in successfully', {
          userId: result.data.user._id,
          email: result.data.user.email?.substring(0, 3) + '***',
          clientIP,
          userAgent,
          sessionId,
        });

        // Return success data for client-side handling (ไม่ส่ง token กลับ)
        return {
          success: true,
          user: result.data.user,
          message: 'เข้าสู่ระบบสำเร็จ',
          type: 'signin',
        };
      }
      else {
        // Log failed signin
        logger.warn(LogCategory.AUTH, 'signin_failed', 'Signin failed', {
          email: sanitizedData.email?.substring(0, 3) + '***',
          error: result.error,
          clientIP,
          userAgent,
          sessionId,
        });

        // ใช้ ErrorHandler สำหรับการจัดการ error ที่สอดคล้องกัน
        const userMessage = ErrorHandler.toUserMessage(result.error);

        // Return error message สำหรับ client-side
        return fail(401, {
          message: userMessage,
          type: 'auth_failed',
        });
      }
    }
    catch (error) {
      // Log error
      logger.error(LogCategory.AUTH, 'signin_exception', 'Exception during signin', {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        clientIP,
        userAgent,
        sessionId,
      });

      // ใช้ ErrorHandler สำหรับการจัดการ error ที่สอดคล้องกัน
      const userMessage = ErrorHandler.toUserMessage(error);

      return fail(500, {
        message: userMessage,
        type: 'server_error',
      });
    }
  },
};
