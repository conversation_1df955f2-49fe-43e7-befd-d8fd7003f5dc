import { authService } from '$lib/services/auth';
import { redirect } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';

export const prerender = false;

export const load: PageServerLoad = async ({ locals, url }) => {
  // ถ้ามี user อยู่แล้ว ให้ redirect ไป dashboard
  if (locals.user) {
    throw redirect(302, '/dashboard');
  }

  // ตรวจสอบ token จาก URL
  const token = url.searchParams.get('token');
  if (!token) {
    return {
      error: 'Token ไม่ถูกต้อง',
    };
  }

  return {
    user: null,
    token,
  };
};

export const actions: Actions = {
  resetPassword: async ({ request, url }) => {
    try {
      const formData = await request.formData();
      const password = formData.get('password') as string;
      const confirmPassword = formData.get('confirmPassword') as string;
      const token = url.searchParams.get('token');

      // Validation
      if (!token) {
        return {
          success: false,
          error: 'Token ไม่ถูกต้อง',
        };
      }

      if (!password?.trim()) {
        return {
          success: false,
          error: 'กรุณากรอกรหัสผ่าน',
        };
      }

      if (password !== confirmPassword) {
        return {
          success: false,
          error: 'รหัสผ่านไม่ตรงกัน',
        };
      }

      if (password.length < 6) {
        return {
          success: false,
          error: 'รหัสผ่านต้องมีอย่างน้อย 6 ตัวอักษร',
        };
      }

      // เรียก authService โดยตรง
      const result = await authService.resetPassword(token, password.trim());

      if (result.success) {
        return {
          success: true,
          message: 'รีเซ็ตรหัสผ่านสำเร็จ! กรุณาเข้าสู่ระบบด้วยรหัสผ่านใหม่',
        };
      }
      else {
        return {
          success: false,
          error: result.error || 'ไม่สามารถรีเซ็ตรหัสผ่านได้',
        };
      }
    }
    catch (error) {
      console.error('Reset password action error:', error);
      return {
        success: false,
        error: 'เกิดข้อผิดพลาดในการรีเซ็ตรหัสผ่าน',
      };
    }
  },
};
