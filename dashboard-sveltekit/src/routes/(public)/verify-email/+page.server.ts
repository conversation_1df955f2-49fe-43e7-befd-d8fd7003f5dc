import { authService } from '$lib/services/auth';
import { redirect } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';

export const prerender = false;

export const load: PageServerLoad = async ({ locals, url }) => {
  // ถ้ามี user อยู่แล้ว ให้ redirect ไป dashboard
  if (locals.user) {
    throw redirect(302, '/dashboard');
  }

  // ตรวจสอบ token จาก URL
  const token = url.searchParams.get('token');
  if (!token) {
    return {
      error: 'Token ไม่ถูกต้อง',
    };
  }

  return {
    user: null,
    token,
  };
};

export const actions: Actions = {
  verifyEmail: async ({ request, url }) => {
    try {
      const token = url.searchParams.get('token');

      // Validation
      if (!token) {
        return {
          success: false,
          error: 'Token ไม่ถูกต้อง',
        };
      }

      // เรียก authService โดยตรง
      const result = await authService.verifyEmail(token);

      if (result.success) {
        return {
          success: true,
          message: 'ยืนยันอีเมลสำเร็จ! กรุณาเข้าสู่ระบบ',
        };
      }
      else {
        return {
          success: false,
          error: result.error || 'ไม่สามารถยืนยันอีเมลได้',
        };
      }
    }
    catch (error) {
      console.error('Verify email action error:', error);
      return {
        success: false,
        error: 'เกิดข้อผิดพลาดในการยืนยันอีเมล',
      };
    }
  },
};
