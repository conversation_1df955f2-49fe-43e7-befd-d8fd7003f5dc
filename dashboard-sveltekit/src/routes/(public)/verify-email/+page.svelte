<script lang="ts">
	import Icon from '@iconify/svelte';
	import { onMount } from 'svelte';
	import { t } from 'svelte-i18n';
	import { enhance } from '$app/forms';
	import { goto } from '$app/navigation';
	// Using native fetch instead of ofetch
	import SEO from '$lib/components/layout/SEO.svelte';
	import Button from '$lib/components/ui/Button.svelte';
	import Card from '$lib/components/ui/Card.svelte';
	import Input from '$lib/components/ui/Input.svelte';
	import { showError, showLoading, showSuccess } from '$lib/utils/sweetalert';

	const { url, form } = $props();

	const isLoading = $state(false);
	let token = $state('');
	let isVerifying = $state(false);
	const email = $state('');
	let errors = $state<Record<string, string>>({});

	// Form action result
	const formResult = $derived(form);

	onMount(() => {
		const urlParams = new URLSearchParams(window.location.search);
		token = urlParams.get('token') || '';
		if (token) {
			verifyEmail();
		}
	});

	// Handle form result
	$effect(() => {
		if (formResult?.success) {
			showSuccess('สำเร็จ!', formResult.message);
			setTimeout(() => {
				goto('/signin');
			}, 2000);
		} else if (formResult?.error) {
			showError('เกิดข้อผิดพลาด', formResult.error);
		}
	});

	async function verifyEmail() {
		isVerifying = true;
		showLoading('กำลังยืนยันอีเมล...');

		try {
			const response = await fetch('/auth/verify-email', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({ token }),
			});

			if (!response.ok) {
				throw new Error(`HTTP ${response.status}: ${response.statusText}`);
			}

			const result = await response.json();

			if (result.success) {
				showSuccess('ยืนยันอีเมลสำเร็จ', 'กำลังไปยังหน้าเข้าสู่ระบบ...');
				setTimeout(() => {
					goto('/signin');
				}, 2000);
			} else {
				showError('ยืนยันอีเมลไม่สำเร็จ', result.message || 'Token ไม่ถูกต้องหรือหมดอายุ');
			}
		} catch (error) {
			console.error('Verify email error:', error);
			showError('ยืนยันอีเมลไม่สำเร็จ', 'เกิดข้อผิดพลาดในการเชื่อมต่อกับเซิร์ฟเวอร์');
		} finally {
			isVerifying = false;
		}
	}

	function validateEmail(): boolean {
		errors = {};

		if (!email) {
			errors.email = 'กรุณากรอกอีเมล';
		} else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
			errors.email = 'รูปแบบอีเมลไม่ถูกต้อง';
		}

		return Object.keys(errors).length === 0;
	}

	function handleKeydown(event: KeyboardEvent) {
		if (event.key === 'Enter') {
			const form = event.target?.closest('form') as HTMLFormElement;
			if (form) {
				form.requestSubmit();
			}
		}
	}
</script>

<SEO
	title={$t('auth.verifyEmailTitle')}
	description={$t('auth.verifyEmailDescription')}
	keywords="ยืนยันอีเมล, เปิดใช้งานบัญชี, ยืนยันตัวตน"
	url="/verify-email"
	noindex={true}
/>

<div
	class="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 p-4"
>
	<Card class="w-full max-w-md p-8 space-y-6">
		<!-- Header -->
		<div class="text-center space-y-2">
			<div class="flex justify-center mb-4">
				<div class="w-12 h-12 bg-primary rounded-full flex items-center justify-center">
					<Icon icon="mdi:email-check" class="w-6 h-6 text-primary-foreground" />
				</div>
			</div>
			<h1 class="text-2xl font-bold text-foreground">{$t('auth.verifyEmailTitle')}</h1>
			<p class="text-muted-foreground">
				{#if token}
					{$t('auth.verifyingEmail')}
				{:else}
					{$t('auth.verifyEmailTokenPlaceholder')}
				{/if}
			</p>
		</div>

		<!-- Form Action Messages -->
		{#if formResult?.success}
			<div class="alert alert-success">
				<Icon icon="mdi:check-circle" class="w-5 h-5" />
				<span>{formResult.message}</span>
			</div>
		{:else if formResult?.error}
			<div class="alert alert-error">
				<Icon icon="mdi:alert-circle" class="w-5 h-5" />
				<span>{formResult.error}</span>
			</div>
		{/if}

		{#if !token}
			<form
				method="POST"
				action="?/verifyEmail"
				use:enhance={() => {
					isLoading = true;
					showLoading('กำลังยืนยันอีเมล...');

					return async ({ result }) => {
						isLoading = false;
						if (result.type === 'failure') {
							showError('เกิดข้อผิดพลาด', result.data?.error || 'ไม่สามารถยืนยันอีเมลได้');
						}
					};
				}}
				class="space-y-4"
			>
				<Input
					id="token"
					name="token"
					type="text"
					placeholder="กรอก Token ที่ได้รับจากอีเมล"
					label="Token ยืนยันอีเมล"
					icon="mdi:key"
					error={errors.token}
					required
					onkeydown={handleKeydown}
				/>

				<Button
					type="submit"
					color="primary"
					size="lg"
					block
					loading={isLoading}
					disabled={isLoading}
				>
					ยืนยันอีเมล
				</Button>
			</form>

			<div class="relative">
				<div class="absolute inset-0 flex items-center">
					<div class="w-full border-t border-border"></div>
				</div>
				<div class="relative flex justify-center text-sm">
					<span class="px-2 bg-card text-muted-foreground">หรือ</span>
				</div>
			</div>

			<!-- Resend Verification Form -->
			<form
				method="POST"
				action="?/resendVerification"
				use:enhance={() => {
					return async ({ result }) => {
						if (result.type === 'failure') {
							showError('เกิดข้อผิดพลาด', result.data?.error || 'ไม่สามารถส่งอีเมลยืนยันใหม่ได้');
						}
					};
				}}
				class="space-y-4"
			>
				<div class="text-center mb-4">
					<p class="text-sm text-muted-foreground">ไม่ได้รับอีเมลยืนยัน?</p>
				</div>

				<Input
					id="email"
					name="email"
					type="email"
					bind:value={email}
					placeholder="<EMAIL>"
					label="อีเมล"
					icon="mdi:email"
					error={errors.email}
					required
					autocomplete="email"
					onkeydown={handleKeydown}
				/>

				<Button
					type="submit"
					color="secondary"
					size="lg"
					block
					loading={isLoading}
					disabled={isLoading}
				>
					ส่งอีเมลยืนยันใหม่
				</Button>
			</form>
		{:else}
			<div class="text-center">
				{#if isVerifying}
					<div class="flex items-center justify-center mb-4">
						<Icon icon="mdi:loading" class="animate-spin w-8 h-8 text-primary" />
					</div>
					<p class="text-base-content/70">กำลังยืนยันอีเมล...</p>
				{/if}
			</div>
		{/if}

		<div class="text-center">
			<a href="/signin" class="text-primary hover:text-primary-focus">กลับไปหน้าเข้าสู่ระบบ</a>
		</div>
	</Card>
</div>
