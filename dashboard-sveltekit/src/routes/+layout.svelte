<script lang="ts">
	import '../app.css';
	import { onMount } from 'svelte';
	import { setInitialLocale, waitLocale } from '$lib/i18n';
	import { languageStore } from '$lib/stores/language.svelte';

	// import { authStore } from "$lib/stores/auth.svelte";

	interface Props {
		children: any;
		data: { user?: any; initialLocale?: string };
	}

	const { children, data }: Props = $props();
	let isLoading = $state(true);

	onMount(async () => {
		// ตั้งค่าภาษาเริ่มต้นจาก server ถ้ามี
		if (data?.initialLocale) {
			setInitialLocale(data.initialLocale);
			languageStore.setLanguage(data.initialLocale as any, false);
		}

		// รอให้ i18n โหลดเสร็จ
		await waitLocale();

		// ตั้งค่า user จาก SSR ถ้ามี
		// if (data?.user) {
		// 	authStore.setUserFromSSR(data.user);
		// }

		isLoading = false;
	});
</script>

{#if isLoading}
	<div class="min-h-screen flex items-center justify-center">
		<div class="loading loading-spinner loading-lg"></div>
	</div>
{:else}
	{@render children()}
{/if}
