<script lang="ts">
	import Icon from '@iconify/svelte';
	import { t } from 'svelte-i18n';
	import { enhance } from '$app/forms';
	import { goto } from '$app/navigation';
	import SEO from '$lib/components/layout/SEO.svelte';
	import { themeService } from '$lib/services/theme';
	import { authStore } from '$lib/stores/auth.svelte';
	import { siteStore } from '$lib/stores/site.svelte';
	import type { ThemeSettings } from '$lib/types';

	const { data, form } = $props<{
		data: { theme: ThemeSettings; isNew: boolean };
		form?: any;
	}>();

	const user = $derived(data?.user || authStore.user);
	const error = $derived(data?.error);
	const site = $derived(data?.site);

	// รอให้ authStore initialized
	const isReady = $derived(authStore.isInitialized);

	// Theme settings state
	let themeSettings = $state(
		data?.theme || {
			siteId: '',
			layout: 'grid',
			headerStyle: 'centered',
			footerStyle: 'simple',
			showSearch: true,
			showLanguageSelector: true,
			showThemeToggle: true,
			mobileMenuStyle: 'slide',
			desktopMenuStyle: 'horizontal',
			primaryColor: '#3b82f6',
			secondaryColor: '#6b7280',
			backgroundColor: '#ffffff',
			textColor: '#1f2937',
			accentColor: '#f59e0b',
			fontFamily: 'Inter',
			fontSize: '16px',
			lineHeight: '1.6',
			fontWeight: '400',
			containerPadding: '1rem',
			sectionSpacing: '2rem',
			elementSpacing: '1rem',
		}
	);
	let isNew = $state(data?.isNew || true);
	let isLoading = $state(false);
	let message = $state('');
	let messageType = $state<'success' | 'error' | ''>('');

	// CSS Preview
	const cssPreview = $derived(themeService.generateCSS(themeSettings));

	// Form actions
	function handleSaveTheme() {
		isLoading = true;
		message = '';
		messageType = '';
	}

	function handleResetTheme() {
		isLoading = true;
		message = '';
		messageType = '';
	}

	function handleDeleteTheme() {
		if (confirm('คุณแน่ใจหรือไม่ที่จะลบการตั้งค่าธีมทั้งหมด?')) {
			isLoading = true;
			message = '';
			messageType = '';
		}
	}

	// Handle form result
	$effect(() => {
		if (form) {
			isLoading = false;
			if (form.success) {
				message = form.message || 'บันทึกการตั้งค่าเรียบร้อย';
				messageType = 'success';
				// อัปเดต theme settings
				if (form.theme) {
					themeSettings = form.theme;
					isNew = false;
				}
			} else {
				message = form.error || 'เกิดข้อผิดพลาด';
				messageType = 'error';
			}
		}
	});

	// Copy CSS to clipboard
	async function copyCSS() {
		try {
			await navigator.clipboard.writeText(cssPreview);
			message = 'คัดลอก CSS เรียบร้อย';
			messageType = 'success';
		} catch (err) {
			message = 'ไม่สามารถคัดลอกได้';
			messageType = 'error';
		}
	}

	// Download CSS file
	function downloadCSS() {
		const blob = new Blob([cssPreview], { type: 'text/css' });
		const url = URL.createObjectURL(blob);
		const a = document.createElement('a');
		a.href = url;
		a.download = `theme-${site?.id || 'default'}.css`;
		document.body.appendChild(a);
		a.click();
		document.body.removeChild(a);
		URL.revokeObjectURL(url);
	}

	// Preview theme
	function previewTheme() {
		// สร้าง style tag และเพิ่ม CSS
		const styleId = 'theme-preview';
		let styleTag = document.getElementById(styleId) as HTMLStyleElement;

		if (!styleTag) {
			styleTag = document.createElement('style');
			styleTag.id = styleId;
			document.head.appendChild(styleTag);
		}

		styleTag.textContent = cssPreview;

		message = 'แสดงตัวอย่างธีมแล้ว (CSS ถูกเพิ่มเข้าไปในหน้าเว็บ)';
		messageType = 'success';
	}
</script>

<SEO
	title="Themes - จัดการธีม"
	description="จัดการธีม การออกแบบ และสไตล์ของเว็บไซต์"
	keywords="themes, ธีม, การออกแบบ, สไตล์"
	url="/dashboard/themes"
	noindex={true}
/>

{#if !isReady}
	<div class="flex items-center justify-center min-h-screen">
		<div class="loading loading-spinner loading-lg"></div>
	</div>
{:else}
	<div class="space-y-6 sm:space-y-6">
		<!-- Header -->
		<div class="flex justify-between items-center">
			<div>
				<h1 class="text-3xl font-bold text-base-content">
					<Icon icon="mdi:palette" class="w-8 h-8 inline mr-2" />
					จัดการธีม
				</h1>
				<p class="text-base-content/60 mt-1">ปรับแต่งการออกแบบและสไตล์ของเว็บไซต์</p>
			</div>
			<div class="flex gap-2">
				<form method="POST" action="?/resetTheme" use:enhance={handleResetTheme} class="inline">
					<button type="submit" class="btn btn-outline" disabled={isLoading}>
						<Icon icon="mdi:refresh" class="w-4 h-4" />
						รีเซ็ต
					</button>
				</form>
				<button class="btn btn-primary" onclick={previewTheme} disabled={isLoading}>
					<Icon icon="mdi:eye" class="w-4 h-4" />
					ดูตัวอย่าง
				</button>
			</div>
		</div>

		<!-- Message Display -->
		{#if message}
			<div class="alert alert-{messageType === 'success' ? 'success' : 'error'}">
				<Icon
					icon={messageType === 'success' ? 'mdi:check-circle' : 'mdi:alert-circle'}
					class="w-5 h-5"
				/>
				<span>{message}</span>
			</div>
		{/if}

		<!-- Theme Settings Form -->
		<form method="POST" action="?/saveTheme" use:enhance={handleSaveTheme}>
			<input type="hidden" name="themeId" value={themeSettings.id || ''} />

			<!-- Layout Settings -->
			<div class="card bg-base-100 shadow-lg">
				<div class="card-body">
					<h3 class="card-title">
						<Icon icon="mdi:view-grid" class="w-5 h-5" />
						การจัดวาง
					</h3>

					<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
						<div class="form-control">
							<label class="label" for="layout">
								<span class="label-text">รูปแบบการแสดงผล</span>
							</label>
							<select
								id="layout"
								name="layout"
								class="select select-bordered"
								bind:value={themeSettings.layout}
							>
								<option value="grid">Grid</option>
								<option value="list">List</option>
								<option value="masonry">Masonry</option>
							</select>
						</div>

						<div class="form-control">
							<label class="label" for="headerStyle">
								<span class="label-text">สไตล์ Header</span>
							</label>
							<select
								id="headerStyle"
								name="headerStyle"
								class="select select-bordered"
								bind:value={themeSettings.headerStyle}
							>
								<option value="centered">Centered</option>
								<option value="left">Left</option>
								<option value="minimal">Minimal</option>
							</select>
						</div>

						<div class="form-control">
							<label class="label" for="footerStyle">
								<span class="label-text">สไตล์ Footer</span>
							</label>
							<select
								id="footerStyle"
								name="footerStyle"
								class="select select-bordered"
								bind:value={themeSettings.footerStyle}
							>
								<option value="simple">Simple</option>
								<option value="detailed">Detailed</option>
								<option value="minimal">Minimal</option>
							</select>
						</div>

						<div class="form-control">
							<label class="label" for="mobileMenuStyle">
								<span class="label-text">เมนูมือถือ</span>
							</label>
							<select
								id="mobileMenuStyle"
								name="mobileMenuStyle"
								class="select select-bordered"
								bind:value={themeSettings.mobileMenuStyle}
							>
								<option value="slide">Slide</option>
								<option value="overlay">Overlay</option>
								<option value="dropdown">Dropdown</option>
							</select>
						</div>
					</div>

					<div class="space-y-4 mt-4">
						<div class="form-control">
							<label class="label cursor-pointer" for="showSearch">
								<span class="label-text">แสดงช่องค้นหา</span>
								<input
									id="showSearch"
									name="showSearch"
									type="checkbox"
									class="toggle toggle-primary"
									bind:checked={themeSettings.showSearch}
								/>
							</label>
						</div>

						<div class="form-control">
							<label class="label cursor-pointer" for="showLanguageSelector">
								<span class="label-text">แสดงตัวเลือกภาษา</span>
								<input
									id="showLanguageSelector"
									name="showLanguageSelector"
									type="checkbox"
									class="toggle toggle-primary"
									bind:checked={themeSettings.showLanguageSelector}
								/>
							</label>
						</div>

						<div class="form-control">
							<label class="label cursor-pointer" for="showThemeToggle">
								<span class="label-text">แสดงปุ่มเปลี่ยนธีม</span>
								<input
									id="showThemeToggle"
									name="showThemeToggle"
									type="checkbox"
									class="toggle toggle-primary"
									bind:checked={themeSettings.showThemeToggle}
								/>
							</label>
						</div>
					</div>

					<div class="card-actions justify-end mt-4">
						<button type="submit" class="btn btn-primary" disabled={isLoading}>
							{#if isLoading}
								<div class="loading loading-spinner loading-sm"></div>
							{:else}
								<Icon icon="mdi:content-save" class="w-4 h-4" />
							{/if}
							บันทึก
						</button>
					</div>
				</div>
			</div>

			<!-- Color Settings -->
			<div class="card bg-base-100 shadow-lg">
				<div class="card-body">
					<h3 class="card-title">
						<Icon icon="mdi:palette-swatch" class="w-5 h-5" />
						สี
					</h3>

					<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
						<div class="form-control">
							<label class="label" for="primaryColor">
								<span class="label-text">สีหลัก</span>
							</label>
							<input
								id="primaryColor"
								name="primaryColor"
								type="color"
								class="input input-bordered h-12"
								bind:value={themeSettings.primaryColor}
							/>
						</div>

						<div class="form-control">
							<label class="label" for="secondaryColor">
								<span class="label-text">สีรอง</span>
							</label>
							<input
								id="secondaryColor"
								name="secondaryColor"
								type="color"
								class="input input-bordered h-12"
								bind:value={themeSettings.secondaryColor}
							/>
						</div>

						<div class="form-control">
							<label class="label" for="backgroundColor">
								<span class="label-text">สีพื้นหลัง</span>
							</label>
							<input
								id="backgroundColor"
								name="backgroundColor"
								type="color"
								class="input input-bordered h-12"
								bind:value={themeSettings.backgroundColor}
							/>
						</div>

						<div class="form-control">
							<label class="label" for="textColor">
								<span class="label-text">สีข้อความ</span>
							</label>
							<input
								id="textColor"
								name="textColor"
								type="color"
								class="input input-bordered h-12"
								bind:value={themeSettings.textColor}
							/>
						</div>

						<div class="form-control">
							<label class="label" for="accentColor">
								<span class="label-text">สีเน้น</span>
							</label>
							<input
								id="accentColor"
								name="accentColor"
								type="color"
								class="input input-bordered h-12"
								bind:value={themeSettings.accentColor}
							/>
						</div>
					</div>
				</div>
			</div>

			<!-- Typography Settings -->
			<div class="card bg-base-100 shadow-lg">
				<div class="card-body">
					<h3 class="card-title">
						<Icon icon="mdi:format-font" class="w-5 h-5" />
						ตัวอักษร
					</h3>

					<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
						<div class="form-control">
							<label class="label" for="fontFamily">
								<span class="label-text">ฟอนต์</span>
							</label>
							<select
								id="fontFamily"
								name="fontFamily"
								class="select select-bordered"
								bind:value={themeSettings.fontFamily}
							>
								<option value="Inter">Inter</option>
								<option value="Roboto">Roboto</option>
								<option value="Open Sans">Open Sans</option>
								<option value="Poppins">Poppins</option>
								<option value="Noto Sans Thai">Noto Sans Thai</option>
							</select>
						</div>

						<div class="form-control">
							<label class="label" for="fontSize">
								<span class="label-text">ขนาดฟอนต์</span>
							</label>
							<select
								id="fontSize"
								name="fontSize"
								class="select select-bordered"
								bind:value={themeSettings.fontSize}
							>
								<option value="14px">เล็ก (14px)</option>
								<option value="16px">ปานกลาง (16px)</option>
								<option value="18px">ใหญ่ (18px)</option>
								<option value="20px">ใหญ่มาก (20px)</option>
							</select>
						</div>

						<div class="form-control">
							<label class="label" for="lineHeight">
								<span class="label-text">ความสูงบรรทัด</span>
							</label>
							<select
								id="lineHeight"
								name="lineHeight"
								class="select select-bordered"
								bind:value={themeSettings.lineHeight}
							>
								<option value="1.4">แน่น (1.4)</option>
								<option value="1.6">ปานกลาง (1.6)</option>
								<option value="1.8">ห่าง (1.8)</option>
							</select>
						</div>

						<div class="form-control">
							<label class="label" for="fontWeight">
								<span class="label-text">น้ำหนักฟอนต์</span>
							</label>
							<select
								id="fontWeight"
								name="fontWeight"
								class="select select-bordered"
								bind:value={themeSettings.fontWeight}
							>
								<option value="300">บาง (300)</option>
								<option value="400">ปกติ (400)</option>
								<option value="500">ปานกลาง (500)</option>
								<option value="600">หนา (600)</option>
							</select>
						</div>
					</div>
				</div>
			</div>

			<!-- Spacing Settings -->
			<div class="card bg-base-100 shadow-lg">
				<div class="card-body">
					<h3 class="card-title">
						<Icon icon="mdi:arrow-expand-all" class="w-5 h-5" />
						ระยะห่าง
					</h3>

					<div class="grid grid-cols-1 md:grid-cols-3 gap-6">
						<div class="form-control">
							<label class="label" for="containerPadding">
								<span class="label-text">ระยะห่าง Container</span>
							</label>
							<select
								id="containerPadding"
								name="containerPadding"
								class="select select-bordered"
								bind:value={themeSettings.containerPadding}
							>
								<option value="0.5rem">เล็ก (0.5rem)</option>
								<option value="1rem">ปานกลาง (1rem)</option>
								<option value="1.5rem">ใหญ่ (1.5rem)</option>
								<option value="2rem">ใหญ่มาก (2rem)</option>
							</select>
						</div>

						<div class="form-control">
							<label class="label" for="sectionSpacing">
								<span class="label-text">ระยะห่าง Section</span>
							</label>
							<select
								id="sectionSpacing"
								name="sectionSpacing"
								class="select select-bordered"
								bind:value={themeSettings.sectionSpacing}
							>
								<option value="1rem">เล็ก (1rem)</option>
								<option value="2rem">ปานกลาง (2rem)</option>
								<option value="3rem">ใหญ่ (3rem)</option>
								<option value="4rem">ใหญ่มาก (4rem)</option>
							</select>
						</div>

						<div class="form-control">
							<label class="label" for="elementSpacing">
								<span class="label-text">ระยะห่าง Element</span>
							</label>
							<select
								id="elementSpacing"
								name="elementSpacing"
								class="select select-bordered"
								bind:value={themeSettings.elementSpacing}
							>
								<option value="0.5rem">เล็ก (0.5rem)</option>
								<option value="1rem">ปานกลาง (1rem)</option>
								<option value="1.5rem">ใหญ่ (1.5rem)</option>
								<option value="2rem">ใหญ่มาก (2rem)</option>
							</select>
						</div>
					</div>
				</div>
			</div>
		</form>

		<!-- CSS Preview -->
		<div class="card bg-base-100 shadow-lg">
			<div class="card-body">
				<h3 class="card-title">
					<Icon icon="mdi:code-tags" class="w-5 h-5" />
					CSS Preview
				</h3>

				<div class="form-control">
					<label class="label" for="css-preview">
						<span class="label-text">CSS ที่จะใช้</span>
					</label>
					<textarea
						id="css-preview"
						class="textarea textarea-bordered font-mono text-sm"
						value={cssPreview}
						rows="15"
						readonly
					></textarea>
				</div>

				<div class="card-actions justify-end mt-4">
					<button class="btn btn-outline" onclick={copyCSS}>
						<Icon icon="mdi:content-copy" class="w-4 h-4" />
						คัดลอก
					</button>
					<button class="btn btn-primary" onclick={downloadCSS}>
						<Icon icon="mdi:download" class="w-4 h-4" />
						ดาวน์โหลด
					</button>
				</div>
			</div>
		</div>

		<!-- Delete Theme -->
		{#if !isNew}
			<div class="card bg-base-100 shadow-lg border-error/20">
				<div class="card-body">
					<h3 class="card-title text-error">
						<Icon icon="mdi:delete" class="w-5 h-5" />
						ลบธีม
					</h3>
					<p class="text-base-content/70">
						การลบธีมจะทำให้การตั้งค่าทั้งหมดหายไป และไม่สามารถกู้คืนได้
					</p>
					<div class="card-actions justify-end">
						<form
							method="POST"
							action="?/deleteTheme"
							use:enhance={handleDeleteTheme}
							class="inline"
						>
							<button type="submit" class="btn btn-error" disabled={isLoading}>
								<Icon icon="mdi:delete" class="w-4 h-4" />
								ลบธีม
							</button>
						</form>
					</div>
				</div>
			</div>
		{/if}
	</div>
{/if}
