<script lang="ts">
	import Icon from '@iconify/svelte';
	import { t } from 'svelte-i18n';
	import { enhance } from '$app/forms';
	import SEO from '$lib/components/layout/SEO.svelte';
	import Chart from '$lib/components/ui/Chart.svelte';
	import { authStore } from '$lib/stores/auth.svelte';

	const { data, form } = $props<{
		data: {
			analytics?: {
				sales?: any;
				products?: any;
				customers?: any;
				orders?: any;
				revenue?: any;
				topProducts?: any[];
				topCategories?: any[];
				recentOrders?: any[];
			};
			errors?: {
				analytics?: string | null;
			};
		};
		form?: any;
	}>();

	let analytics = $derived(data?.analytics || {});
	const errors = $derived(data?.errors || {});

	// รอให้ authStore initialized
	const isReady = $derived(authStore.isInitialized);

	// Form action result
	const formResult = $derived(form);

	$effect(() => {
		if (formResult?.success && formResult.data) {
			analytics = formResult.data;
		}
	});

	const chartOptions = {
		plugins: {
			legend: {
				display: true,
				position: 'top' as const,
			},
		},
		scales: {
			y: {
				beginAtZero: true,
			},
		},
	};

	const pieOptions = {
		plugins: {
			legend: {
				display: true,
				position: 'bottom' as const,
			},
		},
	};

	// สถิติยอดขาย
	const salesStats = $derived([
		{
			title: 'ยอดขายวันนี้',
			value: analytics?.sales?.today?.toString() || '0',
			change: analytics?.sales?.todayChange || '+0%',
			changeType: analytics?.sales?.todayChange?.startsWith('+') ? 'positive' : 'negative',
			icon: 'mdi:currency-thb',
			color: 'bg-green-500',
		},
		{
			title: 'ยอดขายเดือนนี้',
			value: analytics?.sales?.month?.toString() || '0',
			change: analytics?.sales?.monthChange || '+0%',
			changeType: analytics?.sales?.monthChange?.startsWith('+') ? 'positive' : 'negative',
			icon: 'mdi:chart-line',
			color: 'bg-blue-500',
		},
		{
			title: 'ออเดอร์วันนี้',
			value: analytics?.orders?.today?.toString() || '0',
			change: analytics?.orders?.todayChange || '+0%',
			changeType: analytics?.orders?.todayChange?.startsWith('+') ? 'positive' : 'negative',
			icon: 'mdi:shopping',
			color: 'bg-purple-500',
		},
		{
			title: 'ลูกค้าใหม่',
			value: analytics?.customers?.new?.toString() || '0',
			change: analytics?.customers?.newChange || '+0%',
			changeType: analytics?.customers?.newChange?.startsWith('+') ? 'positive' : 'negative',
			icon: 'mdi:account-plus',
			color: 'bg-orange-500',
		},
	]);

	// สถิติสินค้า
	const productStats = $derived([
		{
			title: 'สินค้าทั้งหมด',
			value: analytics?.products?.total?.toString() || '0',
			change: '+0%',
			changeType: 'positive',
			icon: 'mdi:package',
			color: 'bg-blue-500',
		},
		{
			title: 'สินค้าที่เปิดขาย',
			value: analytics?.products?.active?.toString() || '0',
			change: '+0%',
			changeType: 'positive',
			icon: 'mdi:package-variant-plus',
			color: 'bg-green-500',
		},
		{
			title: 'สินค้าหมดสต็อก',
			value: analytics?.products?.outOfStock?.toString() || '0',
			change: '+0%',
			changeType: 'warning',
			icon: 'mdi:star',
			color: 'bg-yellow-500',
		},
		{
			title: 'สินค้าสต็อกต่ำ',
			value: analytics?.products?.lowStock?.toString() || '0',
			change: '-0%',
			changeType: 'negative',
			icon: 'mdi:package-variant-closed',
			color: 'bg-red-500',
		},
	]);

	// ข้อมูลกราฟยอดขาย - ใช้ข้อมูลจริงจาก backend
	const salesChartData = $derived({
		labels: analytics?.sales?.chartLabels || ['ม.ค.', 'ก.พ.', 'มี.ค.', 'เม.ย.', 'พ.ค.', 'มิ.ย.'],
		datasets: [
			{
				label: 'ยอดขาย',
				data: analytics?.sales?.chartData || [0, 0, 0, 0, 0, 0],
				borderColor: 'rgb(59, 130, 246)',
				backgroundColor: 'rgba(59, 130, 246, 0.1)',
				tension: 0.4,
			},
		],
	});

	// ข้อมูลกราฟหมวดหมู่ - ใช้ข้อมูลจริงจาก backend
	const categoryChartData = $derived({
		labels: analytics?.topCategories?.map((cat: any) => cat.name) || [],
		datasets: [
			{
				data: analytics?.topCategories?.map((cat: any) => cat.sales) || [],
				backgroundColor: [
					'rgb(59, 130, 246)',
					'rgb(34, 197, 94)',
					'rgb(245, 158, 11)',
					'rgb(239, 68, 68)',
					'rgb(156, 163, 175)',
					'rgb(168, 85, 247)',
					'rgb(236, 72, 153)',
					'rgb(14, 165, 233)',
				],
			},
		],
	});

	function formatPrice(price: number) {
		if (!price || isNaN(price)) {
			return new Intl.NumberFormat('th-TH', {
				style: 'currency',
				currency: 'THB',
			}).format(0);
		}
		return new Intl.NumberFormat('th-TH', {
			style: 'currency',
			currency: 'THB',
		}).format(price);
	}

	function formatDate(dateString: string) {
		if (!dateString) return '-';
		return new Date(dateString).toLocaleDateString('th-TH');
	}
</script>

<SEO
	title="Analytics - วิเคราะห์ข้อมูล"
	description="วิเคราะห์ข้อมูลยอดขาย สินค้า และลูกค้า"
	keywords="analytics, วิเคราะห์, ยอดขาย, สถิติ, dashboard"
	url="/dashboard/analytics"
	noindex={true}
/>

{#if !isReady}
	<div class="flex items-center justify-center min-h-screen">
		<div class="loading loading-spinner loading-lg"></div>
	</div>
{:else}
	<div class="space-y-6 sm:space-y-6">
		<!-- Form Action Messages -->
		{#if formResult?.success}
			<div class="alert alert-success">
				<Icon icon="mdi:check-circle" class="w-5 h-5" />
				<span>{formResult.message}</span>
				{#if formResult.downloadUrl}
					<a href={formResult.downloadUrl} class="btn btn-sm btn-success ml-2" target="_blank"
						>ดาวน์โหลด</a
					>
				{/if}
			</div>
		{:else if formResult?.error}
			<div class="alert alert-error">
				<Icon icon="mdi:alert-circle" class="w-5 h-5" />
				<span>{formResult.error}</span>
			</div>
		{/if}

		<!-- API Error Messages -->
		{#if errors.analytics}
			<div class="alert alert-warning">
				<Icon icon="mdi:alert" class="w-5 h-5" />
				<span>ไม่สามารถโหลดข้อมูลวิเคราะห์ได้: {errors.analytics}</span>
			</div>
		{/if}

		<!-- Header -->
		<div class="flex justify-between items-center">
			<div>
				<h1 class="text-3xl font-bold text-base-content">
					<Icon icon="mdi:chart-line" class="w-8 h-8 inline mr-2" />
					วิเคราะห์ข้อมูล
				</h1>
				<p class="text-base-content/60 mt-1">ดูสถิติยอดขาย สินค้า และลูกค้า</p>
			</div>
			<div class="flex gap-2">
				<form method="POST" action="?/exportReport" use:enhance>
					<div class="flex gap-2 items-center">
						<select name="format" class="select select-bordered select-sm">
							<option value="csv">CSV</option>
							<option value="excel">Excel</option>
							<option value="pdf">PDF</option>
						</select>
						<button class="btn btn-outline" type="submit">
							<Icon icon="mdi:download" class="w-5 h-5" />
							ส่งออกรายงาน
						</button>
					</div>
				</form>
				<form method="POST" action="?/refreshData" use:enhance>
					<button class="btn btn-outline" type="submit">
						<Icon icon="mdi:refresh" class="w-5 h-5" />
						รีเฟรชข้อมูล
					</button>
				</form>
			</div>
		</div>

		<!-- Sales Stats Cards -->
		<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
			{#each salesStats as stat}
				<div class="card bg-base-100 shadow-lg">
					<div class="card-body">
						<div class="flex items-center justify-between">
							<div>
								<p class="text-sm text-base-content/60">
									{stat.title}
								</p>
								<p class="text-2xl font-bold text-base-content">
									{stat.value}
								</p>
								<p
									class="text-sm {stat.changeType === 'positive'
										? 'text-green-500'
										: stat.changeType === 'negative'
											? 'text-red-500'
											: 'text-yellow-500'}"
								>
									{stat.change} จากช่วงก่อนหน้า
								</p>
							</div>
							<div class="w-12 h-12 rounded-full {stat.color} flex items-center justify-center">
								<Icon icon={stat.icon} class="w-6 h-6 text-white" />
							</div>
						</div>
					</div>
				</div>
			{/each}
		</div>

		<!-- Product Stats Cards -->
		<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
			{#each productStats as stat}
				<div class="card bg-base-100 shadow-lg">
					<div class="card-body">
						<div class="flex items-center justify-between">
							<div>
								<p class="text-sm text-base-content/60">
									{stat.title}
								</p>
								<p class="text-2xl font-bold text-base-content">
									{stat.value}
								</p>
								<p
									class="text-sm {stat.changeType === 'positive'
										? 'text-green-500'
										: stat.changeType === 'negative'
											? 'text-red-500'
											: 'text-yellow-500'}"
								>
									{stat.change} จากช่วงก่อนหน้า
								</p>
							</div>
							<div class="w-12 h-12 rounded-full {stat.color} flex items-center justify-center">
								<Icon icon={stat.icon} class="w-6 h-6 text-white" />
							</div>
						</div>
					</div>
				</div>
			{/each}
		</div>

		<!-- Charts Row -->
		<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
			<!-- Sales Chart -->
			<div class="card bg-base-100 shadow-lg">
				<div class="card-body">
					<h3 class="card-title">
						<Icon icon="mdi:chart-line" class="w-5 h-5" />
						ยอดขายรายเดือน
					</h3>
					<Chart data={salesChartData} options={chartOptions} height="300px" />
				</div>
			</div>

			<!-- Category Performance -->
			<div class="card bg-base-100 shadow-lg">
				<div class="card-body">
					<h3 class="card-title">
						<Icon icon="mdi:pie-chart" class="w-5 h-5" />
						ยอดขายตามหมวดหมู่
					</h3>
					<Chart type="doughnut" data={categoryChartData} options={pieOptions} height="300px" />
				</div>
			</div>
		</div>

		<!-- Top Products & Recent Orders -->
		<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
			<!-- Top Products -->
			<div class="card bg-base-100 shadow-lg">
				<div class="card-body">
					<h3 class="card-title">
						<Icon icon="mdi:star" class="w-5 h-5" />
						สินค้าขายดี
					</h3>
					<div class="overflow-x-auto">
						<table class="table table-zebra">
							<thead>
								<tr>
									<th>สินค้า</th>
									<th>ยอดขาย</th>
									<th>รายได้</th>
								</tr>
							</thead>
							<tbody>
								{#if analytics?.topProducts && analytics.topProducts.length > 0}
									{#each analytics.topProducts as product}
										<tr>
											<td>
												<div class="flex items-center gap-3">
													<div class="avatar placeholder">
														<div class="bg-neutral text-neutral-content rounded-full w-8">
															<span class="text-xs">
																{product.name?.charAt(0)?.toUpperCase() || 'P'}
															</span>
														</div>
													</div>
													<div>
														<div class="font-bold">
															{product.name || 'ไม่มีชื่อ'}
														</div>
														<div class="text-sm text-base-content/60">
															SKU: {product.sku || '-'}
														</div>
													</div>
												</div>
											</td>
											<td>{product.sales || 0}</td>
											<td>{formatPrice(product.revenue || 0)}</td>
										</tr>
									{/each}
								{:else}
									<tr>
										<td colspan="3" class="text-center py-4">
											<div class="flex flex-col items-center gap-2">
												<Icon
													icon="mdi:package-variant-closed"
													class="w-8 h-8 text-base-content/40"
												/>
												<p class="text-base-content/60">ยังไม่มีข้อมูลสินค้าขายดี</p>
											</div>
										</td>
									</tr>
								{/if}
							</tbody>
						</table>
					</div>
				</div>
			</div>

			<!-- Recent Orders -->
			<div class="card bg-base-100 shadow-lg">
				<div class="card-body">
					<h3 class="card-title">
						<Icon icon="mdi:shopping" class="w-5 h-5" />
						ออเดอร์ล่าสุด
					</h3>
					<div class="overflow-x-auto">
						<table class="table table-zebra">
							<thead>
								<tr>
									<th>ออเดอร์</th>
									<th>ลูกค้า</th>
									<th>ยอดรวม</th>
									<th>สถานะ</th>
								</tr>
							</thead>
							<tbody>
								{#if analytics?.recentOrders && analytics.recentOrders.length > 0}
									{#each analytics.recentOrders as order}
										<tr>
											<td>
												<div>
													<div class="font-bold">
														#{order.orderNumber || order._id}
													</div>
													<div class="text-sm text-base-content/60">
														{formatDate(order.createdAt)}
													</div>
												</div>
											</td>
											<td>{order.customerName || 'ไม่ระบุ'}</td>
											<td>{formatPrice(order.total || 0)}</td>
											<td>
												<span class="badge badge-success">
													{order.status || 'สำเร็จ'}
												</span>
											</td>
										</tr>
									{/each}
								{:else}
									<tr>
										<td colspan="4" class="text-center py-4">
											<div class="flex flex-col items-center gap-2">
												<Icon icon="mdi:shopping-outline" class="w-8 h-8 text-base-content/40" />
												<p class="text-base-content/60">ยังไม่มีออเดอร์</p>
											</div>
										</td>
									</tr>
								{/if}
							</tbody>
						</table>
					</div>
				</div>
			</div>
		</div>
	</div>
{/if}
