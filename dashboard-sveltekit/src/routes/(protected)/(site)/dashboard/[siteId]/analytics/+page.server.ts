import { analyticsService } from '$lib/services/analytics';
import { customerService } from '$lib/services/customer';
import { orderService } from '$lib/services/order';
import { productService } from '$lib/services/product';
import { error, fail } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ params, locals, url, setHeaders }) => {
  try {
    const siteId = params.siteId;
    if (!siteId) {
      throw error(400, 'Site ID is required');
    }

    // Get query parameters for date range
    const period = url.searchParams.get('period') || 'month';
    const startDate = url.searchParams.get('startDate');
    const endDate = url.searchParams.get('endDate');

    // Fetch all analytics data in parallel with error handling
    let productStatsResponse: any,
      orderStatsResponse: any,
      customerStatsResponse: any,
      salesAnalyticsResponse: any;

    try {
      productStatsResponse = await productService.getProductStats(siteId, locals.token!);
    }
    catch (err) {
      console.error('Error fetching product stats:', err);
      productStatsResponse = {
        success: false,
        error: 'Failed to fetch product stats',
        data: {
          totalProducts: 0,
          activeProducts: 0,
          outOfStockProducts: 0,
          lowStockProducts: 0,
        },
      };
    }

    try {
      orderStatsResponse = await orderService.getOrderStats(siteId, locals.token!);
    }
    catch (err) {
      console.error('Error fetching order stats:', err);
      orderStatsResponse = {
        success: false,
        error: 'Failed to fetch order stats',
        data: {
          today: 0,
          month: 0,
          total: 0,
          todayChange: '+0%',
          monthChange: '+0%',
        },
      };
    }

    try {
      customerStatsResponse = await customerService.getCustomerStats(siteId, locals.token!);
    }
    catch (err) {
      console.error('Error fetching customer stats:', err);
      customerStatsResponse = {
        success: false,
        error: 'Failed to fetch customer stats',
        data: {
          new: 0,
          total: 0,
          active: 0,
          newChange: '+0%',
        },
      };
    }

    try {
      salesAnalyticsResponse = await analyticsService.getSalesAnalytics(siteId, locals.token!, {
        period: period as 'month' | 'today' | 'week' | 'year' | 'custom',
        startDate: startDate || undefined,
        endDate: endDate || undefined,
      });
    }
    catch (err) {
      console.error('Error fetching sales analytics:', err);
      salesAnalyticsResponse = {
        success: false,
        error: 'Failed to fetch sales analytics',
        data: {
          today: 0,
          month: 0,
          total: 0,
          todayChange: '+0%',
          monthChange: '+0%',
          chartData: [0, 0, 0, 0, 0, 0],
        },
      };
    }

    // Set cache headers
    setHeaders({
      'Cache-Control': 'public, max-age=300', // 5 minutes
    });

    return {
      analytics: {
        sales: salesAnalyticsResponse.success
          ? salesAnalyticsResponse.data
          : {
            today: 0,
            month: 0,
            total: 0,
            todayChange: '+0%',
            monthChange: '+0%',
            chartData: [0, 0, 0, 0, 0, 0],
          },
        products: productStatsResponse.success
          ? productStatsResponse.data
          : {
            total: 0,
            active: 0,
            outOfStock: 0,
            lowStock: 0,
          },
        orders: orderStatsResponse.success
          ? orderStatsResponse.data
          : {
            today: 0,
            month: 0,
            total: 0,
            todayChange: '+0%',
            monthChange: '+0%',
          },
        customers: customerStatsResponse.success
          ? customerStatsResponse.data
          : {
            new: 0,
            total: 0,
            active: 0,
            newChange: '+0%',
          },
        topProducts: salesAnalyticsResponse.success
          ? salesAnalyticsResponse.data?.topProducts || []
          : [],
        topCategories: salesAnalyticsResponse.success
          ? salesAnalyticsResponse.data?.topCategories || []
          : [],
        recentOrders: orderStatsResponse.success ? orderStatsResponse.data?.recentOrders || [] : [],
      },
      filters: {
        period,
        startDate,
        endDate,
      },
      errors: {
        analytics: !salesAnalyticsResponse.success ? salesAnalyticsResponse.error : null,
        products: !productStatsResponse.success ? productStatsResponse.error : null,
        orders: !orderStatsResponse.success ? orderStatsResponse.error : null,
        customers: !customerStatsResponse.success ? customerStatsResponse.error : null,
      },
    };
  }
  catch (err) {
    console.error('Error loading analytics page:', err);
    throw error(500, 'Failed to load analytics');
  }
};

export const actions: Actions = {
  /**
   * ✅ Export Analytics Report - Hybrid Approach
   * Route API + Service Pattern
   */
  exportReport: async ({ params, locals, request }) => {
    try {
      // Auth check already done in layout
      const { siteId } = params;
      const formData = await request.formData();

      // Extract form data
      const format = (formData.get('format') as string) || 'excel';
      const period = (formData.get('period') as string) || 'month';
      const startDate = formData.get('startDate')?.toString() || undefined;
      const endDate = formData.get('endDate')?.toString() || undefined;

      // Basic validation at route level
      if (!siteId?.trim()) {
        return fail(400, {
          message: 'ไม่พบ Site ID',
          type: 'export',
        });
      }

      // Validate format
      const validFormats = ['excel', 'csv', 'pdf'];
      if (!validFormats.includes(format)) {
        return fail(400, {
          message: 'รูปแบบไฟล์ไม่ถูกต้อง',
          type: 'export',
        });
      }

      // Validate period
      const validPeriods = ['today', 'week', 'month', 'year', 'custom'];
      if (!validPeriods.includes(period)) {
        return fail(400, {
          message: 'ช่วงเวลาไม่ถูกต้อง',
          type: 'export',
        });
      }

      // Validate custom date range
      if (period === 'custom') {
        if (!startDate || !endDate) {
          return fail(400, {
            message: 'กรุณาระบุวันที่เริ่มต้นและสิ้นสุด',
            type: 'export',
          });
        }

        const start = new Date(startDate);
        const end = new Date(endDate);
        if (start > end) {
          return fail(400, {
            message: 'วันที่เริ่มต้นต้องน้อยกว่าวันที่สิ้นสุด',
            type: 'export',
          });
        }
      }

      const config = {
        period,
        startDate,
        endDate,
        format: format as 'excel' | 'csv' | 'pdf',
      };

      // Call service for business logic + backend API
      const result = await analyticsService.exportAnalytics(siteId, locals.token!, config);

      if (!result.success) {
        return fail(400, {
          message: result.error || 'เกิดข้อผิดพลาดในการส่งออกรายงาน',
          type: 'export',
        });
      }

      return {
        success: true,
        data: result.data,
        message: 'ส่งออกรายงานสำเร็จ',
        type: 'export',
      };
    }
    catch (error) {
      console.error('Export report error:', error);
      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการส่งออกรายงาน',
        type: 'export',
      });
    }
  },

  /**
   * ✅ Refresh Analytics Data - Hybrid Approach
   * Route API + Service Pattern
   */
  refreshData: async ({ params, locals, request }) => {
    try {
      // Auth check already done in layout
      const { siteId } = params;
      const formData = await request.formData();

      // Extract form data
      const period = (formData.get('period') as string) || 'month';
      const startDate = formData.get('startDate') as string;
      const endDate = formData.get('endDate') as string;

      // Basic validation at route level
      if (!siteId?.trim()) {
        return fail(400, {
          message: 'ไม่พบ Site ID',
          type: 'refresh',
        });
      }

      // Validate period
      const validPeriods = ['today', 'week', 'month', 'year', 'custom'];
      if (!validPeriods.includes(period)) {
        return fail(400, {
          message: 'ช่วงเวลาไม่ถูกต้อง',
          type: 'refresh',
        });
      }

      // Validate custom date range
      if (period === 'custom') {
        if (!startDate || !endDate) {
          return fail(400, {
            message: 'กรุณาระบุวันที่เริ่มต้นและสิ้นสุด',
            type: 'refresh',
          });
        }

        const start = new Date(startDate);
        const end = new Date(endDate);
        if (start > end) {
          return fail(400, {
            message: 'วันที่เริ่มต้นต้องน้อยกว่าวันที่สิ้นสุด',
            type: 'refresh',
          });
        }
      }

      // Call services for business logic + backend API (parallel execution)
      const [productStats, orderStats, customerStats, salesAnalytics] = await Promise.all([
        productService.getProductStats(siteId, locals.token!),
        orderService.getOrderStats(siteId, locals.token!),
        customerService.getCustomerStats(siteId, locals.token!),
        analyticsService.getSalesAnalytics(siteId, locals.token!, {
          period: period as 'month' | 'today' | 'week' | 'year' | 'custom',
          startDate: startDate || undefined,
          endDate: endDate || undefined,
        }),
      ]);

      // Check if any service failed
      const errors = [];
      if (!productStats.success) errors.push(`Products: ${productStats.error}`);
      if (!orderStats.success) errors.push(`Orders: ${orderStats.error}`);
      if (!customerStats.success) errors.push(`Customers: ${customerStats.error}`);
      if (!salesAnalytics.success) errors.push(`Sales: ${salesAnalytics.error}`);

      if (errors.length > 0) {
        return fail(400, {
          message: `เกิดข้อผิดพลาดในการรีเฟรชข้อมูล: ${errors.join(', ')}`,
          type: 'refresh',
        });
      }

      return {
        success: true,
        data: {
          products: productStats.data,
          orders: orderStats.data,
          customers: customerStats.data,
          sales: salesAnalytics.data,
        },
        message: 'รีเฟรชข้อมูลสำเร็จ',
        type: 'refresh',
      };
    }
    catch (error) {
      console.error('Refresh data error:', error);
      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการรีเฟรชข้อมูล',
        type: 'refresh',
      });
    }
  },
};
