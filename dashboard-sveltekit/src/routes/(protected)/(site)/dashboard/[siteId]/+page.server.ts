import { dashboardService } from '$lib/services/dashboard';
// import { requireAuth } from '$lib/utils/auth';
import type { PageServerLoad } from './$types';

export const prerender = false;

export const load: PageServerLoad = async ({ locals, params, url }) => {
  const { siteId } = params;
  // ตรวจสอบ authentication (ได้จาก parent layout แล้ว)
  // requireAuth(locals);
  try {
    // ดึง query parameters สำหรับ dashboard
    const period = (url.searchParams.get('period') as 'today' | 'week' | 'month' | 'year') || 'month';
    const startDate = url.searchParams.get('startDate') || undefined;
    const endDate = url.searchParams.get('endDate') || undefined;

    // ตรวจสอบ token
    if (!locals.token) {
      console.log('Page Server: No token found, returning default data');
      return {
        siteId,
        dashboardData: {
          stats: {
            totalSales: 0,
            totalOrders: 0,
            totalVisitors: 0,
            totalProducts: 0,
            salesChange: '+0%',
            ordersChange: '+0%',
            visitorsChange: '+0%',
            productsChange: '+0%',
          },
          chartData: {},
          recentActivities: [],
        },
        error: 'ไม่พบ token สำหรับการเข้าถึง',
        period,
        startDate,
        endDate,
      };
    }

    // ใช้ dashboard service เพื่อดึงข้อมูล
    const result = await dashboardService.getDashboardData(siteId, locals.token, {
      period,
      startDate,
      endDate,
    });

    if (!result.success) {
      console.log('Page Server: Dashboard service failed, returning default data');
      return {
        siteId,
        dashboardData: {
          stats: {
            totalSales: 0,
            totalOrders: 0,
            totalVisitors: 0,
            totalProducts: 0,
            salesChange: '+0%',
            ordersChange: '+0%',
            visitorsChange: '+0%',
            productsChange: '+0%',
          },
          chartData: {},
          recentActivities: [],
        },
        error: result.error,
        period,
        startDate,
        endDate,
      };
    }

    return {
      siteId,
      dashboardData: result.data,
      error: null,
      period,
      startDate,
      endDate,
    };
  }
  catch (error) {
    console.error('Page Server: Error in load function:', error);
    return {
      siteId,
      dashboardData: {
        stats: {
          totalSales: 0,
          totalOrders: 0,
          totalVisitors: 0,
          totalProducts: 0,
          salesChange: '+0%',
          ordersChange: '+0%',
          visitorsChange: '+0%',
          productsChange: '+0%',
        },
        chartData: {},
        recentActivities: [],
      },
      error: error instanceof Error ? error.message : String(error),
      period: 'month',
      startDate: undefined,
      endDate: undefined,
    };
  }
};
