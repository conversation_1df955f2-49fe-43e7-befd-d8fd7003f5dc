import { subscriptionService } from '$lib/services/subscription';
import { fail } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ locals, params, url }) => {
  const { siteId } = params;
  const token = locals.token;

  if (!token) {
    return {
      packages: [],
      subscriptions: [],
      notifications: [],
      error: 'ไม่พบ token การยืนยันตัวตน',
    };
  }

  try {
    // ดึงข้อมูลแพ็คเกจ
    const packagesResult = await subscriptionService.getPackages(token);

    // ดึงรายการ subscription ของ site
    const page = url.searchParams.get('page') || '1';
    const limit = url.searchParams.get('limit') || '10';
    const status = url.searchParams.get('status') || '';

    const subscriptionsResult = await subscriptionService.getSiteSubscriptions(
      siteId,
      {
        page,
        limit,
        status,
      },
      token,
    );

    // ดึงการแจ้งเตือน
    const notificationsResult = await subscriptionService.getSiteNotifications(
      siteId,
      {
        page: '1',
        limit: '5',
        unreadOnly: 'true',
      },
      token,
    );

    return {
      packages: packagesResult.success ? packagesResult.data : [],
      subscriptions: subscriptionsResult.success
        ? subscriptionsResult.data?.subscriptions || []
        : [],
      pagination: subscriptionsResult.success ? subscriptionsResult.data?.pagination : null,
      notifications: notificationsResult.success
        ? notificationsResult.data?.notifications || []
        : [],
      error: null,
    };
  }
  catch (error) {
    console.error('Error loading subscriptions:', error);
    return {
      packages: [],
      subscriptions: [],
      notifications: [],
      error: 'เกิดข้อผิดพลาดในการโหลดข้อมูล',
    };
  }
};

export const actions: Actions = {
  /**
   * ✅ Create Subscription - Hybrid Approach
   * Route API + Service Pattern
   */
  create: async ({ request, locals, params }) => {
    try {
      // Auth check already done in layout
      const { siteId } = params;
      const formData = await request.formData();

      // Extract form data
      const packageType = formData.get('packageType') as string;
      const autoRenew = formData.get('autoRenew') === 'true';
      const paymentMethod = formData.get('paymentMethod') as string;
      const discountCode = formData.get('discountCode') as string;

      // Basic validation at route level
      if (!packageType?.trim()) {
        return fail(400, {
          message: 'กรุณาเลือกแพ็คเกจ',
          type: 'create',
        });
      }

      if (!paymentMethod?.trim()) {
        return fail(400, {
          message: 'กรุณาเลือกวิธีการชำระเงิน',
          type: 'create',
        });
      }

      // Validate package type
      const validPackageTypes = ['basic', 'premium', 'enterprise'];
      if (!validPackageTypes.includes(packageType)) {
        return fail(400, {
          message: 'แพ็คเกจที่เลือกไม่ถูกต้อง',
          type: 'create',
        });
      }

      // Call service for business logic + backend API
      const result = await subscriptionService.createSiteSubscription(
        siteId,
        {
          packageType: packageType as any,
          autoRenew,
          paymentMethod,
          discountCode: discountCode || undefined,
        },
        locals.token!,
      );

      if (!result.success) {
        return fail(400, {
          message: result.error || 'เกิดข้อผิดพลาดในการสร้าง subscription',
          type: 'create',
        });
      }

      return {
        success: true,
        data: result.data,
        message: 'สร้าง subscription สำเร็จ',
        type: 'create',
      };
    }
    catch (error) {
      console.error('Create subscription error:', error);
      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการสร้าง subscription',
        type: 'create',
      });
    }
  },

  /**
   * ✅ Renew Subscription - Hybrid Approach
   * Route API + Service Pattern
   */
  renew: async ({ request, locals, params }) => {
    try {
      // Auth check already done in layout
      const { siteId } = params;
      const formData = await request.formData();
      const subscriptionId = formData.get('subscriptionId') as string;

      // Basic validation at route level
      if (!subscriptionId?.trim()) {
        return fail(400, {
          message: 'ไม่พบ subscription ID',
          type: 'renew',
        });
      }

      // Call service for business logic + backend API
      const result = await subscriptionService.renewSubscription(
        siteId,
        subscriptionId,
        locals.token!,
      );

      if (!result.success) {
        return fail(400, {
          message: result.error || 'เกิดข้อผิดพลาดในการต่ออายุ subscription',
          type: 'renew',
        });
      }

      return {
        success: true,
        data: result.data,
        message: 'ต่ออายุ subscription สำเร็จ',
        type: 'renew',
      };
    }
    catch (error) {
      console.error('Renew subscription error:', error);
      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการต่ออายุ subscription',
        type: 'renew',
      });
    }
  },

  /**
   * ✅ Cancel Subscription - Hybrid Approach
   * Route API + Service Pattern
   */
  cancel: async ({ request, locals, params }) => {
    try {
      // Auth check already done in layout
      const { siteId } = params;
      const formData = await request.formData();
      const subscriptionId = formData.get('subscriptionId') as string;

      // Basic validation at route level
      if (!subscriptionId?.trim()) {
        return fail(400, {
          message: 'ไม่พบ subscription ID',
          type: 'cancel',
        });
      }

      // Call service for business logic + backend API
      const result = await subscriptionService.cancelSubscription(
        siteId,
        subscriptionId,
        locals.token!,
      );

      if (!result.success) {
        return fail(400, {
          message: result.error || 'เกิดข้อผิดพลาดในการยกเลิก subscription',
          type: 'cancel',
        });
      }

      return {
        success: true,
        data: result.data,
        message: 'ยกเลิก subscription สำเร็จ',
        type: 'cancel',
      };
    }
    catch (error) {
      console.error('Cancel subscription error:', error);
      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการยกเลิก subscription',
        type: 'cancel',
      });
    }
  },

  /**
   * ✅ Toggle Auto Renew - Hybrid Approach
   * Route API + Service Pattern
   */
  toggleAutoRenew: async ({ request, locals, params }) => {
    try {
      // Auth check already done in layout
      const { siteId } = params;
      const formData = await request.formData();
      const subscriptionId = formData.get('subscriptionId') as string;
      const autoRenew = formData.get('autoRenew') === 'true';

      // Basic validation at route level
      if (!subscriptionId?.trim()) {
        return fail(400, {
          message: 'ไม่พบ subscription ID',
          type: 'toggle',
        });
      }

      // Call service for business logic + backend API
      const result = await subscriptionService.toggleAutoRenew(
        siteId,
        subscriptionId,
        autoRenew,
        locals.token!,
      );

      if (!result.success) {
        return fail(400, {
          message: result.error || 'เกิดข้อผิดพลาดในการเปลี่ยนการตั้งค่า',
          type: 'toggle',
        });
      }

      return {
        success: true,
        data: result.data,
        message: `${autoRenew ? 'เปิด' : 'ปิด'}การต่ออายุอัตโนมัติสำเร็จ`,
        type: 'toggle',
      };
    }
    catch (error) {
      console.error('Toggle auto renew error:', error);
      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการเปลี่ยนการตั้งค่า',
        type: 'toggle',
      });
    }
  },

  /**
   * ✅ Mark Notification As Read - Hybrid Approach
   * Route API + Service Pattern
   */
  markNotificationAsRead: async ({ request, locals }) => {
    try {
      // Auth check already done in layout
      const formData = await request.formData();
      const notificationId = formData.get('notificationId') as string;

      // Basic validation at route level
      if (!notificationId?.trim()) {
        return fail(400, {
          message: 'ไม่พบ notification ID',
          type: 'notification',
        });
      }

      // Call service for business logic + backend API
      const result = await subscriptionService.markNotificationAsRead(
        notificationId,
        locals.token!,
      );

      if (!result.success) {
        return fail(400, {
          message: result.error || 'เกิดข้อผิดพลาดในการอ่านการแจ้งเตือน',
          type: 'notification',
        });
      }

      return {
        success: true,
        data: result.data,
        message: 'อ่านการแจ้งเตือนสำเร็จ',
        type: 'notification',
      };
    }
    catch (error) {
      console.error('Mark notification as read error:', error);
      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการอ่านการแจ้งเตือน',
        type: 'notification',
      });
    }
  },
};
