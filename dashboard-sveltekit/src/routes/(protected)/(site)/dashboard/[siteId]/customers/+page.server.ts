import { customerService } from '$lib/services/customer';
import type { Customer } from '$lib/types';
import { error, fail } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ locals, params, url }) => {
  // ไม่ต้องตรวจสอบ auth อีก เพราะ layout จัดการแล้ว
  const { siteId } = params;

  try {
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const search = url.searchParams.get('search') || '';

    const response = await customerService.getCustomers(siteId, locals.token!, page, limit, search);

    if (!response.success) {
      throw error(500, response.error || 'Failed to load customers');
    }

    return {
      customers: response.data?.customers || [],
      pagination: response.data?.pagination || {
        page: 1,
        limit: 10,
        total: 0,
        totalPages: 0,
      },
      search,
    };
  }
  catch (err) {
    console.error('Error loading customers:', err);
    throw error(500, 'Failed to load customers');
  }
};

export const actions: Actions = {
  /**
   * ✅ Update Customer - Hybrid Approach
   * Route API + Service Pattern
   */
  updateCustomer: async ({ request, locals, params }) => {
    try {
      const { siteId } = params;
      const formData = await request.formData();
      const customerId = formData.get('customerId') as string;

      // Basic validation at route level
      if (!customerId?.trim()) {
        return fail(400, {
          message: 'ไม่พบ ID ลูกค้า',
          type: 'update',
        });
      }

      const customerData = {
        firstName: formData.get('firstName') as string,
        lastName: formData.get('lastName') as string,
        email: formData.get('email') as string,
        phone: formData.get('phone') as string,
        moneyPoint: parseInt((formData.get('moneyPoint') as string) || '0'),
        goldPoint: parseInt((formData.get('goldPoint') as string) || '0'),
      };

      // Route-level validation
      if (!customerData.firstName?.trim() || !customerData.lastName?.trim()) {
        return fail(400, {
          message: 'กรุณากรอกชื่อและนามสกุล',
          type: 'update',
        });
      }

      if (customerData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(customerData.email)) {
        return fail(400, {
          message: 'รูปแบบอีเมลไม่ถูกต้อง',
          type: 'update',
        });
      }

      // Call service for business logic + backend API
      const response = await customerService.updateCustomer(
        customerId,
        customerData,
        locals.token!,
      );

      if (!response.success) {
        return fail(400, {
          message: response.error,
          type: 'update',
        });
      }

      return {
        success: true,
        data: response.data,
        message: 'อัปเดตข้อมูลลูกค้าสำเร็จ',
        type: 'update',
      };
    }
    catch (error) {
      console.error('Update customer error:', error);
      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการอัปเดตข้อมูลลูกค้า',
        type: 'update',
      });
    }
  },

  /**
   * ✅ Delete Customer - Hybrid Approach
   * Route API + Service Pattern
   */
  deleteCustomer: async ({ request, locals, params }) => {
    try {
      const { siteId } = params;
      const formData = await request.formData();
      const customerId = formData.get('customerId') as string;

      // Basic validation at route level
      if (!customerId?.trim()) {
        return fail(400, {
          message: 'ไม่พบ ID ลูกค้า',
          type: 'delete',
        });
      }

      // Call service for business logic + backend API
      const response = await customerService.deleteCustomer(customerId, locals.token!);

      if (!response.success) {
        return fail(400, {
          message: response.error,
          type: 'delete',
        });
      }

      return {
        success: true,
        message: 'ลบลูกค้าสำเร็จ',
        type: 'delete',
      };
    }
    catch (error) {
      console.error('Delete customer error:', error);
      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการลบลูกค้า',
        type: 'delete',
      });
    }
  },
};
