<script lang="ts">
	import Icon from '@iconify/svelte';
	import { enhance } from '$app/forms';
	import { goto } from '$app/navigation';
	import SEO from '$lib/components/layout/SEO.svelte';
	import Button from '$lib/components/ui/Button.svelte';
	import Card from '$lib/components/ui/Card.svelte';
	import Checkbox from '$lib/components/ui/Checkbox.svelte';
	import Input from '$lib/components/ui/Input.svelte';
	import { authStore } from '$lib/stores/auth.svelte';
	import type { Product } from '$lib/types/product';

	const { data, form } = $props<{
		data: {
			product?: Product;
			categories?: any[];
			errors?: {
				categories?: string | null;
			};
		};
		form?: any;
	}>();

	const product = $derived(data?.product);
	const categories = $derived(data?.categories || []);
	const errors = $derived(data?.errors || {});

	// รอให้ authStore initialized
	const isReady = $derived(authStore.isInitialized);

	// Form action result
	const formResult = $derived(form);

	// Redirect after successful deletion
	$effect(() => {
		if (formResult?.success && formResult?.type === 'delete') {
			setTimeout(() => {
				goto('../products');
			}, 1500);
		}
	});

	// Form data
	let formData = $state({
		name: '',
		type: 'physical' as 'physical' | 'digital' | 'service' | 'subscription',
		saleChannel: 'online' as 'online' | 'offline' | 'both',
		description: '',
		shortDescription: '',
		price: 0,
		compareAtPrice: 0,
		costPrice: 0,
		stock: 0,
		trackStock: true,
		allowBackorder: false,
		categoryId: '',
		tags: [] as string[],
		featured: false,
		isActive: true,
		allowPreOrder: false,
		seoTitle: '',
		seoDescription: '',
		shipping: {
			weight: 0,
			dimensions: {
				length: 0,
				width: 0,
				height: 0,
			},
			shippingClass: '',
		},
	});

	// Initialize form data when product loads
	$effect(() => {
		if (product) {
			formData = {
				name: product.name || '',
				type: product.type || 'physical',
				saleChannel: product.saleChannel || 'online',
				description: product.description || '',
				shortDescription: product.shortDescription || '',
				price: product.price || 0,
				compareAtPrice: product.compareAtPrice || 0,
				costPrice: product.costPrice || 0,
				stock: product.stock || 0,
				trackStock: product.trackStock ?? true,
				allowBackorder: product.allowBackorder ?? false,
				categoryId: product.categoryId || '',
				tags: product.tags || [],
				featured: product.featured ?? false,
				isActive: product.isActive ?? true,
				allowPreOrder: product.allowPreOrder ?? false,
				seoTitle: product.seoTitle || '',
				seoDescription: product.seoDescription || '',
				shipping: {
					weight: product.shipping?.weight || 0,
					dimensions: {
						length: product.shipping?.dimensions?.length || 0,
						width: product.shipping?.dimensions?.width || 0,
						height: product.shipping?.dimensions?.height || 0,
					},
					shippingClass: product.shipping?.shippingClass || '',
				},
			};
		}
	});

	let tagInput = $state('');
	let isDeleting = $state(false);

	function addTag() {
		if (tagInput.trim() && !formData.tags.includes(tagInput.trim())) {
			formData.tags = [...formData.tags, tagInput.trim()];
			tagInput = '';
		}
	}

	function removeTag(tag: string) {
		formData.tags = formData.tags.filter(t => t !== tag);
	}

	function formatPrice(price: number) {
		return new Intl.NumberFormat('th-TH', {
			style: 'currency',
			currency: 'THB',
		}).format(price);
	}

	function getProductImage(product: Product) {
		if (product.images && product.images.length > 0) {
			const primaryImage = product.images.find(img => img.isPrimary);
			return primaryImage?.url || product.images[0].url;
		}
		return null;
	}

	function getProductInitials(name: string) {
		if (!name || typeof name !== 'string') {
			return 'UN';
		}
		return name
			.split(' ')
			.map(word => word.charAt(0))
			.join('')
			.toUpperCase()
			.substring(0, 2);
	}

	function handleDelete() {
		if (confirm('คุณแน่ใจหรือไม่ที่จะลบสินค้านี้? การดำเนินการนี้ไม่สามารถยกเลิกได้')) {
			isDeleting = true;
		}
	}
</script>

<SEO
	title="แก้ไขสินค้า - {product?.name || 'จัดการสินค้า'}"
	description="แก้ไขข้อมูลสินค้า {product?.name || ''}"
	keywords="edit product, แก้ไขสินค้า, จัดการสินค้า"
	url="/dashboard/products/{product?._id}"
	noindex={true}
/>

{#if !isReady}
	<div class="flex items-center justify-center min-h-screen">
		<div class="loading loading-spinner loading-lg"></div>
	</div>
{:else if !product}
	<div class="flex items-center justify-center min-h-screen">
		<Card title="ไม่พบสินค้า" size="md">
			<div class="text-center space-y-4">
				<Icon icon="mdi:package-variant-closed" class="w-16 h-16 mx-auto text-base-content/40" />
				<p class="text-base-content/60">ไม่พบสินค้าที่ต้องการแก้ไข</p>
				<Button href="../products" color="primary">
					<Icon icon="mdi:arrow-left" class="w-4 h-4 mr-2" />
					กลับไปรายการสินค้า
				</Button>
			</div>
		</Card>
	</div>
{:else}
	<div class="space-y-6">
		<!-- Form Action Messages -->
		{#if formResult?.success}
			<div class="alert alert-success">
				<Icon icon="mdi:check-circle" class="w-5 h-5" />
				<span>{formResult.message}</span>
			</div>
		{:else if formResult?.error}
			<div class="alert alert-error">
				<Icon icon="mdi:alert-circle" class="w-5 h-5" />
				<span>{formResult.error}</span>
			</div>
		{/if}

		<!-- API Error Messages -->
		{#if errors.categories}
			<div class="alert alert-warning">
				<Icon icon="mdi:alert" class="w-5 h-5" />
				<span>ไม่สามารถโหลดข้อมูลหมวดหมู่ได้: {errors.categories}</span>
			</div>
		{/if}

		<!-- Header -->
		<div class="flex justify-between items-center">
			<div>
				<h1 class="text-3xl font-bold text-base-content">
					<Icon icon="mdi:package-variant" class="w-8 h-8 inline mr-2" />
					แก้ไขสินค้า
				</h1>
				<p class="text-base-content/60 mt-1">แก้ไขข้อมูลสินค้า: {product.name}</p>
			</div>
			<div class="flex gap-2">
				<Button href="../products" variant="outline">
					<Icon icon="mdi:arrow-left" class="w-5 h-5" />
					กลับ
				</Button>
				<Button color="error" variant="outline" onclick={handleDelete}>
					<Icon icon="mdi:delete" class="w-5 h-5" />
					ลบสินค้า
				</Button>
			</div>
		</div>

		<div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
			<!-- Product Info Card -->
			<div class="lg:col-span-1">
				<Card title="ข้อมูลสินค้า" size="full">
					<div class="space-y-4">
						<!-- Product Image -->
						<div class="flex justify-center">
							{#if getProductImage(product)}
								<div class="avatar">
									<div class="w-32 h-32 rounded-lg">
										<img src={getProductImage(product)} alt={product.name} />
									</div>
								</div>
							{:else}
								<div class="avatar placeholder">
									<div class="bg-neutral text-neutral-content rounded-lg w-32 h-32">
										<span class="text-2xl">{getProductInitials(product.name)}</span>
									</div>
								</div>
							{/if}
						</div>

						<!-- Product Details -->
						<div class="space-y-2">
							<div class="flex justify-between">
								<span class="text-sm text-base-content/70">ID:</span>
								<span class="text-sm font-mono">{product._id}</span>
							</div>
							<div class="flex justify-between">
								<span class="text-sm text-base-content/70">Slug:</span>
								<span class="text-sm font-mono">{product.slug}</span>
							</div>
							<div class="flex justify-between">
								<span class="text-sm text-base-content/70">ราคา:</span>
								<span class="text-sm font-semibold">{formatPrice(product.price)}</span>
							</div>
							<div class="flex justify-between">
								<span class="text-sm text-base-content/70">สต็อก:</span>
								<span
									class="badge {product.stock && product.stock > 10
										? 'badge-success'
										: product.stock && product.stock > 0
											? 'badge-warning'
											: 'badge-error'}"
								>
									{product.stock || 0}
								</span>
							</div>
							<div class="flex justify-between">
								<span class="text-sm text-base-content/70">สถานะ:</span>
								<span class="badge {product.isActive ? 'badge-success' : 'badge-error'}">
									{product.isActive ? 'เปิดขาย' : 'ปิดขาย'}
								</span>
							</div>
							<div class="flex justify-between">
								<span class="text-sm text-base-content/70">สร้างเมื่อ:</span>
								<span class="text-sm">
									{new Date(product.createdAt).toLocaleDateString('th-TH')}
								</span>
							</div>
							<div class="flex justify-between">
								<span class="text-sm text-base-content/70">อัปเดตล่าสุด:</span>
								<span class="text-sm">
									{new Date(product.updatedAt).toLocaleDateString('th-TH')}
								</span>
							</div>
						</div>

						<!-- Quick Stock Update -->
						<div class="divider">อัปเดตสต็อกด่วน</div>
						<form
							method="POST"
							action="?/updateStock"
							use:enhance={() => {
								return async ({ result, update }) => {
									await update();
								};
							}}
						>
							<div class="flex gap-2">
								<Input
									type="number"
									name="stock"
									value={product.stock || 0}
									min="0"
									class="flex-1"
									placeholder="จำนวนสต็อก"
								/>
								<Button type="submit" color="primary" size="sm">
									<Icon icon="mdi:content-save" class="w-4 h-4" />
									อัปเดต
								</Button>
							</div>
						</form>
					</div>
				</Card>
			</div>

			<!-- Edit Form -->
			<div class="lg:col-span-2">
				<Card title="แก้ไขข้อมูลสินค้า" size="full">
					<form
						method="POST"
						action="?/updateProduct"
						use:enhance={() => {
							return async ({ result, update }) => {
								await update();
							};
						}}
					>
						<div class="space-y-6">
							<!-- Basic Information -->
							<div class="space-y-4">
								<h3 class="text-lg font-semibold">ข้อมูลพื้นฐาน</h3>

								<Input
									type="text"
									name="name"
									bind:value={formData.name}
									label="ชื่อสินค้า *"
									required
								/>

								<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
									<div class="form-control">
										<label class="label">
											<span class="label-text">ประเภทสินค้า</span>
										</label>
										<select name="type" bind:value={formData.type} class="select select-bordered">
											<option value="physical">สินค้าทั่วไป</option>
											<option value="digital">สินค้าดิจิทัล</option>
											<option value="service">บริการ</option>
											<option value="subscription">สมาชิก</option>
										</select>
									</div>

									<div class="form-control">
										<label class="label">
											<span class="label-text">ช่องทางการขาย</span>
										</label>
										<select
											name="saleChannel"
											bind:value={formData.saleChannel}
											class="select select-bordered"
										>
											<option value="online">ออนไลน์</option>
											<option value="offline">ออฟไลน์</option>
											<option value="both">ทั้งสองช่องทาง</option>
										</select>
									</div>
								</div>

								<div class="form-control">
									<label class="label">
										<span class="label-text">คำอธิบาย</span>
									</label>
									<textarea
										name="description"
										bind:value={formData.description}
										class="textarea textarea-bordered"
										rows="4"
										placeholder="รายละเอียดสินค้า..."
									></textarea>
								</div>

								<Input
									type="text"
									name="shortDescription"
									bind:value={formData.shortDescription}
									label="คำอธิบายสั้น"
									placeholder="คำอธิบายสั้นๆ สำหรับแสดงในรายการสินค้า"
								/>
							</div>

							<!-- Pricing -->
							<div class="space-y-4">
								<h3 class="text-lg font-semibold">ราคาและสต็อก</h3>

								<div class="grid grid-cols-1 md:grid-cols-3 gap-4">
									<Input
										type="number"
										name="price"
										bind:value={formData.price}
										label="ราคา *"
										min="0"
										step="0.01"
										required
									/>

									<Input
										type="number"
										name="compareAtPrice"
										bind:value={formData.compareAtPrice}
										label="ราคาเปรียบเทียบ"
										min="0"
										step="0.01"
									/>

									<Input
										type="number"
										name="costPrice"
										bind:value={formData.costPrice}
										label="ต้นทุน"
										min="0"
										step="0.01"
									/>
								</div>

								<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
									<Input
										type="number"
										name="stock"
										bind:value={formData.stock}
										label="สต็อก"
										min="0"
									/>

									<div class="form-control">
										<label class="label">
											<span class="label-text">หมวดหมู่</span>
										</label>
										<select
											name="categoryId"
											bind:value={formData.categoryId}
											class="select select-bordered"
										>
											<option value="">เลือกหมวดหมู่</option>
											{#each categories as category}
												<option value={category._id}>{category.name}</option>
											{/each}
										</select>
									</div>
								</div>

								<div class="flex gap-4">
									<Checkbox
										name="trackStock"
										bind:checked={formData.trackStock}
										label="ติดตามสต็อก"
									/>
									<Checkbox
										name="allowBackorder"
										bind:checked={formData.allowBackorder}
										label="อนุญาตสั่งซื้อเมื่อหมดสต็อก"
									/>
								</div>
							</div>

							<!-- Tags -->
							<div class="space-y-4">
								<h3 class="text-lg font-semibold">แท็กและการแสดงผล</h3>

								<div class="form-control">
									<label class="label">
										<span class="label-text">แท็ก</span>
									</label>
									<div class="flex gap-2">
										<input
											type="text"
											bind:value={tagInput}
											class="input input-bordered flex-1"
											placeholder="พิมพ์แท็กแล้วกด Enter"
											onkeydown={e => {
												if (e.key === 'Enter') {
													e.preventDefault();
													addTag();
												}
											}}
										/>
										<Button type="button" variant="outline" onclick={addTag}>
											<Icon icon="mdi:plus" class="w-4 h-4" />
										</Button>
									</div>
									{#if formData.tags.length > 0}
										<div class="flex flex-wrap gap-2 mt-2">
											{#each formData.tags as tag}
												<span class="badge badge-primary gap-1">
													{tag}
													<button
														type="button"
														class="btn btn-xs btn-ghost"
														onclick={() => removeTag(tag)}
													>
														<Icon icon="mdi:close" class="w-3 h-3" />
													</button>
												</span>
											{/each}
										</div>
									{/if}
									<input type="hidden" name="tags" value={formData.tags.join(',')} />
								</div>

								<div class="flex gap-4">
									<Checkbox name="featured" bind:checked={formData.featured} label="สินค้าแนะนำ" />
									<Checkbox name="isActive" bind:checked={formData.isActive} label="เปิดขาย" />
									<Checkbox
										name="allowPreOrder"
										bind:checked={formData.allowPreOrder}
										label="อนุญาต Pre-order"
									/>
								</div>
							</div>

							<!-- Shipping -->
							{#if formData.type === 'physical'}
								<div class="space-y-4">
									<h3 class="text-lg font-semibold">การจัดส่ง</h3>

									<div class="grid grid-cols-1 md:grid-cols-4 gap-4">
										<Input
											type="number"
											name="shipping.weight"
											bind:value={formData.shipping.weight}
											label="น้ำหนัก (กรัม)"
											min="0"
											step="0.1"
										/>

										<Input
											type="number"
											name="shipping.dimensions.length"
											bind:value={formData.shipping.dimensions.length}
											label="ความยาว (ซม.)"
											min="0"
											step="0.1"
										/>

										<Input
											type="number"
											name="shipping.dimensions.width"
											bind:value={formData.shipping.dimensions.width}
											label="ความกว้าง (ซม.)"
											min="0"
											step="0.1"
										/>

										<Input
											type="number"
											name="shipping.dimensions.height"
											bind:value={formData.shipping.dimensions.height}
											label="ความสูง (ซม.)"
											min="0"
											step="0.1"
										/>
									</div>

									<Input
										type="text"
										name="shipping.shippingClass"
										bind:value={formData.shipping.shippingClass}
										label="คลาสการจัดส่ง"
										placeholder="เช่น standard, express"
									/>
								</div>
							{/if}

							<!-- SEO -->
							<div class="space-y-4">
								<h3 class="text-lg font-semibold">SEO</h3>

								<Input
									type="text"
									name="seoTitle"
									bind:value={formData.seoTitle}
									label="SEO Title"
									placeholder="ชื่อหน้าเว็บสำหรับ SEO"
								/>

								<div class="form-control">
									<label class="label">
										<span class="label-text">SEO Description</span>
									</label>
									<textarea
										name="seoDescription"
										bind:value={formData.seoDescription}
										class="textarea textarea-bordered"
										rows="3"
										placeholder="คำอธิบายหน้าเว็บสำหรับ SEO..."
									></textarea>
								</div>
							</div>

							<!-- Submit Buttons -->
							<div class="flex justify-end gap-2">
								<Button href="../products" variant="outline">ยกเลิก</Button>
								<Button type="submit" color="primary">
									<Icon icon="mdi:content-save" class="w-5 h-5" />
									บันทึกการเปลี่ยนแปลง
								</Button>
							</div>
						</div>
					</form>
				</Card>
			</div>
		</div>

		<!-- Delete Confirmation Modal -->
		{#if isDeleting}
			<div class="modal modal-open">
				<div class="modal-box">
					<h3 class="font-bold text-lg">ยืนยันการลบสินค้า</h3>
					<p class="py-4">
						คุณแน่ใจหรือไม่ที่จะลบสินค้า "<strong>{product.name}</strong>"?
						<br />
						การดำเนินการนี้ไม่สามารถยกเลิกได้
					</p>
					<div class="modal-action">
						<Button variant="outline" onclick={() => (isDeleting = false)}>ยกเลิก</Button>
						<form
							method="POST"
							action="?/deleteProduct"
							use:enhance={() => {
								return async ({ result, update }) => {
									await update();
									isDeleting = false;
								};
							}}
						>
							<Button type="submit" color="error">
								<Icon icon="mdi:delete" class="w-4 h-4 mr-2" />
								ลบสินค้า
							</Button>
						</form>
					</div>
				</div>
			</div>
		{/if}
	</div>
{/if}