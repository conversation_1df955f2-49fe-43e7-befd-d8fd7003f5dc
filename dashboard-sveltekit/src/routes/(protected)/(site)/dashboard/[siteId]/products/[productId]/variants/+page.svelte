<script lang="ts">
	import Icon from '@iconify/svelte';
	import { enhance } from '$app/forms';
	import SEO from '$lib/components/layout/SEO.svelte';
	import Button from '$lib/components/ui/Button.svelte';
	import Card from '$lib/components/ui/Card.svelte';
	import Checkbox from '$lib/components/ui/Checkbox.svelte';
	import Input from '$lib/components/ui/Input.svelte';
	import { authStore } from '$lib/stores/auth.svelte';
	import type { Product, ProductVariant } from '$lib/types/product';

	const { data, form } = $props<{
		data: {
			product?: Product;
		};
		form?: any;
	}>();

	const product = $derived(data?.product);

	// รอให้ authStore initialized
	const isReady = $derived(authStore.isInitialized);

	// Form action result
	const formResult = $derived(form);

	// State
	let showAddForm = $state(false);
	let editingVariant = $state<ProductVariant | null>(null);

	// Form data for adding/editing variants
	let variantForm = $state({
		name: '',
		sku: '',
		price: 0,
		stock: 0,
		attributes: {} as Record<string, string>,
		isActive: true,
	});

	// Available attributes (from existing variants or predefined)
	const availableAttributes = $derived(() => {
		if (!product) return [];
		
		const attrs = new Set<string>();
		product.variants?.forEach(variant => {
			Object.keys(variant.attributes || {}).forEach(key => attrs.add(key));
		});
		
		// Add common attributes if none exist
		if (attrs.size === 0) {
			attrs.add('color');
			attrs.add('size');
		}
		
		return Array.from(attrs);
	});

	function resetForm() {
		variantForm = {
			name: '',
			sku: '',
			price: 0,
			stock: 0,
			attributes: {},
			isActive: true,
		};
		showAddForm = false;
		editingVariant = null;
	}

	function startEdit(variant: ProductVariant) {
		editingVariant = variant;
		variantForm = {
			name: variant.name,
			sku: variant.sku || '',
			price: variant.price || 0,
			stock: variant.stock || 0,
			attributes: { ...variant.attributes },
			isActive: variant.isActive,
		};
		showAddForm = true;
	}

	function addAttribute() {
		const attrName = prompt('ชื่อคุณสมบัติ (เช่น color, size):');
		if (attrName && !availableAttributes.includes(attrName)) {
			variantForm.attributes[attrName] = '';
		}
	}

	function removeAttribute(attrName: string) {
		const { [attrName]: removed, ...rest } = variantForm.attributes;
		variantForm.attributes = rest;
	}

	function formatPrice(price: number) {
		return new Intl.NumberFormat('th-TH', {
			style: 'currency',
			currency: 'THB',
		}).format(price);
	}

	function getVariantDisplayName(variant: ProductVariant) {
		const attrs = Object.entries(variant.attributes || {})
			.map(([key, value]) => `${key}: ${value}`)
			.join(', ');
		return attrs ? `${variant.name} (${attrs})` : variant.name;
	}

	// Reset form after successful action
	$effect(() => {
		if (formResult?.success) {
			resetForm();
		}
	});
</script>

<SEO
	title="จัดการตัวเลือกสินค้า - {product?.name || 'จัดการสินค้า'}"
	description="จัดการตัวเลือกสินค้า (Variants) สำหรับ {product?.name || ''}"
	keywords="product variants, ตัวเลือกสินค้า, จัดการสินค้า"
	url="/dashboard/products/{product?._id}/variants"
	noindex={true}
/>

{#if !isReady}
	<div class="flex items-center justify-center min-h-screen">
		<div class="loading loading-spinner loading-lg"></div>
	</div>
{:else if !product}
	<div class="flex items-center justify-center min-h-screen">
		<Card title="ไม่พบสินค้า" size="md">
			<div class="text-center space-y-4">
				<Icon icon="mdi:package-variant-closed" class="w-16 h-16 mx-auto text-base-content/40" />
				<p class="text-base-content/60">ไม่พบสินค้าที่ต้องการจัดการตัวเลือก</p>
				<Button href="../../products" color="primary">
					<Icon icon="mdi:arrow-left" class="w-4 h-4 mr-2" />
					กลับไปรายการสินค้า
				</Button>
			</div>
		</Card>
	</div>
{:else}
	<div class="space-y-6">
		<!-- Form Action Messages -->
		{#if formResult?.success}
			<div class="alert alert-success">
				<Icon icon="mdi:check-circle" class="w-5 h-5" />
				<span>{formResult.message}</span>
			</div>
		{:else if formResult?.error}
			<div class="alert alert-error">
				<Icon icon="mdi:alert-circle" class="w-5 h-5" />
				<span>{formResult.error}</span>
			</div>
		{/if}

		<!-- Header -->
		<div class="flex justify-between items-center">
			<div>
				<h1 class="text-3xl font-bold text-base-content">
					<Icon icon="mdi:tune-variant" class="w-8 h-8 inline mr-2" />
					จัดการตัวเลือกสินค้า
				</h1>
				<p class="text-base-content/60 mt-1">
					จัดการตัวเลือกสินค้า (Variants) สำหรับ: {product.name}
				</p>
			</div>
			<div class="flex gap-2">
				<Button href="../" variant="outline">
					<Icon icon="mdi:arrow-left" class="w-5 h-5" />
					กลับ
				</Button>
				<Button color="primary" onclick={() => (showAddForm = true)}>
					<Icon icon="mdi:plus" class="w-5 h-5" />
					เพิ่มตัวเลือก
				</Button>
			</div>
		</div>

		<div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
			<!-- Product Info -->
			<div class="lg:col-span-1">
				<Card title="ข้อมูลสินค้า" size="full">
					<div class="space-y-4">
						<div class="space-y-2">
							<div class="flex justify-between">
								<span class="text-sm text-base-content/70">ชื่อสินค้า:</span>
								<span class="text-sm font-semibold">{product.name}</span>
							</div>
							<div class="flex justify-between">
								<span class="text-sm text-base-content/70">ราคาหลัก:</span>
								<span class="text-sm font-semibold">{formatPrice(product.price)}</span>
							</div>
							<div class="flex justify-between">
								<span class="text-sm text-base-content/70">มีตัวเลือก:</span>
								<span class="badge {product.hasVariants ? 'badge-success' : 'badge-neutral'}">
									{product.hasVariants ? 'ใช่' : 'ไม่'}
								</span>
							</div>
							<div class="flex justify-between">
								<span class="text-sm text-base-content/70">จำนวนตัวเลือก:</span>
								<span class="text-sm font-semibold">{product.variants?.length || 0}</span>
							</div>
						</div>

						{#if product.variantAttributes && product.variantAttributes.length > 0}
							<div class="divider">คุณสมบัติที่ใช้</div>
							<div class="flex flex-wrap gap-2">
								{#each product.variantAttributes as attr}
									<span class="badge badge-primary">{attr}</span>
								{/each}
							</div>
						{/if}
					</div>
				</Card>
			</div>

			<!-- Variants List -->
			<div class="lg:col-span-2">
				<Card title="รายการตัวเลือกสินค้า" size="full">
					{#if product.variants && product.variants.length > 0}
						<div class="overflow-x-auto">
							<table class="table table-zebra">
								<thead>
									<tr>
										<th>ชื่อตัวเลือก</th>
										<th>คุณสมบัติ</th>
										<th>SKU</th>
										<th>ราคา</th>
										<th>สต็อก</th>
										<th>สถานะ</th>
										<th>การดำเนินการ</th>
									</tr>
								</thead>
								<tbody>
									{#each product.variants as variant}
										<tr>
											<td class="font-medium">{variant.name}</td>
											<td>
												<div class="flex flex-wrap gap-1">
													{#each Object.entries(variant.attributes || {}) as [key, value]}
														<span class="badge badge-sm badge-outline">
															{key}: {value}
														</span>
													{/each}
												</div>
											</td>
											<td>
												<span class="font-mono text-sm">{variant.sku || '-'}</span>
											</td>
											<td>
												{#if variant.price}
													{formatPrice(variant.price)}
												{:else}
													<span class="text-base-content/60">ใช้ราคาหลัก</span>
												{/if}
											</td>
											<td>
												<span
													class="badge {(variant.stock || 0) > 10
														? 'badge-success'
														: (variant.stock || 0) > 0
															? 'badge-warning'
															: 'badge-error'}"
												>
													{variant.stock || 0}
												</span>
											</td>
											<td>
												<span class="badge {variant.isActive ? 'badge-success' : 'badge-error'}">
													{variant.isActive ? 'เปิดใช้' : 'ปิดใช้'}
												</span>
											</td>
											<td>
												<div class="flex gap-1">
													<Button size="xs" variant="outline" onclick={() => startEdit(variant)}>
														<Icon icon="mdi:pencil" class="w-3 h-3" />
													</Button>
													<form
														method="POST"
														action="?/deleteVariant"
														use:enhance={() => {
															return async ({ result, update }) => {
																if (confirm('คุณแน่ใจหรือไม่ที่จะลบตัวเลือกนี้?')) {
																	await update();
																}
															};
														}}
													>
														<input type="hidden" name="variantId" value={variant._id} />
														<Button type="submit" size="xs" color="error" variant="outline">
															<Icon icon="mdi:delete" class="w-3 h-3" />
														</Button>
													</form>
												</div>
											</td>
										</tr>
									{/each}
								</tbody>
							</table>
						</div>
					{:else}
						<div class="text-center py-8">
							<Icon icon="mdi:tune-variant" class="w-16 h-16 mx-auto text-base-content/40 mb-4" />
							<h3 class="text-lg font-semibold mb-2">ยังไม่มีตัวเลือกสินค้า</h3>
							<p class="text-base-content/60 mb-4">
								เพิ่มตัวเลือกสินค้า เช่น สี ขนาด หรือรุ่น เพื่อให้ลูกค้าเลือกได้
							</p>
							<Button color="primary" onclick={() => (showAddForm = true)}>
								<Icon icon="mdi:plus" class="w-4 h-4 mr-2" />
								เพิ่มตัวเลือกแรก
							</Button>
						</div>
					{/if}
				</Card>
			</div>
		</div>

		<!-- Add/Edit Variant Modal -->
		{#if showAddForm}
			<div class="modal modal-open">
				<div class="modal-box max-w-2xl">
					<h3 class="font-bold text-lg mb-4">
						{editingVariant ? 'แก้ไขตัวเลือกสินค้า' : 'เพิ่มตัวเลือกสินค้า'}
					</h3>

					<form
						method="POST"
						action={editingVariant ? '?/updateVariant' : '?/addVariant'}
						use:enhance={() => {
							return async ({ result, update }) => {
								await update();
							};
						}}
					>
						{#if editingVariant}
							<input type="hidden" name="variantId" value={editingVariant._id} />
						{/if}

						<div class="space-y-4">
							<Input
								type="text"
								name="name"
								bind:value={variantForm.name}
								label="ชื่อตัวเลือก *"
								placeholder="เช่น สีแดง ไซส์ M"
								required
							/>

							<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
								<Input
									type="text"
									name="sku"
									bind:value={variantForm.sku}
									label="SKU"
									placeholder="รหัสสินค้า"
								/>

								<Input
									type="number"
									name="price"
									bind:value={variantForm.price}
									label="ราคา (ถ้าต่างจากราคาหลัก)"
									min="0"
									step="0.01"
									placeholder="ใช้ราคาหลัก"
								/>
							</div>

							<Input
								type="number"
								name="stock"
								bind:value={variantForm.stock}
								label="สต็อก"
								min="0"
								required
							/>

							<!-- Attributes -->
							<div class="space-y-2">
								<label class="label">
									<span class="label-text">คุณสมบัติ *</span>
									<Button
										type="button"
										size="xs"
										variant="outline"
										onclick={addAttribute}
									>
										<Icon icon="mdi:plus" class="w-3 h-3 mr-1" />
										เพิ่มคุณสมบัติ
									</Button>
								</label>

								<div class="space-y-2">
									{#each availableAttributes as attr}
										<div class="flex gap-2 items-center">
											<span class="w-20 text-sm font-medium">{attr}:</span>
											<input
												type="text"
												name="attributes.{attr}"
												bind:value={variantForm.attributes[attr]}
												class="input input-bordered input-sm flex-1"
												placeholder="ค่าของ {attr}"
											/>
											<Button
												type="button"
												size="xs"
												color="error"
												variant="outline"
												onclick={() => removeAttribute(attr)}
											>
												<Icon icon="mdi:close" class="w-3 h-3" />
											</Button>
										</div>
									{/each}
								</div>
							</div>

							<Checkbox
								name="isActive"
								bind:checked={variantForm.isActive}
								label="เปิดใช้งาน"
							/>
						</div>

						<div class="modal-action">
							<Button type="button" variant="outline" onclick={resetForm}>ยกเลิก</Button>
							<Button type="submit" color="primary">
								<Icon icon="mdi:content-save" class="w-4 h-4 mr-2" />
								{editingVariant ? 'บันทึกการเปลี่ยนแปลง' : 'เพิ่มตัวเลือก'}
							</Button>
						</div>
					</form>
				</div>
			</div>
		{/if}
	</div>
{/if}