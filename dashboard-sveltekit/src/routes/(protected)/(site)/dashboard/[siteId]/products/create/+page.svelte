<script lang="ts">
	import { enhance } from '$app/forms';
	import { goto } from '$app/navigation';
	import { page } from '$app/stores';
	import { onMount } from 'svelte';
	import { t } from 'svelte-i18n';
	import { Button, Card, Input, Label, Textarea, Select, Checkbox, Badge, Alert } from '$lib/components/ui';
	import Icon from '@iconify/svelte';

	interface PageData {
		categories?: any[];
		error?: string;
	}

	interface Props {
		data: PageData;
	}

	let { data }: Props = $props();

	const formData = $state({
		name: '',
		description: '',
		shortDescription: '',
		sku: '',
		price: 0,
		compareAtPrice: 0,
		cost: 0,
		stock: 0,
		categoryId: '',
		type: 'physical',
		saleChannel: 'online',
		tags: [] as string[],
		featured: false,
		isActive: true,
		allowPreOrder: false,
		seoTitle: '',
		seoDescription: '',
		shipping: {
			weight: 0,
			dimensions: {
				length: 0,
				width: 0,
				height: 0,
			},
			shippingClass: '',
			requiresShipping: true,
		},
	});

	let newTag = $state('');
	let isSubmitting = $state(false);

	const productTypes = [
		{ value: 'physical', label: 'สินค้าทางกายภาพ' },
		{ value: 'digital', label: 'สินค้าดิจิทัล' },
		{ value: 'service', label: 'บริการ' },
	];

	const saleChannels = [
		{ value: 'online', label: 'ออนไลน์' },
		{ value: 'offline', label: 'ออฟไลน์' },
		{ value: 'both', label: 'ทั้งสอง' },
	];

	function addTag() {
		if (newTag.trim() && !formData.tags.includes(newTag.trim())) {
			formData.tags = [...formData.tags, newTag.trim()];
			newTag = '';
		}
	}

	function removeTag(index: number) {
		formData.tags = formData.tags.filter((_, i) => i !== index);
	}

	function handleKeyPress(event: KeyboardEvent) {
		if (event.key === 'Enter') {
			event.preventDefault();
			addTag();
		}
	}

	async function handleSubmit() {
		isSubmitting = true;
	}

	onMount(() => {
		// Initialize form with default values if needed
	});
</script>

<svelte:head>
	<title>{$t('products.create.title')}</title>
	<meta name="description" content="{$t('products.create.description')}" />
</svelte:head>

<div class="container mx-auto p-6 space-y-6">
	<!-- Header -->
	<div class="bg-gradient-to-r from-primary to-primary-focus rounded-lg p-6 text-white shadow-lg">
		<div class="flex items-center gap-4">
			<Icon icon="mdi:plus-circle" class="w-8 h-8" />
			<div>
				<h1 class="text-2xl font-bold">{$t('products.create.title')}</h1>
				<p class="text-primary-content/80">{$t('products.create.description')}</p>
			</div>
		</div>
	</div>

	<!-- Error Alert -->
	{#if data.error}
		<Alert color="error" className="shadow-lg">
			<Icon icon="mdi:alert-circle" class="w-6 h-6" />
			<span class="font-medium">{$t('common.error')}</span>
			<p>{data.error}</p>
		</Alert>
	{/if}

	<!-- Loading State -->
	{#if isSubmitting}
		<div class="flex items-center justify-center p-8">
			<div class="flex items-center gap-3">
				<div class="loading loading-spinner loading-lg text-primary"></div>
				<span class="text-lg">กำลังโหลด...</span>
			</div>
		</div>
	{:else}
		<form method="POST" use:enhance={handleSubmit} class="space-y-8">
			<div class="grid xl:grid-cols-2 gap-8">
				<!-- Basic Information -->
				<Card class="shadow-xl border border-base-300 p-8">
					<div class="flex items-center gap-3 mb-6">
						<div class="bg-primary/10 rounded-lg p-2">
							<Icon icon="mdi:information" class="w-6 h-6 text-primary" />
						</div>
						<h2 class="text-xl font-semibold">ข้อมูลพื้นฐาน</h2>
					</div>

					<div class="space-y-6">
						<div>
							<Label for="name" class="text-base font-medium" text="ชื่อสินค้า *" />
							<Input
								id="name"
								name="name"
								bind:value={formData.name}
								placeholder="ใส่ชื่อสินค้า"
								class="input-lg focus:input-primary"
								required
							/>
						</div>

						<div>
							<Label for="shortDescription" class="text-base font-medium" text="คำอธิบายสั้น" />
							<Textarea
								id="shortDescription"
								name="shortDescription"
								value={formData.shortDescription}
								onInput={e => formData.shortDescription = (e.target as HTMLTextAreaElement).value}
								placeholder="คำอธิบายสั้นๆ เกี่ยวกับสินค้า"
								rows={3}
								class="focus:input-primary"
							/>
						</div>

						<div>
							<Label for="description" class="text-base font-medium" text="คำอธิบาย" />
							<Textarea
								value={formData.description}
								onInput={e => formData.description = (e.target as HTMLTextAreaElement).value}
								placeholder="คำอธิบายรายละเอียดสินค้า"
								rows={5}
								class="focus:input-primary"
							/>
						</div>

						<div class="grid grid-cols-2 gap-4">
							<div>
								<Label for="sku" class="text-base font-medium" text="SKU" />
								<div class="input-group">
									<span class="input-group-text bg-base-200">
										<Icon icon="mdi:barcode" class="w-4 h-4" />
									</span>
									<Input
										id="sku"
										name="sku"
										bind:value={formData.sku}
										placeholder="SKU"
										class="input-lg focus:input-primary"
									/>
								</div>
							</div>

							<div>
								<Label for="categoryId" class="text-base font-medium" text="หมวดหมู่" />
								<div class="input-group">
									<span class="input-group-text bg-base-200">
										<Icon icon="mdi:folder" class="w-4 h-4" />
									</span>
									<Select
										id="categoryId"
										name="categoryId"
										bind:value={formData.categoryId}
										class="input-lg focus:input-primary"
									>
										<option value="">เลือกหมวดหมู่</option>
										{#if data.categories}
											{#each data.categories as category}
												<option value={category.id}>{category.name}</option>
											{/each}
										{/if}
									</Select>
								</div>
							</div>
						</div>

						<div class="grid grid-cols-2 gap-4">
							<div>
								<Label for="type" class="text-base font-medium" text="ประเภทสินค้า" />
								<div class="input-group">
									<span class="input-group-text bg-base-200">
										<Icon icon="mdi:package-variant" class="w-4 h-4" />
									</span>
									<Select
										id="type"
										name="type"
										bind:value={formData.type}
										class="input-lg focus:input-primary"
									>
										{#each productTypes as type}
											<option value={type.value}>{type.label}</option>
										{/each}
									</Select>
								</div>
							</div>

							<div>
								<Label for="saleChannel" class="text-base font-medium" text="ช่องทางการขาย" />
								<div class="input-group">
									<span class="input-group-text bg-base-200">
										<Icon icon="mdi:store" class="w-4 h-4" />
									</span>
									<Select
										id="saleChannel"
										name="saleChannel"
										bind:value={formData.saleChannel}
										class="input-lg focus:input-primary"
									>
										{#each saleChannels as channel}
											<option value={channel.value}>{channel.label}</option>
										{/each}
									</Select>
								</div>
							</div>
						</div>
					</div>
				</Card>

				<!-- Pricing & Inventory -->
				<Card class="shadow-xl border border-base-300 p-8">
					<div class="flex items-center gap-3 mb-6">
						<div class="bg-success/10 rounded-lg p-2">
							<Icon icon="mdi:currency-usd" class="w-6 h-6 text-success" />
						</div>
						<h2 class="text-xl font-semibold">ราคาและสต็อก</h2>
					</div>

					<div class="space-y-6">
						<div>
							<Label for="price" class="text-base font-medium" text="ราคาขาย *" />
							<div class="input-group">
								<span class="input-group-text bg-success text-success-content">
									<Icon icon="mdi:currency-usd" class="w-4 h-4" />
								</span>
								<Input
									id="price"
									name="price"
									type="number"
									bind:value={formData.price}
									placeholder="0.00"
									step="0.01"
									min="0"
									class="input-lg focus:input-primary"
									required
								/>
							</div>
						</div>

						<div>
							<Label for="compareAtPrice" class="text-base font-medium" text="ราคาเปรียบเทียบ" />
							<div class="input-group">
								<span class="input-group-text bg-warning text-warning-content">
									<Icon icon="mdi:tag" class="w-4 h-4" />
								</span>
								<Input
									id="compareAtPrice"
									name="compareAtPrice"
									type="number"
									bind:value={formData.compareAtPrice}
									placeholder="0.00"
									step="0.01"
									min="0"
									class="input-lg focus:input-primary"
								/>
							</div>
						</div>

						<div>
							<Label for="cost" class="text-base font-medium" text="ต้นทุน" />
							<div class="input-group">
								<span class="input-group-text bg-error text-error-content">
									<Icon icon="mdi:calculator" class="w-4 h-4" />
								</span>
								<Input
									id="cost"
									name="cost"
									type="number"
									bind:value={formData.cost}
									placeholder="0.00"
									step="0.01"
									min="0"
									class="input-lg focus:input-primary"
								/>
							</div>
						</div>

						<div>
							    <Label for="stock" class="text-base font-medium" text="จำนวนสต็อก" />
							<div class="input-group">
								<span class="input-group-text bg-info text-info-content">
									<Icon icon="mdi:package-variant-closed" class="w-4 h-4" />
								</span>
								<Input
									id="stock"
									name="stock"
									type="number"
									bind:value={formData.stock}
									placeholder="0"
									min="0"
									class="input-lg focus:input-primary"
								/>
							</div>
						</div>
					</div>
				</Card>
			</div>

			<!-- Tags Section -->
			<Card class="shadow-xl border border-base-300 p-8">
				<div class="flex items-center gap-3 mb-6">
					<div class="bg-secondary/10 rounded-lg p-2">
						<Icon icon="mdi:tag-multiple" class="w-6 h-6 text-secondary" />
					</div>
					<h2 class="text-xl font-semibold">แท็ก</h2>
				</div>

				<div class="space-y-4">
					<div class="input-group">
						<span class="input-group-text bg-base-200">
							<Icon icon="mdi:tag-plus" class="w-4 h-4" />
						</span>
						<Input
							value={newTag}
							placeholder="ใส่แท็กและกด Enter"
							class="input-lg focus:input-primary"
							onkeydown={handleKeyPress}
						/>
						<Button type="button" color="primary" onclick={addTag} class="btn-lg">
							<Icon icon="mdi:plus" class="w-4 h-4" />
							เพิ่ม
						</Button>
					</div>

					{#if formData.tags.length > 0}
						<div class="flex flex-wrap gap-2">
							{#each formData.tags as tag, index}
								<Badge color="secondary" class="text-sm">
									{tag}
									<button
										type="button"
										onclick={() => removeTag(index)}
										class="ml-2 hover:text-error"
									>
										<Icon icon="mdi:close" class="w-3 h-3" />
									</button>
								</Badge>
							{/each}
						</div>
					{/if}
				</div>
			</Card>

			<!-- Options -->
			<Card class="shadow-xl border border-base-300 p-8">
				<div class="flex items-center gap-3 mb-6">
					<div class="bg-neutral/10 rounded-lg p-2">
						<Icon icon="mdi:cog" class="w-6 h-6 text-neutral" />
					</div>
					<h2 class="text-xl font-semibold">ตัวเลือก</h2>
				</div>

				<div class="grid grid-cols-1 md:grid-cols-3 gap-6">
					<div>
						<Checkbox
							id="featured"
							name="featured"
							checked={formData.featured}
							label="สินค้าแนะนำ"
						/>
					</div>

					<div>
						<Checkbox
							id="isActive"
							name="isActive"
							checked={formData.isActive}
							label="เปิดใช้งาน"
						/>
					</div>

					<div>
						<Checkbox
							id="allowPreOrder"
							name="allowPreOrder"
							checked={formData.allowPreOrder}
							label="อนุญาตให้สั่งจองล่วงหน้า"
						/>
					</div>
				</div>
			</Card>

			<!-- Submit Buttons -->
			<div class="flex justify-end gap-4 pt-6">
				<Button
					type="button"
					variant="outline"
					size="lg"
					onclick={() => goto(`/dashboard/${$page.params.siteId}/products`)}
			 
				>
					<Icon icon="mdi:close" class="w-5 h-5" />
					{$t('common.cancel')}
				</Button>
				<Button type="submit" color="primary" size="lg" class="btn-lg" disabled={isSubmitting}>
					<Icon icon="mdi:content-save" class="w-5 h-5" />
					{$t('common.save')}
				</Button>
			</div>
		</form>
	{/if}
</div> 