import { categoryService } from '$lib/services/category';
import { productService } from '$lib/services/product';
import { error, fail, redirect } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ params, locals }) => {
  try {
    if (!locals.token || !locals.user) {
      throw redirect(302, '/signin');
    }

    const { siteId, productId } = params;
    if (!siteId || !productId) {
      throw error(400, 'Site ID และ Product ID จำเป็น');
    }

    // Fetch product and categories in parallel
    const [productResponse, categoriesResponse] = await Promise.all([
      productService.getProduct(productId, siteId, locals.token),
      categoryService.getCategories(siteId, locals.token),
    ]);

    if (!productResponse.success) {
      throw error(404, productResponse.error || 'ไม่พบสินค้า');
    }

    return {
      product: productResponse.data,
      categories: categoriesResponse.success ? categoriesResponse.data : [],
      errors: {
        categories: !categoriesResponse.success ? categoriesResponse.error : null,
      },
    };
  }
  catch (err) {
    console.error('Error loading product edit page:', err);
    if (err instanceof Response) {
      throw err;
    }
    throw error(500, 'เกิดข้อผิดพลาดในการโหลดข้อมูล');
  }
};

export const actions: Actions = {
  updateProduct: async ({ request, params, locals }) => {
    try {
      if (!locals.token || !locals.user) {
        return fail(401, { error: 'กรุณาเข้าสู่ระบบ' });
      }

      const { siteId, productId } = params;
      if (!siteId || !productId) {
        return fail(400, { error: 'Site ID และ Product ID จำเป็น' });
      }

      const formData = await request.formData();

      // Parse form data
      const productData = {
        name: formData.get('name') as string,
        type: (formData.get('type') as 'physical' | 'digital' | 'service' | 'subscription') || 'physical',
        saleChannel: (formData.get('saleChannel') as 'online' | 'offline' | 'both') || 'online',
        description: formData.get('description') as string,
        shortDescription: formData.get('shortDescription') as string,
        price: parseFloat(formData.get('price') as string) || 0,
        compareAtPrice: parseFloat(formData.get('compareAtPrice') as string) || undefined,
        costPrice: parseFloat(formData.get('costPrice') as string) || undefined,
        stock: parseInt(formData.get('stock') as string) || 0,
        trackStock: formData.get('trackStock') === 'on',
        allowBackorder: formData.get('allowBackorder') === 'on',
        categoryId: formData.get('categoryId') as string,
        tags: (formData.get('tags') as string)?.split(',').filter(Boolean) || [],
        featured: formData.get('featured') === 'on',
        isActive: formData.get('isActive') === 'on',
        allowPreOrder: formData.get('allowPreOrder') === 'on',
        seoTitle: formData.get('seoTitle') as string,
        seoDescription: formData.get('seoDescription') as string,
        shipping: {
          weight: parseFloat(formData.get('shipping.weight') as string) || undefined,
          dimensions: {
            length: parseFloat(formData.get('shipping.dimensions.length') as string) || 0,
            width: parseFloat(formData.get('shipping.dimensions.width') as string) || 0,
            height: parseFloat(formData.get('shipping.dimensions.height') as string) || 0,
          },
          shippingClass: formData.get('shipping.shippingClass') as string,
        },
      };

      // Validation
      if (!productData.name?.trim()) {
        return fail(400, { error: 'กรุณากรอกชื่อสินค้า' });
      }

      if (productData.price <= 0) {
        return fail(400, { error: 'กรุณากรอกราคาสินค้า' });
      }

      const response = await productService.updateProduct(productId, productData, siteId, locals.token);

      if (response.success) {
        return { success: true, message: 'อัปเดตสินค้าเรียบร้อย' };
      }
      else {
        return fail(400, {
          error: response.error || 'เกิดข้อผิดพลาดในการอัปเดตสินค้า',
        });
      }
    }
    catch (err) {
      console.error('Error updating product:', err);
      return fail(500, { error: 'เกิดข้อผิดพลาดในการอัปเดตสินค้า' });
    }
  },

  deleteProduct: async ({ params, locals }) => {
    try {
      if (!locals.token || !locals.user) {
        return fail(401, { error: 'กรุณาเข้าสู่ระบบ' });
      }

      const { siteId, productId } = params;
      if (!siteId || !productId) {
        return fail(400, { error: 'Site ID และ Product ID จำเป็น' });
      }

      const response = await productService.deleteProduct(productId, siteId, locals.token);

      if (response.success) {
        throw redirect(302, `../products`);
      }
      else {
        return fail(400, {
          error: response.error || 'เกิดข้อผิดพลาดในการลบสินค้า',
        });
      }
    }
    catch (err) {
      if (err instanceof Response) {
        throw err;
      }
      console.error('Error deleting product:', err);
      return fail(500, { error: 'เกิดข้อผิดพลาดในการลบสินค้า' });
    }
  },

  updateStock: async ({ request, params, locals }) => {
    try {
      if (!locals.token || !locals.user) {
        return fail(401, { error: 'กรุณาเข้าสู่ระบบ' });
      }

      const { siteId, productId } = params;
      if (!siteId || !productId) {
        return fail(400, { error: 'Site ID และ Product ID จำเป็น' });
      }

      const formData = await request.formData();
      const stockValue = formData.get('stock') as string;
      const variantId = formData.get('variantId') as string;

      const stock = parseInt(stockValue);
      if (isNaN(stock) || stock < 0) {
        return fail(400, {
          error: 'จำนวนสต็อกต้องเป็นตัวเลขที่มากกว่าหรือเท่ากับ 0',
        });
      }

      const response = await productService.updateProductStock(
        productId,
        { stock, variantId: variantId || undefined },
        siteId,
        locals.token,
      );

      if (response.success) {
        return { success: true, message: 'อัปเดตสต็อกสำเร็จ' };
      }
      else {
        return fail(400, {
          error: response.error || 'เกิดข้อผิดพลาดในการอัปเดตสต็อก',
        });
      }
    }
    catch (err) {
      console.error('Error updating stock:', err);
      return fail(500, { error: 'เกิดข้อผิดพลาดในการอัปเดตสต็อก' });
    }
  },
};
