<script lang="ts">
	import Icon from '@iconify/svelte';
	import SEO from '$lib/components/layout/SEO.svelte';
	import Button from '$lib/components/ui/Button.svelte';
	import Card from '$lib/components/ui/Card.svelte';
	import { authStore } from '$lib/stores/auth.svelte';
	import type { ProductStats } from '$lib/types/product';

	const { data } = $props<{
		data: {
			stats?: ProductStats | null;
			error?: string | null;
		};
	}>();

	const stats = $derived(data?.stats);
	const error = $derived(data?.error);

	// รอให้ authStore initialized
	const isReady = $derived(authStore.isInitialized);

	// Calculate percentages
	const activePercentage = $derived(() => {
		if (!stats || stats.totalProducts === 0) return 0;
		return Math.round((stats.activeProducts / stats.totalProducts) * 100);
	});

	const outOfStockPercentage = $derived(() => {
		if (!stats || stats.totalProducts === 0) return 0;
		return Math.round((stats.outOfStockProducts / stats.totalProducts) * 100);
	});

	const lowStockPercentage = $derived(() => {
		if (!stats || stats.totalProducts === 0) return 0;
		return Math.round((stats.lowStockProducts / stats.totalProducts) * 100);
	});

	function getStockHealthColor() {
		if (!stats) return 'text-base-content';
		
		const healthyStock = stats.totalProducts - stats.outOfStockProducts - stats.lowStockProducts;
		const healthyPercentage = stats.totalProducts > 0 ? (healthyStock / stats.totalProducts) * 100 : 0;
		
		if (healthyPercentage >= 80) return 'text-success';
		if (healthyPercentage >= 60) return 'text-warning';
		return 'text-error';
	}

	function getStockHealthText() {
		if (!stats) return 'ไม่ทราบ';
		
		const healthyStock = stats.totalProducts - stats.outOfStockProducts - stats.lowStockProducts;
		const healthyPercentage = stats.totalProducts > 0 ? (healthyStock / stats.totalProducts) * 100 : 0;
		
		if (healthyPercentage >= 80) return 'ดีมาก';
		if (healthyPercentage >= 60) return 'ปานกลาง';
		return 'ต้องปรับปรุง';
	}
</script>

<SEO
	title="สถิติสินค้า - จัดการสินค้า"
	description="ดูสถิติและข้อมูลสินค้าทั้งหมด"
	keywords="product stats, สถิติสินค้า, รายงานสินค้า"
	url="/dashboard/products/stats"
	noindex={true}
/>

{#if !isReady}
	<div class="flex items-center justify-center min-h-screen">
		<div class="loading loading-spinner loading-lg"></div>
	</div>
{:else}
	<div class="space-y-6">
		<!-- Header -->
		<div class="flex justify-between items-center">
			<div>
				<h1 class="text-3xl font-bold text-base-content">
					<Icon icon="mdi:chart-box" class="w-8 h-8 inline mr-2" />
					สถิติสินค้า
				</h1>
				<p class="text-base-content/60 mt-1">ดูสถิติและข้อมูลสินค้าทั้งหมด</p>
			</div>
			<div class="flex gap-2">
				<Button href="../products" variant="outline">
					<Icon icon="mdi:arrow-left" class="w-5 h-5" />
					กลับ
				</Button>
				<Button color="primary" onclick={() => window.location.reload()}>
					<Icon icon="mdi:refresh" class="w-5 h-5" />
					รีเฟรช
				</Button>
			</div>
		</div>

		{#if error}
			<div class="alert alert-error">
				<Icon icon="mdi:alert-circle" class="w-5 h-5" />
				<span>ไม่สามารถโหลดสถิติสินค้าได้: {error}</span>
			</div>
		{:else if !stats}
			<div class="flex items-center justify-center min-h-[400px]">
				<div class="loading loading-spinner loading-lg"></div>
			</div>
		{:else}
			<!-- Stats Cards -->
			<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
				<!-- Total Products -->
				<Card size="full">
					<div class="stat">
						<div class="stat-figure text-primary">
							<Icon icon="mdi:package" class="w-8 h-8" />
						</div>
						<div class="stat-title">สินค้าทั้งหมด</div>
						<div class="stat-value text-primary">{stats.totalProducts.toLocaleString()}</div>
						<div class="stat-desc">รายการสินค้าในระบบ</div>
					</div>
				</Card>

				<!-- Active Products -->
				<Card size="full">
					<div class="stat">
						<div class="stat-figure text-success">
							<Icon icon="mdi:package-check" class="w-8 h-8" />
						</div>
						<div class="stat-title">สินค้าเปิดขาย</div>
						<div class="stat-value text-success">{stats.activeProducts.toLocaleString()}</div>
						<div class="stat-desc">{activePercentage()}% ของสินค้าทั้งหมด</div>
					</div>
				</Card>

				<!-- Out of Stock -->
				<Card size="full">
					<div class="stat">
						<div class="stat-figure text-error">
							<Icon icon="mdi:package-variant-closed" class="w-8 h-8" />
						</div>
						<div class="stat-title">สินค้าหมดสต็อก</div>
						<div class="stat-value text-error">{stats.outOfStockProducts.toLocaleString()}</div>
						<div class="stat-desc">{outOfStockPercentage()}% ของสินค้าทั้งหมด</div>
					</div>
				</Card>

				<!-- Low Stock -->
				<Card size="full">
					<div class="stat">
						<div class="stat-figure text-warning">
							<Icon icon="mdi:package-down" class="w-8 h-8" />
						</div>
						<div class="stat-title">สินค้าสต็อกต่ำ</div>
						<div class="stat-value text-warning">{stats.lowStockProducts.toLocaleString()}</div>
						<div class="stat-desc">{lowStockPercentage()}% ของสินค้าทั้งหมด</div>
					</div>
				</Card>
			</div>

			<!-- Charts and Analysis -->
			<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
				<!-- Stock Health -->
				<Card title="สุขภาพสต็อกสินค้า" size="full">
					<div class="space-y-4">
						<div class="text-center">
							<div class="text-4xl font-bold {getStockHealthColor()} mb-2">
								{getStockHealthText()}
							</div>
							<p class="text-base-content/60">สถานะโดยรวมของสต็อกสินค้า</p>
						</div>

						<!-- Progress bars -->
						<div class="space-y-3">
							<div>
								<div class="flex justify-between text-sm mb-1">
									<span>สินค้าเปิดขาย</span>
									<span>{activePercentage()}%</span>
								</div>
								<progress class="progress progress-success w-full" value={activePercentage()} max="100"></progress>
							</div>

							<div>
								<div class="flex justify-between text-sm mb-1">
									<span>สินค้าสต็อกต่ำ</span>
									<span>{lowStockPercentage()}%</span>
								</div>
								<progress class="progress progress-warning w-full" value={lowStockPercentage()} max="100"></progress>
							</div>

							<div>
								<div class="flex justify-between text-sm mb-1">
									<span>สินค้าหมดสต็อก</span>
									<span>{outOfStockPercentage()}%</span>
								</div>
								<progress class="progress progress-error w-full" value={outOfStockPercentage()} max="100"></progress>
							</div>
						</div>
					</div>
				</Card>

				<!-- Quick Actions -->
				<Card title="การดำเนินการด่วน" size="full">
					<div class="space-y-4">
						<div class="grid grid-cols-1 gap-3">
							<Button href="../products?status=inactive" variant="outline" block>
								<Icon icon="mdi:package-variant-closed" class="w-5 h-5 mr-2" />
								ดูสินค้าที่ปิดขาย ({stats.totalProducts - stats.activeProducts} รายการ)
							</Button>

							<Button href="../products" variant="outline" block>
								<Icon icon="mdi:package-down" class="w-5 h-5 mr-2" />
								ดูสินค้าหมดสต็อก ({stats.outOfStockProducts} รายการ)
							</Button>

							<Button href="../products" variant="outline" block>
								<Icon icon="mdi:package-variant" class="w-5 h-5 mr-2" />
								ดูสินค้าสต็อกต่ำ ({stats.lowStockProducts} รายการ)
							</Button>

							<div class="divider"></div>

							<Button href="../products/create" color="primary" block>
								<Icon icon="mdi:plus" class="w-5 h-5 mr-2" />
								เพิ่มสินค้าใหม่
							</Button>

							<Button href="../categories" variant="outline" block>
								<Icon icon="mdi:tag-multiple" class="w-5 h-5 mr-2" />
								จัดการหมวดหมู่
							</Button>
						</div>
					</div>
				</Card>
			</div>

			<!-- Recommendations -->
			{#if stats.outOfStockProducts > 0 || stats.lowStockProducts > 0}
				<Card title="คำแนะนำ" size="full">
					<div class="space-y-3">
						{#if stats.outOfStockProducts > 0}
							<div class="alert alert-error">
								<Icon icon="mdi:alert-circle" class="w-5 h-5" />
								<div>
									<h4 class="font-bold">สินค้าหมดสต็อก!</h4>
									<div class="text-xs">
										มีสินค้า {stats.outOfStockProducts} รายการที่หมดสต็อก ควรเติมสต็อกหรือปิดการขายชั่วคราว
									</div>
								</div>
							</div>
						{/if}

						{#if stats.lowStockProducts > 0}
							<div class="alert alert-warning">
								<Icon icon="mdi:alert" class="w-5 h-5" />
								<div>
									<h4 class="font-bold">สินค้าสต็อกต่ำ</h4>
									<div class="text-xs">
										มีสินค้า {stats.lowStockProducts} รายการที่มีสต็อกต่ำ ควรเติมสต็อกเพื่อป้องกันการขาดแคลน
									</div>
								</div>
							</div>
						{/if}

						{#if stats.totalProducts - stats.activeProducts > 0}
							<div class="alert alert-info">
								<Icon icon="mdi:information" class="w-5 h-5" />
								<div>
									<h4 class="font-bold">สินค้าที่ปิดขาย</h4>
									<div class="text-xs">
										มีสินค้า {stats.totalProducts - stats.activeProducts} รายการที่ปิดขาย ตรวจสอบว่าควรเปิดขายหรือลบออก
									</div>
								</div>
							</div>
						{/if}
					</div>
				</Card>
			{/if}
		{/if}
	</div>
{/if}