# หน้ารายการสินค้า (Products Page)

หน้านี้แสดงรายการสินค้าทั้งหมดของไซต์ พร้อมสถิติและฟีเจอร์การจัดการต่างๆ

## ฟีเจอร์

### 1. สถิติสินค้า

- แสดงจำนวนสินค้าทั้งหมด
- สินค้าที่เปิดขาย
- สินค้าหมดสต็อก
- สินค้าสต็อกต่ำ

### 2. กราฟและชาร์ต

- กราฟยอดขายรายเดือน
- กราฟวงกลมแสดงสัดส่วนสินค้าตามหมวดหมู่

### 3. ตารางสินค้า

- แสดงรายการสินค้าทั้งหมด
- รูปภาพสินค้า (ถ้ามี)
- ข้อมูล SKU, ราคา, สต็อก
- สถานะสินค้า (เปิดขาย/ปิดขาย)
- ปุ่มจัดการ (ดู, แก้ไข, ลบ)

### 4. การแบ่งหน้า (Pagination)

- แสดงหน้าปัจจุบันและจำนวนหน้าทั้งหมด
- ปุ่มนำทางไปหน้าถัดไป/ก่อนหน้า

### 5. Server Actions

- `deleteProduct`: ลบสินค้า
- `updateStock`: อัปเดตสต็อกสินค้า

## การเชื่อมต่อ API

### Backend Endpoints

- `GET /product/dashboard/:siteId/list` - ดึงรายการสินค้า
- `GET /product/dashboard/:siteId/stats` - ดึงสถิติสินค้า
- `DELETE /product/dashboard/:siteId/delete/:productId` - ลบสินค้า
- `PUT /product/dashboard/:siteId/stock/:productId` - อัปเดตสต็อก

### Service

- `productService` - จัดการการเชื่อมต่อกับ API สินค้า
- `categoryService` - จัดการการเชื่อมต่อกับ API หมวดหมู่

## การใช้งาน

1. **ดูรายการสินค้า**: หน้าแสดงสินค้าทั้งหมดพร้อมข้อมูลพื้นฐาน
2. **ลบสินค้า**: คลิกปุ่มลบและยืนยันการลบ
3. **ดูสถิติ**: ดูสถิติสินค้าในรูปแบบการ์ดและกราฟ
4. **นำทาง**: ใช้ปุ่มแบ่งหน้าเพื่อดูสินค้าในหน้าถัดไป

## การปรับแต่ง

### เพิ่มฟิลเตอร์

สามารถเพิ่มฟิลเตอร์เพิ่มเติมใน `+page.server.ts`:

- ค้นหาตามชื่อสินค้า
- กรองตามหมวดหมู่
- กรองตามสถานะ

### เพิ่มการเรียงลำดับ

สามารถเพิ่มตัวเลือกการเรียงลำดับ:

- เรียงตามวันที่สร้าง
- เรียงตามราคา
- เรียงตามชื่อ

## การจัดการข้อผิดพลาด

- แสดงข้อความแจ้งเตือนเมื่อไม่มีสินค้า
- แสดงข้อความเมื่อเกิดข้อผิดพลาดในการโหลดข้อมูล
- ยืนยันก่อนลบสินค้า
- แสดงผลลัพธ์จาก server actions
