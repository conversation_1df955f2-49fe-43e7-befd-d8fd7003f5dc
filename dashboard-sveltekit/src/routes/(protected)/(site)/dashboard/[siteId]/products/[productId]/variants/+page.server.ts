import { productService } from '$lib/services/product';
import { error, fail } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ params, locals }) => {
  try {
    if (!locals.token || !locals.user) {
      throw error(401, 'กรุณาเข้าสู่ระบบ');
    }

    const { siteId, productId } = params;
    if (!siteId || !productId) {
      throw error(400, 'Site ID และ Product ID จำเป็น');
    }

    // Fetch product details
    const productResponse = await productService.getProduct(productId, siteId, locals.token);

    if (!productResponse.success) {
      throw error(404, productResponse.error || 'ไม่พบสินค้า');
    }

    return {
      product: productResponse.data,
    };
  }
  catch (err) {
    console.error('Error loading product variants page:', err);
    if (err instanceof Response) {
      throw err;
    }
    throw error(500, 'เกิดข้อผิดพลาดในการโหลดข้อมูล');
  }
};

export const actions: Actions = {
  addVariant: async ({ request, params, locals }) => {
    try {
      if (!locals.token || !locals.user) {
        return fail(401, { error: 'กรุณาเข้าสู่ระบบ' });
      }

      const { siteId, productId } = params;
      if (!siteId || !productId) {
        return fail(400, { error: 'Site ID และ Product ID จำเป็น' });
      }

      const formData = await request.formData();

      // Parse attributes from form
      const attributes: Record<string, string> = {};
      for (const [key, value] of formData.entries()) {
        if (key.startsWith('attributes.')) {
          const attrName = key.replace('attributes.', '');
          attributes[attrName] = value as string;
        }
      }

      const variantData = {
        name: formData.get('name') as string,
        sku: formData.get('sku') as string,
        price: parseFloat(formData.get('price') as string) || undefined,
        stock: parseInt(formData.get('stock') as string) || undefined,
        attributes,
        isActive: formData.get('isActive') === 'on',
      };

      // Validation
      if (!variantData.name?.trim()) {
        return fail(400, { error: 'กรุณากรอกชื่อตัวเลือกสินค้า' });
      }

      if (Object.keys(attributes).length === 0) {
        return fail(400, { error: 'กรุณากรอกคุณสมบัติของตัวเลือกสินค้า' });
      }

      // Call backend API to add variant
      const response = await fetch(`${process.env.API_BASE_URL}/product/dashboard/${siteId}/variant/${productId}/add`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${locals.token}`,
        },
        body: JSON.stringify(variantData),
      });

      const result = await response.json();

      if (result.success) {
        return { success: true, message: 'เพิ่มตัวเลือกสินค้าสำเร็จ' };
      }
      else {
        return fail(400, {
          error: result.error || 'เกิดข้อผิดพลาดในการเพิ่มตัวเลือกสินค้า',
        });
      }
    }
    catch (err) {
      console.error('Error adding variant:', err);
      return fail(500, { error: 'เกิดข้อผิดพลาดในการเพิ่มตัวเลือกสินค้า' });
    }
  },

  updateVariant: async ({ request, params, locals }) => {
    try {
      if (!locals.token || !locals.user) {
        return fail(401, { error: 'กรุณาเข้าสู่ระบบ' });
      }

      const { siteId, productId } = params;
      if (!siteId || !productId) {
        return fail(400, { error: 'Site ID และ Product ID จำเป็น' });
      }

      const formData = await request.formData();
      const variantId = formData.get('variantId') as string;

      if (!variantId) {
        return fail(400, { error: 'Variant ID จำเป็น' });
      }

      // Parse attributes from form
      const attributes: Record<string, string> = {};
      for (const [key, value] of formData.entries()) {
        if (key.startsWith('attributes.')) {
          const attrName = key.replace('attributes.', '');
          attributes[attrName] = value as string;
        }
      }

      const variantData = {
        name: formData.get('name') as string,
        sku: formData.get('sku') as string,
        price: parseFloat(formData.get('price') as string) || undefined,
        stock: parseInt(formData.get('stock') as string) || undefined,
        attributes,
        isActive: formData.get('isActive') === 'on',
      };

      // Call backend API to update variant
      const response = await fetch(
        `${process.env.API_BASE_URL}/product/dashboard/${siteId}/variant/${productId}/${variantId}`,
        {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${locals.token}`,
          },
          body: JSON.stringify(variantData),
        },
      );

      const result = await response.json();

      if (result.success) {
        return { success: true, message: 'อัปเดตตัวเลือกสินค้าสำเร็จ' };
      }
      else {
        return fail(400, {
          error: result.error || 'เกิดข้อผิดพลาดในการอัปเดตตัวเลือกสินค้า',
        });
      }
    }
    catch (err) {
      console.error('Error updating variant:', err);
      return fail(500, { error: 'เกิดข้อผิดพลาดในการอัปเดตตัวเลือกสินค้า' });
    }
  },

  deleteVariant: async ({ request, params, locals }) => {
    try {
      if (!locals.token || !locals.user) {
        return fail(401, { error: 'กรุณาเข้าสู่ระบบ' });
      }

      const { siteId, productId } = params;
      if (!siteId || !productId) {
        return fail(400, { error: 'Site ID และ Product ID จำเป็น' });
      }

      const formData = await request.formData();
      const variantId = formData.get('variantId') as string;

      if (!variantId) {
        return fail(400, { error: 'Variant ID จำเป็น' });
      }

      // Call backend API to delete variant
      const response = await fetch(
        `${process.env.API_BASE_URL}/product/dashboard/${siteId}/variant/${productId}/${variantId}`,
        {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${locals.token}`,
          },
        },
      );

      const result = await response.json();

      if (result.success) {
        return { success: true, message: 'ลบตัวเลือกสินค้าสำเร็จ' };
      }
      else {
        return fail(400, {
          error: result.error || 'เกิดข้อผิดพลาดในการลบตัวเลือกสินค้า',
        });
      }
    }
    catch (err) {
      console.error('Error deleting variant:', err);
      return fail(500, { error: 'เกิดข้อผิดพลาดในการลบตัวเลือกสินค้า' });
    }
  },
};
