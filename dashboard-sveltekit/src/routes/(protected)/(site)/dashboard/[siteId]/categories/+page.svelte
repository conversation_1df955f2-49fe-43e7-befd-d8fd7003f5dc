<script lang="ts">
	import Icon from '@iconify/svelte';
	import { enhance } from '$app/forms';
	import { page } from '$app/state';
	import { categoryService } from '$lib/services/category';
	import type { Category } from '$lib/types';
	import type { PageData } from './$types';
	import CategoryNode from './CategoryNode.svelte';

	// Using native fetch instead of ofetch

	const { data } = $props<{ data: PageData }>();

	const categories = $state(data.categories);
	let isLoading = $state(false);
	let message = $state('');
	let messageType = $state<'success' | 'error' | ''>('');
	let selectedCategory = $state<Category | null>(null);
	let showCreateModal = $state(false);
	let showEditModal = $state(false);
	let showDeleteModal = $state(false);
	let searchTerm = $state('');

	// Create category
	function openCreateModal() {
		selectedCategory = null;
		showCreateModal = true;
	}

	function closeCreateModal() {
		showCreateModal = false;
	}

	async function handleCreateCategory({ result }: any) {
		isLoading = true;
		try {
			if (result.type === 'success') {
				showMessage('สร้างหมวดหมู่สำเร็จ', 'success');
				closeCreateModal();
				// Refresh the page to get updated data
				window.location.reload();
			} else {
				showMessage(result.data?.error || 'เกิดข้อผิดพลาดในการสร้าง', 'error');
			}
		} catch (error) {
			console.error('Error creating category:', error);
			showMessage('เกิดข้อผิดพลาดในการสร้าง', 'error');
		} finally {
			isLoading = false;
		}
	}

	// Edit category
	function openEditModal(category: Category) {
		selectedCategory = category;
		showEditModal = true;
	}

	function closeEditModal() {
		selectedCategory = null;
		showEditModal = false;
	}

	async function handleEditCategory({ result }: any) {
		isLoading = true;
		try {
			if (result.type === 'success') {
				showMessage('อัปเดตหมวดหมู่สำเร็จ', 'success');
				closeEditModal();
				// Refresh the page to get updated data
				window.location.reload();
			} else {
				showMessage(result.data?.error || 'เกิดข้อผิดพลาดในการอัปเดต', 'error');
			}
		} catch (error) {
			console.error('Error updating category:', error);
			showMessage('เกิดข้อผิดพลาดในการอัปเดต', 'error');
		} finally {
			isLoading = false;
		}
	}

	// Delete category
	function openDeleteModal(category: Category) {
		selectedCategory = category;
		showDeleteModal = true;
	}

	function closeDeleteModal() {
		selectedCategory = null;
		showDeleteModal = false;
	}

	async function handleDeleteCategory() {
		if (!selectedCategory) return;

		isLoading = true;
		try {
			const formData = new FormData();
			formData.append('categoryId', selectedCategory._id);

			const response = await fetch('?/deleteCategory', {
				method: 'POST',
				body: formData,
			});

			if (!response.ok) {
				throw new Error(`HTTP ${response.status}: ${response.statusText}`);
			}

			const result = await response.json();

			if (result.type === 'success') {
				showMessage('ลบหมวดหมู่สำเร็จ', 'success');
				closeDeleteModal();
				// Refresh the page to get updated data
				window.location.reload();
			} else {
				showMessage(result.data?.error || 'เกิดข้อผิดพลาดในการลบ', 'error');
			}
		} catch (error) {
			console.error('Error deleting category:', error);
			showMessage('เกิดข้อผิดพลาดในการลบ', 'error');
		} finally {
			isLoading = false;
		}
	}

	function showMessage(msg: string, type: 'success' | 'error') {
		message = msg;
		messageType = type;
		setTimeout(() => {
			message = '';
			messageType = '';
		}, 3000);
	}

	function formatDate(dateString: string) {
		return new Date(dateString).toLocaleDateString('th-TH', {
			year: 'numeric',
			month: 'short',
			day: 'numeric',
			hour: '2-digit',
			minute: '2-digit',
		});
	}

	function getTargetLabel(target: string) {
		const labels = {
			product: 'สินค้า',
			category: 'หมวดหมู่',
			page: 'หน้าเว็บ',
			brand: 'แบรนด์',
			blog: 'บล็อก',
			news: 'ข่าวสาร',
		};
		return labels[target as keyof typeof labels] || target;
	}

	function getTargetColor(target: string) {
		const colors = {
			product: 'badge-primary',
			category: 'badge-secondary',
			page: 'badge-accent',
			brand: 'badge-info',
			blog: 'badge-warning',
			news: 'badge-error',
		};
		return colors[target as keyof typeof colors] || 'badge-neutral';
	}

	// Filter categories based on search
	let filteredCategories = categories;
	$effect(() => {
		filteredCategories = categories.filter(
			(category: Category) =>
				category.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
				getTargetLabel(category.target).toLowerCase().includes(searchTerm.toLowerCase())
		);
	});

	// Build category tree
	let categoryTree = $state(categoryService.buildCategoryTree(filteredCategories));
	$effect(() => {
		categoryTree = categoryService.buildCategoryTree(filteredCategories);
	});
</script>

<svelte:head>
	<title>จัดการหมวดหมู่ - {page.data.site?.name || 'Dashboard'}</title>
</svelte:head>

<div class="container mx-auto p-6">
	<!-- Header -->
	<div class="flex justify-between items-center mb-6">
		<div>
			<h1 class="text-2xl font-bold text-gray-900">จัดการหมวดหมู่</h1>
			<p class="text-gray-600">จัดการหมวดหมู่สินค้าและเนื้อหาต่างๆ</p>
		</div>
		<div class="flex gap-2">
			<button class="btn btn-primary" onclick={openCreateModal} disabled={isLoading}>
				<Icon icon="mdi:plus" class="w-4 h-4" />
				เพิ่มหมวดหมู่
			</button>
			<button class="btn btn-outline" onclick={() => window.location.reload()} disabled={isLoading}>
				<Icon icon="mdi:refresh" class="w-4 h-4" />
				รีเฟรช
			</button>
		</div>
	</div>

	<!-- Search -->
	<div class="card bg-base-100 shadow-sm mb-6">
		<div class="card-body">
			<div class="form-control">
				<label class="label" for="search-category">
					<span class="label-text">ค้นหาหมวดหมู่</span>
				</label>
				<div class="input-group">
					<input
						id="search-category"
						type="text"
						placeholder="ค้นหาด้วยชื่อหมวดหมู่หรือประเภท"
						class="input input-bordered flex-1"
						bind:value={searchTerm}
					/>
					<button class="btn btn-square btn-primary">
						<Icon icon="mdi:magnify" class="w-4 h-4" />
					</button>
				</div>
			</div>
		</div>
	</div>

	<!-- Message -->
	{#if message}
		<div class="alert alert-{messageType === 'success' ? 'success' : 'error'} mb-6">
			<Icon
				icon={messageType === 'success' ? 'mdi:check-circle' : 'mdi:alert-circle'}
				class="w-5 h-5"
			/>
			<span>{message}</span>
		</div>
	{/if}

	<!-- Categories List -->
	<div class="card bg-base-100 shadow-sm">
		<div class="card-body">
			{#if categories.length === 0}
				<div class="text-center py-8">
					<Icon icon="mdi:folder-outline" class="w-16 h-16 text-gray-400 mx-auto mb-4" />
					<h3 class="text-lg font-semibold text-gray-600 mb-2">ไม่พบหมวดหมู่</h3>
					<p class="text-gray-500">ยังไม่มีหมวดหมู่ในระบบ</p>
					<button class="btn btn-primary mt-4" onclick={openCreateModal}>
						<Icon icon="mdi:plus" class="w-4 h-4" />
						เพิ่มหมวดหมู่แรก
					</button>
				</div>
			{:else}
				<div class="space-y-2">
					{#each categoryTree as category (category._id)}
						<CategoryNode {category} level={0} {openEditModal} {openDeleteModal} />
					{/each}
				</div>
			{/if}
		</div>
	</div>
</div>

<!-- Create Category Modal -->
{#if showCreateModal}
	<div class="modal modal-open">
		<div class="modal-box">
			<h3 class="font-bold text-lg mb-4">เพิ่มหมวดหมู่ใหม่</h3>
			<form method="POST" action="?/createCategory" use:enhance={handleCreateCategory}>
				<div class="form-control mb-4">
					<label class="label" for="create-category-name">
						<span class="label-text">ชื่อหมวดหมู่</span>
					</label>
					<input
						id="create-category-name"
						type="text"
						name="name"
						class="input input-bordered"
						required
						placeholder="ชื่อหมวดหมู่"
					/>
				</div>

				<div class="form-control mb-4">
					<label class="label" for="create-category-target">
						<span class="label-text">ประเภท</span>
					</label>
					<select id="create-category-target" name="target" class="select select-bordered">
						<option value="product">สินค้า</option>
						<option value="category">หมวดหมู่</option>
						<option value="page">หน้าเว็บ</option>
						<option value="brand">แบรนด์</option>
						<option value="blog">บล็อก</option>
						<option value="news">ข่าวสาร</option>
					</select>
				</div>

				<div class="form-control mb-4">
					<label class="label" for="create-category-parent">
						<span class="label-text">หมวดหมู่หลัก (ไม่บังคับ)</span>
					</label>
					<select id="create-category-parent" name="parentId" class="select select-bordered">
						<option value="">ไม่มีหมวดหมู่หลัก</option>
						{#each categories.filter((c: Category) => c.target === 'category') as parentCategory}
							<option value={parentCategory._id}>{parentCategory.name}</option>
						{/each}
					</select>
				</div>

				<div class="form-control mb-6">
					<label class="label" for="create-category-cover">
						<span class="label-text">รูปภาพ (ไม่บังคับ)</span>
					</label>
					<input
						id="create-category-cover"
						type="text"
						name="cover"
						class="input input-bordered"
						placeholder="URL รูปภาพ"
					/>
				</div>

				<div class="modal-action">
					<button
						type="button"
						class="btn btn-outline"
						onclick={closeCreateModal}
						disabled={isLoading}
					>
						ยกเลิก
					</button>
					<button type="submit" class="btn btn-primary" disabled={isLoading}>
						{isLoading ? 'กำลังสร้าง...' : 'สร้างหมวดหมู่'}
					</button>
				</div>
			</form>
		</div>
	</div>
{/if}

<!-- Edit Category Modal -->
{#if showEditModal && selectedCategory}
	<div class="modal modal-open">
		<div class="modal-box">
			<h3 class="font-bold text-lg mb-4">แก้ไขหมวดหมู่</h3>
			<form method="POST" action="?/updateCategory" use:enhance={handleEditCategory}>
				<input type="hidden" name="categoryId" value={selectedCategory._id} />

				<div class="form-control mb-4">
					<label class="label" for="edit-category-name">
						<span class="label-text">ชื่อหมวดหมู่</span>
					</label>
					<input
						id="edit-category-name"
						type="text"
						name="name"
						class="input input-bordered"
						value={selectedCategory.name}
						required
					/>
				</div>

				<div class="form-control mb-4">
					<label class="label" for="edit-category-target">
						<span class="label-text">ประเภท</span>
					</label>
					<select id="edit-category-target" name="target" class="select select-bordered">
						<option value="product" selected={selectedCategory.target === 'product'}>สินค้า</option>
						<option value="category" selected={selectedCategory.target === 'category'}
							>หมวดหมู่</option
						>
						<option value="page" selected={selectedCategory.target === 'page'}>หน้าเว็บ</option>
						<option value="brand" selected={selectedCategory.target === 'brand'}>แบรนด์</option>
						<option value="blog" selected={selectedCategory.target === 'blog'}>บล็อก</option>
						<option value="news" selected={selectedCategory.target === 'news'}>ข่าวสาร</option>
					</select>
				</div>

				<div class="form-control mb-4">
					<label class="label" for="edit-category-parent">
						<span class="label-text">หมวดหมู่หลัก (ไม่บังคับ)</span>
					</label>
					<select id="edit-category-parent" name="parentId" class="select select-bordered">
						<option value="">ไม่มีหมวดหมู่หลัก</option>
						{#each categories.filter((c: Category) => c._id !== selectedCategory?._id && c.target === 'category') as parentCategory}
							<option
								value={parentCategory._id}
								selected={selectedCategory?.parentId === parentCategory._id}
							>
								{parentCategory.name}
							</option>
						{/each}
					</select>
				</div>

				<div class="form-control mb-6">
					<label class="label" for="edit-category-cover">
						<span class="label-text">รูปภาพ (ไม่บังคับ)</span>
					</label>
					<input
						id="edit-category-cover"
						type="text"
						name="cover"
						class="input input-bordered"
						value={selectedCategory.cover || ''}
						placeholder="URL รูปภาพ"
					/>
				</div>

				<div class="modal-action">
					<button
						type="button"
						class="btn btn-outline"
						onclick={closeEditModal}
						disabled={isLoading}
					>
						ยกเลิก
					</button>
					<button type="submit" class="btn btn-primary" disabled={isLoading}>
						{isLoading ? 'กำลังบันทึก...' : 'บันทึก'}
					</button>
				</div>
			</form>
		</div>
	</div>
{/if}

<!-- Delete Category Modal -->
{#if showDeleteModal && selectedCategory}
	<div class="modal modal-open">
		<div class="modal-box">
			<h3 class="font-bold text-lg mb-4">ยืนยันการลบหมวดหมู่</h3>
			<p class="mb-6">
				คุณต้องการลบหมวดหมู่ <strong>{selectedCategory.name}</strong> หรือไม่? การดำเนินการนี้ไม่สามารถยกเลิกได้
			</p>

			<div class="modal-action">
				<button class="btn btn-outline" onclick={closeDeleteModal} disabled={isLoading}>
					ยกเลิก
				</button>
				<button class="btn btn-error" onclick={handleDeleteCategory} disabled={isLoading}>
					{isLoading ? 'กำลังลบ...' : 'ลบหมวดหมู่'}
				</button>
			</div>
		</div>
	</div>
{/if}
