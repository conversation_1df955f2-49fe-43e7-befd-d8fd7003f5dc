<script lang="ts">
	import Icon from '@iconify/svelte';
	import CategoryNode from './CategoryNode.svelte';

	const { category, level = 0, openEditModal, openDeleteModal } = $props();
</script>

<div
	class="flex items-center justify-between p-3 bg-base-100 rounded-lg mb-2 shadow-sm"
	style="margin-left: {level * 16}px;"
>
	<div class="flex items-center gap-3">
		<div class="avatar placeholder">
			<div class="bg-neutral text-neutral-content rounded-full w-10">
				<span class="text-sm">{category.name.charAt(0).toUpperCase()}</span>
			</div>
		</div>
		<div>
			<div class="font-semibold">{category.name}</div>
			<div class="text-sm opacity-60">
				<span class="badge badge-primary badge-sm">{category.target}</span>
				{#if category.cover}
					<span class="ml-2 text-xs">มีรูปภาพ</span>
				{/if}
			</div>
		</div>
	</div>
	<div class="flex gap-2">
		<button class="btn btn-sm btn-outline" onclick={() => openEditModal(category)}>
			<Icon icon="mdi:pencil" class="w-4 h-4" /> แก้ไข
		</button>
		<button class="btn btn-sm btn-error" onclick={() => openDeleteModal(category)}>
			<Icon icon="mdi:delete" class="w-4 h-4" /> ลบ
		</button>
	</div>
</div>
{#if category.children && category.children.length > 0}
	{#each category.children as child}
		<CategoryNode category={child} level={level + 1} {openEditModal} {openDeleteModal} />
	{/each}
{/if}
