<script lang="ts">
	import Icon from '@iconify/svelte';
	import SEO from '$lib/components/layout/SEO.svelte';
	import Chart from '$lib/components/ui/Chart.svelte';
	import type { DashboardData } from '$lib/services/dashboard';
	import { authStore } from '$lib/stores/auth.svelte';
	import { siteStore } from '$lib/stores/site.svelte';

	const { data } = $props<{
		data: {
			user?: any;
			site?: any;
			error?: string;
			siteId: string;
			dashboardData: DashboardData;
			period?: string;
			startDate?: string;
			endDate?: string;
		};
	}>();

	const site = $derived(data?.site || $siteStore.site);
	const dashboardData = $derived(data?.dashboardData);

	// รอให้ authStore initialized
	const isReady = $derived(authStore.isInitialized);
	const isAuthenticated = $derived(authStore.isAuthenticated);

	// ตรวจสอบ error จาก server
	const hasError = $derived(data?.error);

	 
	const chartOptions = {
		plugins: {
			legend: {
				display: true,
				position: 'top' as const,
			},
		},
		scales: {
			y: {
				beginAtZero: true,
			},
		},
	};

	const pieOptions = {
		plugins: {
			legend: {
				display: true,
				position: 'bottom' as const,
			},
		},
	};

	// สถิติสรุป - ใช้ข้อมูลจาก server
	const stats = $derived([
		{
			title: 'ยอดขายรวม',
			value: `฿${dashboardData?.stats.totalSales.toLocaleString() || '0'}`,
			change: dashboardData?.stats.salesChange || '+0%',
			changeType: 'positive',
			icon: 'mdi:currency-usd',
			color: 'bg-blue-500',
		},
		{
			title: 'คำสั่งซื้อ',
			value: dashboardData?.stats.totalOrders.toLocaleString() || '0',
			change: dashboardData?.stats.ordersChange || '+0%',
			changeType: 'positive',
			icon: 'mdi:shopping-cart',
			color: 'bg-green-500',
		},
		{
			title: 'ผู้เข้าชม',
			value: dashboardData?.stats.totalVisitors.toLocaleString() || '0',
			change: dashboardData?.stats.visitorsChange || '+0%',
			changeType: 'positive',
			icon: 'mdi:eye',
			color: 'bg-purple-500',
		},
		{
			title: 'สินค้าขายดี',
			value: dashboardData?.stats.totalProducts.toLocaleString() || '0',
			change: dashboardData?.stats.productsChange || '+0%',
			changeType: 'positive',
			icon: 'mdi:star',
			color: 'bg-orange-500',
		},
	]);

	// กิจกรรมล่าสุด - ใช้ข้อมูลจาก server
	const recentActivities = $derived(dashboardData?.recentActivities || []);
</script>

<SEO
	title="Dashboard - จัดการเว็บไซต์ของคุณ"
	description="จัดการเว็บไซต์ร้านค้าออนไลน์ของคุณ สร้างเว็บไซต์ใหม่ เติมเงิน และดูสถิติการขาย"
	keywords="dashboard, จัดการเว็บไซต์, ร้านค้าออนไลน์, สถิติการขาย, เว็บไซต์"
	url="/dashboard"
	noindex={true}
/>

{#if !isReady}
	<div class="flex items-center justify-center min-h-screen">
		<div class="loading loading-spinner loading-lg"></div>
	</div>
{:else if hasError}
	<div class="flex items-center justify-center min-h-screen">
		<div class="card bg-base-100 shadow-lg max-w-md">
			<div class="card-body text-center">
				<div
					class="w-16 h-16 bg-warning/20 rounded-full flex items-center justify-center mx-auto mb-4"
				>
					<Icon icon="mdi:alert-circle" class="w-8 h-8 text-warning" />
				</div>
				<h2 class="text-xl font-bold text-base-content mb-2">เกิดข้อผิดพลาด</h2>
				<p class="text-base-content/60 mb-4">{hasError}</p>
				<button class="btn btn-primary" onclick={() => window.location.reload()}>
					ลองใหม่อีกครั้ง
				</button>
			</div>
		</div>
	</div>
{:else}
	<div class="space-y-6 sm:space-y-6">
		<!-- Header -->
		<div class="flex justify-between items-center">
			<div>
				<h1 class="text-3xl font-bold text-base-content">
					{#if site}
						Dashboard - {site.name}
					{:else}
						Dashboard
					{/if}
				</h1>
				<p class="text-base-content/60 mt-1">
					ยินดีต้อนรับกลับ! นี่คือภาพรวมของร้านค้าออนไลน์ของคุณ
				</p>
			</div>
			<div class="flex gap-2">
				<button class="btn btn-primary">
					<Icon icon="mdi:plus" class="w-5 h-5" />
					เพิ่มสินค้า
				</button>
				<button class="btn btn-outline">
					<Icon icon="mdi:cog" class="w-5 h-5" />
					ตั้งค่า
				</button>
			</div>
		</div>

		<!-- Stats Cards -->
		<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
			{#each stats as stat}
				<div class="card bg-base-100 shadow-lg">
					<div class="card-body">
						<div class="flex items-center justify-between">
							<div>
								<p class="text-sm text-base-content/60">{stat.title}</p>
								<p class="text-2xl font-bold text-base-content">{stat.value}</p>
								<p
									class="text-sm {stat.changeType === 'positive'
										? 'text-green-500'
										: 'text-red-500'}"
								>
									{stat.change} จากเดือนที่แล้ว
								</p>
							</div>
							<div class="w-12 h-12 rounded-full {stat.color} flex items-center justify-center">
								<Icon icon={stat.icon} class="w-6 h-6 text-white" />
							</div>
						</div>
					</div>
				</div>
			{/each}
		</div>

		<!-- Charts Row -->
		<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
			<!-- Sales Chart -->
			<div class="card bg-base-100 shadow-lg">
				<div class="card-body">
					<h3 class="card-title">
						<Icon icon="mdi:chart-line" class="w-5 h-5" />
						ยอดขายรายเดือน
					</h3>
					{#if dashboardData?.chartData?.sales}
						<Chart data={dashboardData.chartData.sales} options={chartOptions} height="300px" />
					{:else}
						<div class="flex items-center justify-center h-[300px]">
							<div class="loading loading-spinner loading-lg"></div>
						</div>
					{/if}
				</div>
			</div>

			<!-- Visitors Chart -->
			<div class="card bg-base-100 shadow-lg">
				<div class="card-body">
					<h3 class="card-title">
						<Icon icon="mdi:eye" class="w-5 h-5" />
						ผู้เข้าชมรายเดือน
					</h3>
					{#if dashboardData?.chartData?.visitors}
						<Chart data={dashboardData.chartData.visitors} options={chartOptions} height="300px" />
					{:else}
						<div class="flex items-center justify-center h-[300px]">
							<div class="loading loading-spinner loading-lg"></div>
						</div>
					{/if}
				</div>
			</div>
		</div>

		<!-- Charts Row 2 -->
		<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
			<!-- Orders Status -->
			<div class="card bg-base-100 shadow-lg">
				<div class="card-body">
					<h3 class="card-title">
						<Icon icon="mdi:shopping-cart" class="w-5 h-5" />
						สถานะคำสั่งซื้อ
					</h3>
					{#if dashboardData?.chartData?.orders}
						<Chart
							type="doughnut"
							data={dashboardData.chartData.orders}
							options={pieOptions}
							height="300px"
						/>
					{:else}
						<div class="flex items-center justify-center h-[300px]">
							<div class="loading loading-spinner loading-lg"></div>
						</div>
					{/if}
				</div>
			</div>

			<!-- Product Categories -->
			<div class="card bg-base-100 shadow-lg">
				<div class="card-body">
					<h3 class="card-title">
						<Icon icon="mdi:package" class="w-5 h-5" />
						ประเภทสินค้า
					</h3>
					{#if dashboardData?.chartData?.products}
						<Chart
							type="pie"
							data={dashboardData.chartData.products}
							options={pieOptions}
							height="300px"
						/>
					{:else}
						<div class="flex items-center justify-center h-[300px]">
							<div class="loading loading-spinner loading-lg"></div>
						</div>
					{/if}
				</div>
			</div>
		</div>

		<!-- Recent Activities & Quick Actions -->
		<div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
			<!-- Recent Activities -->
			<div class="card bg-base-100 shadow-lg lg:col-span-2">
				<div class="card-body">
					<h3 class="card-title">
						<Icon icon="mdi:clock-outline" class="w-5 h-5" />
						กิจกรรมล่าสุด
					</h3>
					<div class="space-y-4">
						{#each recentActivities as activity}
							<div class="flex items-center gap-3 p-3 rounded-lg bg-base-200">
								<div class="w-10 h-10 rounded-full bg-base-300 flex items-center justify-center">
									<Icon icon={activity.icon} class="w-5 h-5 {activity.color}" />
								</div>
								<div class="flex-1">
									<p class="font-medium text-base-content">{activity.message}</p>
									<p class="text-sm text-base-content/60">{activity.time}</p>
								</div>
							</div>
						{/each}
					</div>
				</div>
			</div>

			<!-- Quick Actions -->
			<div class="card bg-base-100 shadow-lg">
				<div class="card-body">
					<h3 class="card-title">
						<Icon icon="mdi:lightning-bolt" class="w-5 h-5" />
						การดำเนินการด่วน
					</h3>
					<div class="space-y-3">
						<button class="btn btn-primary btn-sm w-full justify-start">
							<Icon icon="mdi:plus" class="w-4 h-4" />
							เพิ่มสินค้าใหม่
						</button>
						<button class="btn btn-outline btn-sm w-full justify-start">
							<Icon icon="mdi:shopping-cart" class="w-4 h-4" />
							ดูคำสั่งซื้อ
						</button>
						<button class="btn btn-outline btn-sm w-full justify-start">
							<Icon icon="mdi:account-group" class="w-4 h-4" />
							จัดการลูกค้า
						</button>
						<button class="btn btn-outline btn-sm w-full justify-start">
							<Icon icon="mdi:chart-bar" class="w-4 h-4" />
							ดูรายงาน
						</button>
						<button class="btn btn-outline btn-sm w-full justify-start">
							<Icon icon="mdi:cog" class="w-4 h-4" />
							ตั้งค่าร้านค้า
						</button>
					</div>
				</div>
			</div>
		</div>
	</div>
{/if}
