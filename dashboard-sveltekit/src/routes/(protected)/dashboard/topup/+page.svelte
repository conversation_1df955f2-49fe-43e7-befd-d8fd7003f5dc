<script lang="ts">
	// ตัวแปรสำหรับเก็บข้อมูล (ใช้ $state สำหรับ Svelte 5)
	let selectedPaymentMethod = $state('');
	let amount = $state('');
	let currentStep = $state(1); // 1 = เลือกช่องทาง, 2 = กรอกยอด, 3 = แสดง QR/รายละเอียด
	let qrCodeUrl = $state('');
	let isLoading = $state(false);

	// ช่องทางการชำระเงิน
	const paymentMethods = [
		{
			id: 'promptpay',
			name: 'PromptPay',
			icon: '💳',
			description: 'ชำระผ่าน QR Code PromptPay',
			hasQR: true,
		},
		{
			id: 'labnet',
			name: 'LabNet Lao',
			icon: '🏦',
			description: 'ชำระผ่าน LabNet Lao',
			hasQR: true,
		},
		{
			id: 'truemoney',
			name: 'True ซองอั่งเปา',
			icon: '🧧',
			description: 'ชำระผ่าน True Wallet',
			hasQR: false,
		},
		{
			id: 'banktransfer',
			name: 'โอนธนาคาร',
			icon: '🏛️',
			description: 'โอนเงินผ่านธนาคาร',
			hasQR: false,
		},
	];

	// ฟังก์ชันเลือกช่องทางการชำระ
	function selectPaymentMethod(methodId: string) {
		selectedPaymentMethod = methodId;
		currentStep = 2;
	}

	// ฟังก์ชันไปขั้นตอนถัดไป
	async function proceedToPayment() {
		if (!amount || parseFloat(amount) <= 0) {
			alert('กรุณากรอกยอดเงินที่ถูกต้อง');
			return;
		}

		isLoading = true;

		const selectedMethod = paymentMethods.find(m => m.id === selectedPaymentMethod);

		if (selectedMethod?.hasQR) {
			// สำหรับ PromptPay และ LabNet จะสร้าง QR Code
			await generateQRCode();
		}

		currentStep = 3;
		isLoading = false;
	}

	// ฟังก์ชันสร้าง QR Code (จำลอง)
	async function generateQRCode() {
		// จำลองการสร้าง QR Code
		await new Promise(resolve => setTimeout(resolve, 1000));

		// ในการใช้งานจริง คุณจะต้องเรียก API เพื่อสร้าง QR Code
		qrCodeUrl = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=payment_${selectedPaymentMethod}_${amount}_${Date.now()}`;
	}

	// ฟังก์ชันกลับไปขั้นตอนก่อนหน้า
	function goBack() {
		if (currentStep > 1) {
			currentStep--;
		}
	}

	// ฟังก์ชันเริ่มใหม่
	function startOver() {
		selectedPaymentMethod = '';
		amount = '';
		currentStep = 1;
		qrCodeUrl = '';
	}

	// ฟังก์ชันคัดลอกข้อมูล
	function copyToClipboard(text: string) {
		navigator.clipboard.writeText(text);
		alert('คัดลอกแล้ว!');
	}
</script>

<div class="max-w-2xl mx-auto p-4">
	<div class="card bg-base-100 shadow-xl">
		<!-- Header -->
		<div class="bg-gradient-to-r from-primary to-secondary text-primary-content p-8 text-center">
			<h1 class="text-2xl font-bold mb-4">เติมเงินเข้าระบบ</h1>
			<div class="flex items-center justify-center gap-2">
				<div
					class="w-8 h-8 rounded-full flex items-center justify-center font-bold transition-all duration-300 {currentStep >=
					1
						? 'bg-base-100 text-primary'
						: 'bg-white/30'}"
				>
					1
				</div>
				<div
					class="w-10 h-0.5 transition-all duration-300 {currentStep >= 2
						? 'bg-base-100'
						: 'bg-white/30'}"
				></div>
				<div
					class="w-8 h-8 rounded-full flex items-center justify-center font-bold transition-all duration-300 {currentStep >=
					2
						? 'bg-base-100 text-primary'
						: 'bg-white/30'}"
				>
					2
				</div>
				<div
					class="w-10 h-0.5 transition-all duration-300 {currentStep >= 3
						? 'bg-base-100'
						: 'bg-white/30'}"
				></div>
				<div
					class="w-8 h-8 rounded-full flex items-center justify-center font-bold transition-all duration-300 {currentStep >=
					3
						? 'bg-base-100 text-primary'
						: 'bg-white/30'}"
				>
					3
				</div>
			</div>
		</div>

		<!-- Step 1: เลือกช่องทางการชำระ -->
		{#if currentStep === 1}
			<div class="card-body p-8">
				<h2 class="text-2xl font-bold text-center mb-2">เลือกช่องทางการชำระเงิน</h2>
				<p class="text-center text-base-content/70 mb-8">
					กรุณาเลือกวิธีการชำระเงินที่สะดวกสำหรับคุณ
				</p>

				<div class="grid gap-4 md:grid-cols-2">
					{#each paymentMethods as method}
						<button
							type="button"
							class="group relative overflow-hidden rounded-2xl border-2 border-base-300 bg-gradient-to-br from-base-100 to-base-200 p-6 transition-all duration-300 hover:border-primary hover:shadow-xl hover:-translate-y-2 cursor-pointer text-left w-full"
							onclick={() => selectPaymentMethod(method.id)}
							onkeydown={e => e.key === 'Enter' && selectPaymentMethod(method.id)}
						>
							<!-- Background Pattern -->
							<div
								class="absolute inset-0 bg-gradient-to-br from-primary/5 to-secondary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
							></div>

							<!-- Content -->
							<div class="relative z-10">
								<!-- Icon และ Badge -->
								<div class="flex items-start justify-between mb-4">
									<div
										class="flex items-center justify-center w-16 h-16 rounded-2xl bg-gradient-to-br from-primary/10 to-secondary/10 group-hover:from-primary/20 group-hover:to-secondary/20 transition-all duration-300"
									>
										<span class="text-3xl">{method.icon}</span>
									</div>
									{#if method.hasQR}
										<div class="badge badge-success badge-sm">QR Code</div>
									{:else}
										<div class="badge badge-info badge-sm">Manual</div>
									{/if}
								</div>

								<!-- Title และ Description -->
								<div class="mb-4">
									<h3
										class="text-lg font-bold text-base-content group-hover:text-primary transition-colors duration-300"
									>
										{method.name}
									</h3>
									<p class="text-sm text-base-content/70 mt-1">
										{method.description}
									</p>
								</div>

								<!-- Features -->
								<div class="space-y-2 mb-4">
									{#if method.hasQR}
										<div class="flex items-center text-xs text-success">
											<svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
												<path
													fill-rule="evenodd"
													d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
													clip-rule="evenodd"
												></path>
											</svg>
											ชำระทันที
										</div>
										<div class="flex items-center text-xs text-success">
											<svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
												<path
													fill-rule="evenodd"
													d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
													clip-rule="evenodd"
												></path>
											</svg>
											ปลอดภัย
										</div>
									{:else}
										<div class="flex items-center text-xs text-info">
											<svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
												<path
													fill-rule="evenodd"
													d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
													clip-rule="evenodd"
												></path>
											</svg>
											ตรวจสอบด้วยตนเอง
										</div>
										<div class="flex items-center text-xs text-info">
											<svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
												<path
													fill-rule="evenodd"
													d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z"
													clip-rule="evenodd"
												></path>
											</svg>
											ใช้เวลา 5-15 นาที
										</div>
									{/if}
								</div>

								<!-- Action Button -->
								<div class="flex items-center justify-between">
									<span class="text-xs text-base-content/50">คลิกเพื่อเลือก</span>
									<div
										class="flex items-center justify-center w-8 h-8 rounded-full bg-primary/10 group-hover:bg-primary group-hover:text-primary-content transition-all duration-300"
									>
										<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path
												stroke-linecap="round"
												stroke-linejoin="round"
												stroke-width="2"
												d="M9 5l7 7-7 7"
											></path>
										</svg>
									</div>
								</div>
							</div>

							<!-- Hover Effect -->
							<div
								class="absolute inset-0 rounded-2xl bg-gradient-to-r from-primary/0 to-secondary/0 group-hover:from-primary/5 group-hover:to-secondary/5 transition-all duration-300"
							></div>
						</button>
					{/each}
				</div>

				<!-- Popular Methods -->
				<div class="mt-8 text-center">
					<p class="text-sm text-base-content/50 mb-3">วิธีการยอดนิยม</p>
					<div class="flex justify-center gap-2">
						<div class="badge badge-outline badge-sm">PromptPay</div>
						<div class="badge badge-outline badge-sm">โอนธนาคาร</div>
					</div>
				</div>
			</div>
		{/if}

		<!-- Step 2: กรอกยอดเงิน -->
		{#if currentStep === 2}
			{@const method = paymentMethods.find(m => m.id === selectedPaymentMethod)}
			<div class="card-body">
				<button class="btn btn-ghost btn-sm mb-4" onclick={goBack}> ← กลับ </button>

				<h2 class="card-title text-xl mb-6">กรอกยอดเงินที่ต้องการเติม</h2>

				<div class="alert alert-info mb-6">
					<span class="text-2xl">{method?.icon}</span>
					<div>
						<div class="font-semibold">{method?.name}</div>
						<div class="text-sm opacity-70">
							{method?.description}
						</div>
					</div>
				</div>

				<div class="form-control mb-6">
					<label class="label" for="amount">
						<span class="label-text font-semibold">ยอดเงิน (บาท)</span>
					</label>
					<input
						id="amount"
						type="number"
						bind:value={amount}
						placeholder="0.00"
						min="1"
						step="0.01"
						class="input input-bordered input-lg text-center text-xl"
					/>

					<div class="grid grid-cols-2 md:grid-cols-4 gap-2 mt-4">
						<button class="btn btn-outline" onclick={() => (amount = '100')}>100</button>
						<button class="btn btn-outline" onclick={() => (amount = '500')}>500</button>
						<button class="btn btn-outline" onclick={() => (amount = '1000')}>1,000</button>
						<button class="btn btn-outline" onclick={() => (amount = '2000')}>2,000</button>
					</div>
				</div>

				<button
					class="btn btn-primary btn-lg w-full"
					onclick={proceedToPayment}
					disabled={!amount || isLoading}
				>
					{#if isLoading}
						<span class="loading loading-spinner"></span>
						กำลังดำเนินการ...
					{:else}
						ถัดไป
					{/if}
				</button>
			</div>
		{/if}

		<!-- Step 3: แสดงรายละเอียดการชำระ -->
		{#if currentStep === 3}
			{@const method = paymentMethods.find(m => m.id === selectedPaymentMethod)}
			<div class="card-body">
				<button class="btn btn-ghost btn-sm mb-4" onclick={goBack}> ← กลับ </button>

				<h2 class="card-title text-xl mb-6">ชำระเงิน</h2>

				<div class="bg-base-200 p-4 rounded-lg mb-6">
					<div class="flex justify-between items-center mb-2">
						<span class="text-sm opacity-70">ช่องทางการชำระ:</span>
						<span class="font-semibold">{method?.name}</span>
					</div>
					<div class="flex justify-between items-center pt-2 border-t border-base-300">
						<span class="text-sm opacity-70">ยอดเงิน:</span>
						<span class="text-lg font-bold text-primary"
							>{parseFloat(amount).toLocaleString()} บาท</span
						>
					</div>
				</div>

				<!-- สำหรับ PromptPay และ LabNet แสดง QR Code -->
				{#if method?.hasQR && qrCodeUrl}
					<div class="text-center mb-6">
						<h3 class="text-lg font-semibold mb-4">สแกน QR Code เพื่อชำระเงิน</h3>
						<div class="flex justify-center mb-4">
							<div class="p-4 bg-white rounded-lg border">
								<img src={qrCodeUrl} alt="QR Code สำหรับชำระเงิน" class="w-48 h-48" />
							</div>
						</div>
						<p class="text-sm opacity-70">
							เปิดแอปธนาคารหรือ {method.name} แล้วสแกน QR Code ด้านบน
						</p>
					</div>
				{/if}

				<!-- สำหรับ True Money -->
				{#if selectedPaymentMethod === 'truemoney'}
					<div class="mb-6">
						<h3 class="text-lg font-semibold mb-4">ชำระผ่าน True Wallet</h3>
						<div class="bg-base-200 p-4 rounded-lg space-y-3">
							<div class="flex items-center justify-between">
								<span>1. เปิดแอป True Money Wallet</span>
							</div>
							<div class="flex items-center justify-between">
								<span>2. เลือกเมนู "โอนเงิน"</span>
							</div>
							<div class="flex items-center justify-between">
								<span>3. กรอกหมายเลขโทรศัพท์: <strong>081-234-5678</strong></span>
								<button class="btn btn-xs btn-primary" onclick={() => copyToClipboard('0812345678')}
									>คัดลอก</button
								>
							</div>
							<div class="flex items-center justify-between">
								<span
									>4. กรอกยอดเงิน: <strong>{parseFloat(amount).toLocaleString()} บาท</strong></span
								>
							</div>
							<div class="flex items-center justify-between">
								<span>5. ใส่ข้อความ: <strong>TOPUP-{Date.now()}</strong></span>
								<button
									class="btn btn-xs btn-primary"
									onclick={() => copyToClipboard(`TOPUP-${Date.now()}`)}>คัดลอก</button
								>
							</div>
						</div>
					</div>
				{/if}

				<!-- สำหรับโอนธนาคาร -->
				{#if selectedPaymentMethod === 'banktransfer'}
					<div class="mb-6">
						<h3 class="text-lg font-semibold mb-4">โอนเงินผ่านธนาคาร</h3>
						<div class="space-y-4">
							<div class="card bg-success/10 border border-success/20">
								<div class="card-body p-4">
									<h4 class="font-semibold text-success mb-2">ธนาคารกสิกรไทย</h4>
									<div class="space-y-1">
										<div class="flex items-center justify-between">
											<span>ชื่อบัญชี: <strong>บริษัท ตัวอย่าง จำกัด</strong></span>
										</div>
										<div class="flex items-center justify-between">
											<span>เลขที่บัญชี: <strong>123-4-56789-0</strong></span>
											<button
												class="btn btn-xs btn-success"
												onclick={() => copyToClipboard('**********')}>คัดลอก</button
											>
										</div>
									</div>
								</div>
							</div>
							<div class="alert alert-warning">
								<svg
									xmlns="http://www.w3.org/2000/svg"
									class="stroke-current shrink-0 h-6 w-6"
									fill="none"
									viewBox="0 0 24 24"
									><path
										stroke-linecap="round"
										stroke-linejoin="round"
										stroke-width="2"
										d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.728-.833-2.498 0L3.732 16c-.77.833.192 2.5 1.732 2.5z"
									/></svg
								>
								<div>
									<div class="flex items-center justify-between w-full">
										<span
											><strong>หมายเหตุ:</strong>
											กรุณาระบุรหัสอ้างอิง
											<strong>REF-{Date.now()}</strong> ในการโอน</span
										>
										<button
											class="btn btn-xs btn-warning"
											onclick={() => copyToClipboard(`REF-${Date.now()}`)}>คัดลอก</button
										>
									</div>
								</div>
							</div>
						</div>
					</div>
				{/if}

				<div class="flex gap-4 mt-8">
					<button class="btn btn-outline flex-1" onclick={startOver}> เริ่มใหม่ </button>
					<button class="btn btn-primary flex-[2]"> ยืนยันการชำระเงิน </button>
				</div>
			</div>
		{/if}
	</div>
</div>
