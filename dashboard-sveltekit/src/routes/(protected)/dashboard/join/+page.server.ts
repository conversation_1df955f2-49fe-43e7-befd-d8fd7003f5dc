import { apiClient } from '$lib/api/client';
import { fail } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';

/**
 * ✅ HYBRID APPROACH: SvelteKit Route API + Service Pattern
 * - Route-level validation and error handling
 * - Direct API client usage for invitation management
 * - Consistent fail() responses
 * - Type-safe data flow
 */

interface Invitation {
  _id: string;
  siteId: string;
  siteName: string;
  fromUserId: string;
  fromUserName: string;
  fromUserEmail: string;
  role: 'owner' | 'admin' | 'editor' | 'viewer';
  message?: string;
  status: 'pending' | 'accepted' | 'rejected' | 'expired';
  createdAt: string;
  expiresAt: string;
}

export const load: PageServerLoad = async ({ locals }) => {
  // Auth check already done in layout
  try {
    // ✅ Load invitations for the user
    const response = await apiClient.request<{
      data: { invitations: Invitation[]; };
      message: string;
    }>('/invitations/received', {
      method: 'GET',
      headers: {
        Authorization: `Bearer ${locals.token}`,
      },
    });

    return {
      invitations: response.data?.invitations || [],
    };
  }
  catch (error) {
    console.error('Error loading invitations:', error);
    return {
      invitations: [],
      error: 'เกิดข้อผิดพลาดในการดึงข้อมูลคำเชิญ',
    };
  }
};

export const actions: Actions = {
  /**
   * ✅ Accept invitation
   */
  acceptInvitation: async ({ request, locals }) => {
    try {
      const data = await request.formData();
      const invitationId = data.get('invitationId')?.toString();

      if (!invitationId) {
        return fail(400, {
          error: 'ไม่พบรหัสคำเชิญ',
          type: 'accept',
        });
      }

      // Call API to accept invitation
      await apiClient.request<{
        data: any;
        message: string;
      }>(`/invitations/${invitationId}/accept`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${locals.token}`,
        },
      });

      return {
        success: true,
        message: 'เข้าร่วมทีมงานเรียบร้อยแล้ว',
        type: 'accept',
        invitationId,
      };
    }
    catch (error) {
      console.error('Accept invitation error:', error);
      return fail(500, {
        error: 'เกิดข้อผิดพลาดในการเข้าร่วมทีมงาน',
        type: 'accept',
      });
    }
  },

  /**
   * ✅ Reject invitation
   */
  rejectInvitation: async ({ request, locals }) => {
    try {
      const data = await request.formData();
      const invitationId = data.get('invitationId')?.toString();

      if (!invitationId) {
        return fail(400, {
          error: 'ไม่พบรหัสคำเชิญ',
          type: 'reject',
        });
      }

      // Call API to reject invitation
      await apiClient.request<{
        data: any;
        message: string;
      }>(`/invitations/${invitationId}/reject`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${locals.token}`,
        },
      });

      return {
        success: true,
        message: 'ปฏิเสธคำเชิญเรียบร้อยแล้ว',
        type: 'reject',
        invitationId,
      };
    }
    catch (error) {
      console.error('Reject invitation error:', error);
      return fail(500, {
        error: 'เกิดข้อผิดพลาดในการปฏิเสธคำเชิญ',
        type: 'reject',
      });
    }
  },
};
