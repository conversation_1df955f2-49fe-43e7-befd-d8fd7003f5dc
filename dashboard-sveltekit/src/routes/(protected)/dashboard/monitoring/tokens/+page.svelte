<script lang="ts">
	import Icon from '@iconify/svelte';
	import { onMount } from 'svelte';
	import { Badge, Card } from '$lib/components/ui';
	import { authStore } from '$lib/stores/auth.svelte';

	let tokenHealth = $state<{
		isValid: boolean;
		needsRefresh: boolean;
		error?: string;
	} | null>(null);

	let refreshStats = $state({
		lastRefreshTime: 0,
		totalRefreshes: 0,
		failedRefreshes: 0,
	});

	let isLoading = $state(false);

	onMount(async () => {
		await checkTokenHealth();
		loadRefreshStats();
	});

	async function checkTokenHealth() {
		isLoading = true;
		try {
			tokenHealth = await authStore.checkTokenHealth();
		} catch (error) {
			console.error('Failed to check token health:', error);
		} finally {
			isLoading = false;
		}
	}

	function loadRefreshStats() {
		// Load from localStorage or API
		const stats = localStorage.getItem('token-refresh-stats');
		if (stats) {
			refreshStats = JSON.parse(stats);
		}
	}

	async function forceRefresh() {
		isLoading = true;
		try {
			const success = await authStore.smartRefresh();
			if (success) {
				refreshStats.totalRefreshes++;
				refreshStats.lastRefreshTime = Date.now();
			} else {
				refreshStats.failedRefreshes++;
			}
			localStorage.setItem('token-refresh-stats', JSON.stringify(refreshStats));
			await checkTokenHealth();
		} catch (error) {
			console.error('Force refresh failed:', error);
			refreshStats.failedRefreshes++;
		} finally {
			isLoading = false;
		}
	}

	function formatTime(timestamp: number) {
		if (!timestamp) return 'Never';
		return new Date(timestamp).toLocaleString('th-TH');
	}

	function getHealthBadgeColor(isValid: boolean, needsRefresh: boolean) {
		if (isValid) return 'success';
		if (needsRefresh) return 'warning';
		return 'error';
	}

	function getHealthBadgeText(isValid: boolean, needsRefresh: boolean) {
		if (isValid) return 'Healthy';
		if (needsRefresh) return 'Needs Refresh';
		return 'Invalid';
	}
</script>

<div class="space-y-6">
	<div class="flex items-center justify-between">
		<h1 class="text-2xl font-bold">Token Monitoring</h1>
		<button class="btn btn-primary btn-sm" onclick={checkTokenHealth} disabled={isLoading}>
			{#if isLoading}
				<span class="loading loading-spinner loading-sm"></span>
			{:else}
				<Icon icon="solar:refresh-bold" class="w-4 h-4" />
			{/if}
			Refresh Status
		</button>
	</div>

	<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
		<!-- Token Health Status -->
		<Card title="Token Health" titleIcon="solar:shield-check-bold">
			<div class="space-y-4">
				{#if tokenHealth}
					<div class="flex items-center justify-between">
						<span>Status:</span>
						<Badge
							label={getHealthBadgeText(tokenHealth.isValid, tokenHealth.needsRefresh)}
							color={getHealthBadgeColor(tokenHealth.isValid, tokenHealth.needsRefresh)}
							variant="solid"
						/>
					</div>

					{#if tokenHealth.error}
						<div class="alert alert-warning alert-sm">
							<Icon icon="solar:danger-triangle-bold" class="w-4 h-4" />
							<span class="text-xs">{tokenHealth.error}</span>
						</div>
					{/if}

					{#if tokenHealth.needsRefresh}
						<button
							class="btn btn-warning btn-sm w-full"
							onclick={forceRefresh}
							disabled={isLoading}
						>
							Force Refresh
						</button>
					{/if}
				{:else}
					<div class="skeleton h-20 w-full"></div>
				{/if}
			</div>
		</Card>

		<!-- Refresh Statistics -->
		<Card title="Refresh Stats" titleIcon="solar:chart-2-bold">
			<div class="space-y-3">
				<div class="flex justify-between">
					<span class="text-sm text-base-content/70">Last Refresh:</span>
					<span class="text-sm font-medium">
						{formatTime(refreshStats.lastRefreshTime)}
					</span>
				</div>

				<div class="flex justify-between">
					<span class="text-sm text-base-content/70">Total Refreshes:</span>
					<span class="text-sm font-medium text-success">
						{refreshStats.totalRefreshes}
					</span>
				</div>

				<div class="flex justify-between">
					<span class="text-sm text-base-content/70">Failed Refreshes:</span>
					<span class="text-sm font-medium text-error">
						{refreshStats.failedRefreshes}
					</span>
				</div>

				<div class="flex justify-between">
					<span class="text-sm text-base-content/70">Success Rate:</span>
					<span class="text-sm font-medium">
						{refreshStats.totalRefreshes + refreshStats.failedRefreshes > 0
							? Math.round(
									(refreshStats.totalRefreshes /
										(refreshStats.totalRefreshes + refreshStats.failedRefreshes)) *
										100
								)
							: 0}%
					</span>
				</div>
			</div>
		</Card>

		<!-- User Info -->
		<Card title="User Session" titleIcon="solar:user-bold">
			<div class="space-y-3">
				{#if authStore.user}
					<div class="flex justify-between">
						<span class="text-sm text-base-content/70">User ID:</span>
						<span class="text-sm font-medium font-mono">
							{authStore.user._id.substring(0, 8)}...
						</span>
					</div>

					<div class="flex justify-between">
						<span class="text-sm text-base-content/70">Email:</span>
						<span class="text-sm font-medium">
							{authStore.user.email}
						</span>
					</div>

					<div class="flex justify-between">
						<span class="text-sm text-base-content/70">Verified:</span>
						<Badge
							label={authStore.user.isEmailVerified ? 'Yes' : 'No'}
							color={authStore.user.isEmailVerified ? 'success' : 'warning'}
							size="sm"
						/>
					</div>

					<div class="flex justify-between">
						<span class="text-sm text-base-content/70">Role:</span>
						<Badge label={authStore.user.role || 'user'} color="info" size="sm" />
					</div>
				{:else}
					<div class="alert alert-error alert-sm">
						<Icon icon="solar:user-cross-bold" class="w-4 h-4" />
						<span class="text-xs">No user session</span>
					</div>
				{/if}
			</div>
		</Card>
	</div>

	<!-- Token Configuration Info -->
	<Card title="Token Configuration" titleIcon="solar:settings-bold">
		<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
			<div class="stat">
				<div class="stat-title">Auto Refresh Interval</div>
				<div class="stat-value text-lg">2 hours</div>
				<div class="stat-desc">Reduced from 30 minutes</div>
			</div>

			<div class="stat">
				<div class="stat-title">Manual Refresh Limit</div>
				<div class="stat-value text-lg">5 minutes</div>
				<div class="stat-desc">Minimum time between refreshes</div>
			</div>

			<div class="stat">
				<div class="stat-title">Token Lifetime</div>
				<div class="stat-value text-lg">14 days</div>
				<div class="stat-desc">Reduced from 30 days</div>
			</div>

			<div class="stat">
				<div class="stat-title">Rotation Threshold</div>
				<div class="stat-value text-lg">12 hours</div>
				<div class="stat-desc">When to rotate tokens</div>
			</div>
		</div>
	</Card>
</div>
