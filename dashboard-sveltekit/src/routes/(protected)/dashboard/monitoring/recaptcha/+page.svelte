<script lang="ts">
	import Icon from '@iconify/svelte';
	import { onMount } from 'svelte';
	import { t } from 'svelte-i18n';
	import Button from '$lib/components/ui/Button.svelte';
	import Card from '$lib/components/ui/Card.svelte';
	import { showError, showSuccess } from '$lib/utils/sweetalert';

	const { data } = $props<{
		data: {
			stats: any;
			config: any;
		};
	}>();

	let stats = $state(data.stats);
	let config = $state(data.config);
	let isLoading = $state(false);

	// Auto refresh every 30 seconds
	let refreshInterval: NodeJS.Timeout;

	onMount(() => {
		refreshInterval = setInterval(refreshStats, 30000);
		return () => {
			if (refreshInterval) clearInterval(refreshInterval);
		};
	});

	async function refreshStats() {
		if (isLoading) return;

		try {
			isLoading = true;
			const response = await fetch('/dashboard/monitoring/recaptcha/api', {
				method: 'GET',
			});

			if (response.ok) {
				const result = await response.json();
				stats = result.stats;
				config = result.config;
			}
		} catch (error) {
			console.error('Failed to refresh stats:', error);
		} finally {
			isLoading = false;
		}
	}

	async function resetStats() {
		try {
			const response = await fetch('/dashboard/monitoring/recaptcha/api', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({ action: 'reset' }),
			});

			if (response.ok) {
				await refreshStats();
				showSuccess('สำเร็จ!', 'รีเซ็ตสถิติ reCAPTCHA แล้ว');
			} else {
				showError('เกิดข้อผิดพลาด', 'ไม่สามารถรีเซ็ตสถิติได้');
			}
		} catch (error) {
			console.error('Failed to reset stats:', error);
			showError('เกิดข้อผิดพลาด', 'ไม่สามารถรีเซ็ตสถิติได้');
		}
	}

	function getSuccessRate(success: number, total: number): number {
		return total > 0 ? (success / total) * 100 : 0;
	}

	function getStatusColor(rate: number): string {
		if (rate >= 90) return 'text-success';
		if (rate >= 70) return 'text-warning';
		return 'text-error';
	}

	function getScoreColor(score: number): string {
		if (score >= 0.7) return 'text-success';
		if (score >= 0.5) return 'text-warning';
		return 'text-error';
	}
</script>

<div class="container mx-auto p-6 space-y-6">
	<!-- Header -->
	<div class="flex justify-between items-center">
		<div>
			<h1 class="text-3xl font-bold">reCAPTCHA Monitoring</h1>
			<p class="text-base-content/70 mt-2">ติดตามสถิติและประสิทธิภาพของ reCAPTCHA</p>
		</div>
		<div class="flex gap-2">
			<Button color="primary" size="sm" loading={isLoading} onclick={refreshStats}>
				<Icon icon="mdi:refresh" class="w-4 h-4 mr-2" />
				รีเฟรช
			</Button>
			<Button color="error" variant="outline" size="sm" onclick={resetStats}>
				<Icon icon="mdi:delete" class="w-4 h-4 mr-2" />
				รีเซ็ตสถิติ
			</Button>
		</div>
	</div>

	<!-- Configuration Status -->
	<Card title="การตั้งค่า reCAPTCHA" variant="default">
		<div class="grid grid-cols-1 md:grid-cols-3 gap-4">
			<div class="stat">
				<div class="stat-figure text-primary">
					<Icon icon="mdi:shield-check" class="w-8 h-8" />
				</div>
				<div class="stat-title">สถานะ</div>
				<div class="stat-value text-sm {config.enabled ? 'text-success' : 'text-error'}">
					{config.enabled ? 'เปิดใช้งาน' : 'ปิดใช้งาน'}
				</div>
			</div>
			<div class="stat">
				<div class="stat-figure text-secondary">
					<Icon icon="mdi:key" class="w-8 h-8" />
				</div>
				<div class="stat-title">Site Key</div>
				<div class="stat-value text-sm {config.siteKey ? 'text-success' : 'text-error'}">
					{config.siteKey ? 'กำหนดแล้ว' : 'ไม่ได้กำหนด'}
				</div>
			</div>
			<div class="stat">
				<div class="stat-figure text-accent">
					<Icon icon="mdi:key-variant" class="w-8 h-8" />
				</div>
				<div class="stat-title">Secret Key</div>
				<div class="stat-value text-sm {config.hasSecretKey ? 'text-success' : 'text-error'}">
					{config.hasSecretKey ? 'กำหนดแล้ว' : 'ไม่ได้กำหนด'}
				</div>
			</div>
		</div>
	</Card>

	<!-- Overall Statistics -->
	<Card title="สถิติรวม" variant="default">
		<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
			<div class="stat">
				<div class="stat-figure text-primary">
					<Icon icon="mdi:counter" class="w-8 h-8" />
				</div>
				<div class="stat-title">การตรวจสอบทั้งหมด</div>
				<div class="stat-value">{stats.total.toLocaleString()}</div>
			</div>
			<div class="stat">
				<div class="stat-figure text-success">
					<Icon icon="mdi:check-circle" class="w-8 h-8" />
				</div>
				<div class="stat-title">สำเร็จ</div>
				<div class="stat-value text-success">
					{stats.success.toLocaleString()}
				</div>
				<div class="stat-desc">
					{getSuccessRate(stats.success, stats.total).toFixed(1)}%
				</div>
			</div>
			<div class="stat">
				<div class="stat-figure text-error">
					<Icon icon="mdi:close-circle" class="w-8 h-8" />
				</div>
				<div class="stat-title">ล้มเหลว</div>
				<div class="stat-value text-error">
					{stats.failed.toLocaleString()}
				</div>
				<div class="stat-desc">
					{getSuccessRate(stats.failed, stats.total).toFixed(1)}%
				</div>
			</div>
			<div class="stat">
				<div class="stat-figure text-warning">
					<Icon icon="mdi:alert-circle" class="w-8 h-8" />
				</div>
				<div class="stat-title">คะแนนต่ำ</div>
				<div class="stat-value text-warning">
					{stats.lowScore.toLocaleString()}
				</div>
				<div class="stat-desc">
					{getSuccessRate(stats.lowScore, stats.total).toFixed(1)}%
				</div>
			</div>
		</div>

		<!-- Average Score -->
		<div class="mt-6">
			<div class="flex items-center justify-between mb-2">
				<span class="text-sm font-medium">คะแนนเฉลี่ย</span>
				<span class="text-sm {getScoreColor(stats.averageScore)}">
					{stats.averageScore.toFixed(3)}
				</span>
			</div>
			<div class="w-full bg-base-300 rounded-full h-2">
				<div
					class="h-2 rounded-full transition-all duration-300 {stats.averageScore >= 0.7
						? 'bg-success'
						: stats.averageScore >= 0.5
							? 'bg-warning'
							: 'bg-error'}"
					style="width: {Math.min(stats.averageScore * 100, 100)}%"
				></div>
			</div>
		</div>
	</Card>

	<!-- Action-based Statistics -->
	<Card title="สถิติตาม Action" variant="default">
		{#if Object.keys(stats.actions).length > 0}
			<div class="overflow-x-auto">
				<table class="table table-zebra w-full">
					<thead>
						<tr>
							<th>Action</th>
							<th>ทั้งหมด</th>
							<th>สำเร็จ</th>
							<th>Success Rate</th>
							<th>คะแนนเฉลี่ย</th>
							<th>สถานะ</th>
						</tr>
					</thead>
					<tbody>
						{#each Object.entries(stats.actions) as [action, actionStats]}
							{@const successRate = getSuccessRate(actionStats.success, actionStats.total)}
							<tr>
								<td>
									<div class="flex items-center gap-2">
										<Icon
											icon={action === 'signup'
												? 'mdi:account-plus'
												: action === 'forgot_password'
													? 'mdi:lock-reset'
													: action === 'signin'
														? 'mdi:login'
														: 'mdi:shield-check'}
											class="w-4 h-4"
										/>
										<span class="font-medium">{action}</span>
									</div>
								</td>
								<td>{actionStats.total.toLocaleString()}</td>
								<td class="text-success">{actionStats.success.toLocaleString()}</td>
								<td>
									<div class="flex items-center gap-2">
										<span class={getStatusColor(successRate)}>
											{successRate.toFixed(1)}%
										</span>
										<div class="w-16 bg-base-300 rounded-full h-1">
											<div
												class="h-1 rounded-full {successRate >= 90
													? 'bg-success'
													: successRate >= 70
														? 'bg-warning'
														: 'bg-error'}"
												style="width: {successRate}%"
											></div>
										</div>
									</div>
								</td>
								<td class={getScoreColor(actionStats.averageScore)}>
									{actionStats.averageScore.toFixed(3)}
								</td>
								<td>
									{#if successRate >= 90}
										<div class="badge badge-success">ดีเยี่ยม</div>
									{:else if successRate >= 70}
										<div class="badge badge-warning">ปานกลาง</div>
									{:else}
										<div class="badge badge-error">ต้องปรับปรุง</div>
									{/if}
								</td>
							</tr>
						{/each}
					</tbody>
				</table>
			</div>
		{:else}
			<div class="text-center py-8 text-base-content/60">
				<Icon icon="mdi:chart-line" class="w-16 h-16 mx-auto mb-4 opacity-50" />
				<p>ยังไม่มีข้อมูลสถิติ</p>
				<p class="text-sm">สถิติจะแสดงหลังจากมีการใช้งาน reCAPTCHA</p>
			</div>
		{/if}
	</Card>

	<!-- Performance Indicators -->
	<div class="grid grid-cols-1 md:grid-cols-3 gap-6">
		<Card title="ประสิทธิภาพ" variant="default">
			<div class="space-y-4">
				<div class="flex justify-between items-center">
					<span>Success Rate</span>
					<span class={getStatusColor(getSuccessRate(stats.success, stats.total))}>
						{getSuccessRate(stats.success, stats.total).toFixed(1)}%
					</span>
				</div>
				<div class="flex justify-between items-center">
					<span>Low Score Rate</span>
					<span class="text-warning">
						{getSuccessRate(stats.lowScore, stats.total).toFixed(1)}%
					</span>
				</div>
				<div class="flex justify-between items-center">
					<span>Average Score</span>
					<span class={getScoreColor(stats.averageScore)}>
						{stats.averageScore.toFixed(3)}
					</span>
				</div>
			</div>
		</Card>

		<Card title="แนะนำ" variant="default">
			<div class="space-y-3">
				{#if getSuccessRate(stats.success, stats.total) < 70}
					<div class="alert alert-warning">
						<Icon icon="mdi:alert" class="w-4 h-4" />
						<span class="text-sm">Success rate ต่ำ ควรตรวจสอบการตั้งค่า</span>
					</div>
				{/if}
				{#if stats.averageScore < 0.5}
					<div class="alert alert-error">
						<Icon icon="mdi:alert-circle" class="w-4 h-4" />
						<span class="text-sm">คะแนนเฉลี่ยต่ำมาก ควรปรับ threshold</span>
					</div>
				{/if}
				{#if getSuccessRate(stats.lowScore, stats.total) > 20}
					<div class="alert alert-info">
						<Icon icon="mdi:information" class="w-4 h-4" />
						<span class="text-sm">มี low score สูง ควรลด minimum score</span>
					</div>
				{/if}
				{#if stats.total === 0}
					<div class="alert alert-info">
						<Icon icon="mdi:information" class="w-4 h-4" />
						<span class="text-sm">ยังไม่มีข้อมูล ลองใช้งาน reCAPTCHA</span>
					</div>
				{/if}
			</div>
		</Card>

		<Card title="การตั้งค่าที่แนะนำ" variant="default">
			<div class="space-y-3 text-sm">
				<div>
					<strong>Signup:</strong> min score 0.5
				</div>
				<div>
					<strong>Forgot Password:</strong> min score 0.3
				</div>
				<div>
					<strong>Signin:</strong> min score 0.7
				</div>
				<div class="divider my-2"></div>
				<div class="text-xs text-base-content/60">
					คะแนน reCAPTCHA v3 อยู่ระหว่าง 0.0-1.0<br />
					1.0 = มนุษย์แน่นอน<br />
					0.0 = bot แน่นอน
				</div>
			</div>
		</Card>
	</div>
</div>
