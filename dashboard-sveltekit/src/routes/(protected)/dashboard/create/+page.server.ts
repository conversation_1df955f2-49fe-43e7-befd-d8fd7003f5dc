import { validateCreateSiteForm } from '$lib/schemas/common.schema';
import type { CheckDomainData, CreateSiteData } from '$lib/schemas/site.schema';
import { siteService } from '$lib/services/site';
import { subscriptionService } from '$lib/services/subscription';
import { fail, redirect } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';

/**
 * ✅ HYBRID APPROACH: SvelteKit Route API + Service Pattern
 * - Route-level validation and error handling
 * - Service layer for business logic
 * - Consistent fail() responses
 * - Type-safe data flow
 * - Progressive enhancement with use:enhance
 */

export const load: PageServerLoad = async ({ locals }) => {
  // Auth check already done in layout
  try {
    // ✅ Load packages for site creation
    const packagesResult = await subscriptionService.getPackages(locals.token!);

    return {
      packages: packagesResult.success && packagesResult.data
        ? Array.isArray(packagesResult.data)
          ? packagesResult.data
          : (packagesResult.data as any)?.packages || []
        : [],
    };
  }
  catch (error) {
    console.error('Error loading packages:', error);
    return {
      packages: [],
    };
  }
};

export const actions: Actions = {
  /**
   * ✅ Check domain availability
   */
  checkDomain: async ({ request, locals }) => {
    try {
      const data = await request.formData();

      // Extract form data
      const domainData: CheckDomainData = {
        typeDomain: data.get('typeDomain') as 'subdomain' | 'custom',
        subDomain: data.get('subDomain')?.toString(),
        mainDomain: data.get('mainDomain')?.toString(),
        customDomain: data.get('customDomain')?.toString(),
      };

      // Call site service (validation included)
      const result = await siteService.checkDomain(domainData, locals.token);
      console.log('result', result);

      if (!result.success) {
        return fail(400, {
          statusMessage: 'ล้มเหลว!',
          message: result.message,
          type: 'domain',
        });
      }

      if (!result.data?.available) {
        return fail(400, {
          statusMessage: 'โดเมนไม่ว่าง!',
          message: 'โดเมนนี้ถูกใช้งานแล้ว',
          type: 'domain',
        });
      }

      return {
        success: true,
        data: result.data,
        statusMessage: result.statusMessage || 'โดเมนว่าง',
        message: 'สามารถใช้งานได้',
        type: 'domain',
      };
    }
    catch (error) {
      console.error('Check domain error:', error);
      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการตรวจสอบโดเมน',
        type: 'domain',
      });
    }
  },

  /**
   * ✅ Check discount code
   */
  checkDiscount: async ({ request, locals }) => {
    try {
      const data = await request.formData();

      // Extract form data
      const discountCode = data.get('discountCode')?.toString()?.trim();
      const orderAmount = Number(data.get('orderAmount')) || 0;

      // Route-level validation
      if (!discountCode) {
        return fail(400, {
          message: 'กรุณากรอกรหัสส่วนลด',
          type: 'discount',
        });
      }

      if (discountCode.length < 2) {
        return fail(400, {
          message: 'รหัสส่วนลดต้องมีอย่างน้อย 2 ตัวอักษร',
          type: 'discount',
        });
      }

      if (orderAmount <= 0) {
        return fail(400, {
          message: 'ยอดสั่งซื้อไม่ถูกต้อง',
          type: 'discount',
        });
      }

      // Call subscription service for discount validation
      const result = await subscriptionService.validateDiscount(discountCode, locals.token!);

      if (!result.success) {
        return fail(400, {
          message: result.error || 'รหัสส่วนลดไม่ถูกต้องหรือหมดอายุ',
          type: 'discount',
        });
      }

      return {
        success: true,
        data: result.data,
        message: 'รหัสส่วนลดถูกต้อง',
        type: 'discount',
      };
    }
    catch (error) {
      console.error('Check discount error:', error);
      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการตรวจสอบรหัสส่วนลด',
        type: 'discount',
      });
    }
  },

  /**
   * ✅ Create new site with enhanced validation
   */
  createSite: async ({ request, locals }) => {
    try {
      const data = await request.formData();

      // Extract form data
      const siteData: CreateSiteData = {
        siteName: data.get('siteName')?.toString() || '',
        typeDomain: data.get('typeDomain') as 'subdomain' | 'custom',
        subDomain: data.get('subDomain')?.toString()?.trim(),
        mainDomain: data.get('mainDomain')?.toString()?.trim(),
        customDomain: data.get('customDomain')?.toString()?.trim(),
        packageType: data.get('packageType')?.toString()?.trim() || '',
      };

      console.log('siteData', siteData);

      // ✅ Zod Schema Validation
      const validation = validateCreateSiteForm(siteData);
      if (!validation.success) {
        return fail(400, {
          message: 'ข้อมูลไม่ถูกต้อง',
          errors: validation.errors,
          type: 'create',
        });
      }

      // ใช้ validated data
      const validatedData = validation.data!;
      console.log('validatedData', validatedData);

      // ✅ Zod schema ครอบคลุม validation ทั้งหมดแล้ว

      // Call site service (additional validation included)
      const result = await siteService.createSite(validatedData, locals.token!);

      console.log('result', result);
      if (!result.success) {
        return fail(400, {
          message: result.error,
          type: 'create',
        });
      }

      // Redirect to the new site dashboard
      throw redirect(303, `/dashboard/${result.data?._id}`);
    }
    catch (error) {
      // Don't log redirects as errors
      if (error instanceof Response) {
        throw error; // Re-throw redirects
      }

      console.error('Create site error:', error);
      return fail(500, {
        message: 'เกิดข้อผิดพลาดในการสร้างเว็บไซต์',
        type: 'create',
      });
    }
  },
};
