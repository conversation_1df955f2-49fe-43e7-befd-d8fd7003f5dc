import type { LayoutServerLoad } from './$types';

export const load: LayoutServerLoad = async ({ locals, parent }) => {
  console.log('Dashboard Layout Server: locals.user:', locals.user);
  console.log('Dashboard Layout Server: locals.token:', locals.token ? 'exists' : 'not found');

  // รับข้อมูลจาก parent layout
  const parentData = await parent();

  return {
    user: parentData.user,
    token: parentData.token,
  };
};
