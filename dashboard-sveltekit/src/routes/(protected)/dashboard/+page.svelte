<script lang="ts">
	import Icon from '@iconify/svelte';
	import { t } from 'svelte-i18n';
	import { page } from '$app/state';
	import SEO from '$lib/components/layout/SEO.svelte';
	import Image from '$lib/components/ui/Image.svelte';
	import { authStore } from '$lib/stores/auth.svelte';
	import {
		showConfirm,
		showErrorToast,
		showInfoToast,
		showSuccess,
		showSuccessToast,
		showWarningToast,
	} from '$lib/utils/sweetalert';

	const { data } = $props<{
		data: {
			user?: any;
			sites?: any[];
			pagination?: any;
			error?: string;
		};
	}>();

	const user = $derived(data?.user || authStore.user);
	const error = $derived(data?.error);
	const sites = $derived(data?.sites || []);
	const pagination = $derived(data?.pagination);

	// รอให้ authStore initialized
	const isReady = $derived(authStore.isInitialized);

	// ตัวอย่างการใช้งาน SweetAlert2
	async function testSweetAlert() {
		// แสดง Toast
		showSuccessToast('นี่คือ Success Toast!', {
			// timer: 100000,
		});

		setTimeout(() => {
			showErrorToast('นี่คือ Error Toast!', {
				timer: 1000,
			});
		}, 1000);

		setTimeout(() => {
			showWarningToast('นี่คือ Warning Toast!');
		}, 2000);

		setTimeout(() => {
			showInfoToast('นี่คือ Info Toast!');
		}, 3000);

		// แสดง Modal
		setTimeout(async () => {
			const result = await showConfirm('ยืนยันการดำเนินการ', 'คุณต้องการดำเนินการต่อหรือไม่?');
			if (result && result.isConfirmed) {
				showSuccess('สำเร็จ!', 'การดำเนินการเสร็จสิ้น');
			}
		}, 4000);
	}

	// Format date
	const formatDate = (date: Date) => {
		return new Date(date).toLocaleDateString('th-TH');
	};

	// Get role label
	const getRoleLabel = (role: string) => {
		switch (role) {
			case 'owner':
				return 'เจ้าของ';
			case 'admin':
				return 'ผู้ดูแล';
			case 'editor':
				return 'บรรณาธิการ';
			case 'viewer':
				return 'ผู้ดู';
			default:
				return role;
		}
	};

	// Check if site is expired
	const isExpired = (expiredAt: string) => {
		return new Date(expiredAt) < new Date();
	};

	// Get days until expiration
	const getDaysUntilExpiration = (expiredAt: string) => {
		const now = new Date();
		const expiry = new Date(expiredAt);
		const diffTime = expiry.getTime() - now.getTime();
		const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
		return diffDays;
	};

	// Handle view site click
	function viewSite(url: string, event: Event) {
		event.preventDefault();
		window.open(url, '_blank');
	}
</script>

<SEO title="Dashboard" description="จัดการเว็บไซต์ของคุณ" />

<div class="space-y-6">
	<!-- Header -->
	<div class="flex justify-between items-center">
		<div>
			<h1 class="text-3xl font-bold">Dashboard</h1>
			<p class="text-muted-foreground">จัดการเว็บไซต์ของคุณ</p>
		</div>
		<button class="btn btn-primary" onclick={testSweetAlert}>
			<Icon icon="solar:bell-bold" class="size-5" />
			ทดสอบ SweetAlert
		</button>
	</div>

	<!-- Error Display -->
	{#if error}
		<div class="alert alert-error">
			<Icon icon="solar:info-circle-bold" class="size-5" />
			<span>{error}</span>
		</div>
	{/if}

	<!-- Sites Grid -->
	{#if sites.length > 0}
		<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
			{#each sites as site}
				<div class="card bg-base-100 shadow-xl hover:shadow-2xl transition-all duration-300">
					<div class="card-body">
						<!-- Site Logo/Icon -->
						<div class="flex justify-center mb-4">
							<div class=" ">
								<Image
									publicId={site.seoSettings?.logo}
									width={200}
									height={200}
									cover={true}
									className="w-24 h-24 rounded-full object-cover"
									alt="โลโก้"
									fallbackIcon="solar:global-line-duotone"
									fallbackIconClass="w-12 h-12 text-primary"
								/>
							</div>
						</div>

						<!-- Site Title -->
						<h3 class="card-title text-center justify-center text-xl">
							{site?.title || site?.fullDomain || 'ไม่มีชื่อ'}
						</h3>

						<!-- Role Badge -->
						<div class="badge badge-primary badge-outline absolute top-2 left-2">
							<Icon icon="heroicons:user-group" class="w-4 h-4 mr-1" />
							{getRoleLabel(site.userRole)}
						</div>

						<!-- View Site Button -->
						<button
							class="btn btn-sm btn-outline absolute top-2 right-2"
							onclick={e => viewSite(site.fullDomain, e)}
						>
							<Icon icon="heroicons:globe-alt" class="w-4 h-4 mr-1" />
							ดูเว็บไซต์
						</button>

						<!-- Description -->
						<p class="text-base-content/70 text-center mb-4">
							{site.description || 'ไม่มีคำอธิบาย'}
						</p>

						<!-- Expiration Status -->
						<div class="flex items-center justify-between p-3 rounded-lg bg-base-200">
							<div class="flex items-center gap-2">
								<Icon
									icon="heroicons:clock"
									class="w-5 h-5 {isExpired(site.expiredAt) ? 'text-error' : 'text-success'}"
								/>
								<span class="font-medium text-sm">
									{isExpired(site.expiredAt)
										? 'หมดอายุแล้ว'
										: `เหลือ ${getDaysUntilExpiration(site.expiredAt)} วัน`}
								</span>
							</div>
							<span class="text-xs opacity-75">
								{formatDate(site.expiredAt)}
							</span>
						</div>

						<!-- Keywords -->
						{#if site.keyword && site.keyword.length > 0}
							<div class="flex flex-wrap gap-1 mt-3">
								{#each site.keyword as keyword}
									<div class="badge badge-outline badge-sm">
										{keyword}
									</div>
								{/each}
							</div>
						{/if}

						<!-- Card Actions -->
						<div class="card-actions justify-center mt-4">
							<a data-sveltekit-prefetch="off" href={`/dashboard/${site._id}`} class="btn btn-primary btn-sm"> จัดการเว็บไซต์ </a>
						</div>
					</div>
				</div>
			{/each}
		</div>

		<!-- Pagination -->
		{#if pagination && pagination.totalPages > 1}
			<div class="flex justify-center">
				<div class="join">
					{#each Array.from({ length: pagination.totalPages }, (_, i) => i + 1) as pageNum}
						<a
							href="?page={pageNum}"
							class="join-item btn {pageNum === pagination.page ? 'btn-active' : ''}"
						>
							{pageNum}
						</a>
					{/each}
				</div>
			</div>
		{/if}
	{:else}
		<!-- Empty State -->
		<div class="text-center py-12">
			<Icon icon="solar:home-smile-angle-bold" class="size-24 mx-auto text-muted-foreground mb-4" />
			<h3 class="text-xl font-semibold mb-2">ยังไม่มีเว็บไซต์</h3>
			<p class="text-muted-foreground mb-6">เริ่มต้นสร้างเว็บไซต์แรกของคุณ</p>
			<div class="flex justify-center gap-4">
				<a href="/dashboard/create" class="btn btn-primary">
					<Icon icon="solar:home-add-angle-bold" class="size-5" />
					สร้างเว็บไซต์
				</a>
				<a href="/dashboard/join" class="btn btn-outline">
					<Icon icon="solar:users-group-rounded-bold" class="size-5" />
					เข้าร่วมเว็บไซต์
				</a>
			</div>
		</div>
	{/if}
</div>
