import type { SitePaginationParams } from '$lib/schemas/site.schema';
import { siteService } from '$lib/services/site';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ locals, url }) => {
  // Auth check already done in layout
  try {
    // ✅ Extract pagination parameters
    const params: SitePaginationParams = {
      page: url.searchParams.get('page') || '1',
      limit: url.searchParams.get('limit') || '10',
      search: url.searchParams.get('search') || '',
      status: url.searchParams.get('status') || '',
    };

    // ✅ Load user sites using service
    const result = await siteService.getUserSitesWithPagination(params, locals.token!);

    if (!result.success) {
      console.error('Sites service error:', result.error);
      return {
        user: locals.user, // ✅ เพิ่ม user data ใน error case
        sites: [],
        pagination: {
          page: parseInt(params.page || '1'),
          limit: parseInt(params.limit || '10'),
          total: 0,
          totalPages: 0,
        },
        error: result.error,
      };
    }

    return {
      user: locals.user, // ✅ เพิ่ม user data
      sites: result.data?.sites || [],
      pagination: result.data?.pagination || {
        page: parseInt(params.page || '1'),
        limit: parseInt(params.limit || '10'),
        total: 0,
        totalPages: 0,
      },
      error: null,
    };
  }
  catch (error) {
    console.error('Error loading dashboard:', error);
    return {
      user: locals.user, // ✅ เพิ่ม user data ใน catch block
      sites: [],
      pagination: {
        page: 1,
        limit: 10,
        total: 0,
        totalPages: 0,
      },
      error: error instanceof Error ? error.message : 'เกิดข้อผิดพลาดในการโหลดข้อมูล',
    };
  }
};
