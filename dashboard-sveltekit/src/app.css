@import url('https://fonts.googleapis.com/css2?family=Sarabun:wght@100;200;300;400;500;600;700;800&display=swap');
@import 'tailwindcss';
@import 'tw-animate-css';
@import './lib/styles/sweetalert.css';
@import './lib/styles/animations.css';

@plugin "daisyui";

@plugin "daisyui/theme" {
	name: 'light';
	default: true;
	prefersdark: false;
	color-scheme: light;

	/* Base colors using SvelteUI colors */
	--color-base-100: #ffffff;
	--color-base-200: #f8f9fa;
	--color-base-300: #f1f3f5;
	--color-base-content: #212529;

	/* Primary colors */
	--color-primary: #228be6;
	--color-primary-content: #ffffff;

	/* Secondary colors */
	--color-secondary: #868e96;
	--color-secondary-content: #ffffff;

	/* Accent colors */
	--color-accent: #40c057;
	--color-accent-content: #ffffff;

	/* Neutral colors */
	--color-neutral: #adb5bd;
	--color-neutral-content: #ffffff;

	/* Status colors */
	--color-info: #15aabf;
	--color-info-content: #ffffff;
	--color-success: #40c057;
	--color-success-content: #ffffff;
	--color-warning: #fab005;
	--color-warning-content: #ffffff;
	--color-error: #fa5252;
	--color-error-content: #ffffff;

	/* Border radius */
	--radius-selector: 0.5rem;
	--radius-field: 0.375rem;
	--radius-box: 0.5rem;

	/* Base sizes */
	--size-selector: 0.25rem;
	--size-field: 0.25rem;

	/* Border size */
	--border: 1px;

	/* Effects */
	--depth: 1;
	--noise: 0;
}

@plugin "daisyui/theme" {
	name: 'dark';
	default: false;
	prefersdark: false;
	color-scheme: dark;

	/* Base colors using SvelteUI dark colors */
	--color-base-100: #101113;
	--color-base-200: #141517;
	--color-base-300: #1a1b1e;
	--color-base-content: #c1c2c5;

	/* Primary colors */
	--color-primary: #228be6;
	--color-primary-content: #ffffff;

	/* Secondary colors */
	--color-secondary: #5c5f66;
	--color-secondary-content: #ffffff;

	/* Accent colors */
	--color-accent: #40c057;
	--color-accent-content: #ffffff;

	/* Neutral colors */
	--color-neutral: #373a40;
	--color-neutral-content: #ffffff;

	/* Status colors */
	--color-info: #15aabf;
	--color-info-content: #ffffff;
	--color-success: #40c057;
	--color-success-content: #ffffff;
	--color-warning: #fab005;
	--color-warning-content: #ffffff;
	--color-error: #fa5252;
	--color-error-content: #ffffff;

	/* Border radius */
	--radius-selector: 0.5rem;
	--radius-field: 0.375rem;
	--radius-box: 0.5rem;

	/* Base sizes */
	--size-selector: 0.25rem;
	--size-field: 0.25rem;

	/* Border size */
	--border: 1px;

	/* Effects */
	--depth: 1;
	--noise: 0;
}

/* Custom styles */
:root {
	--background: 0 0% 100%;
	--foreground: 222.2 84% 4.9%;
	--card: 0 0% 100%;
	--card-foreground: 222.2 84% 4.9%;
	--popover: 0 0% 100%;
	--popover-foreground: 222.2 84% 4.9%;
	--primary: 222.2 47.4% 11.2%;
	--primary-foreground: 210 40% 98%;
	--secondary: 210 40% 96%;
	--secondary-foreground: 222.2 84% 4.9%;
	--muted: 210 40% 96%;
	--muted-foreground: 215.4 16.3% 46.9%;
	--accent: 210 40% 96%;
	--accent-foreground: 222.2 84% 4.9%;
	--destructive: 0 84.2% 60.2%;
	--destructive-foreground: 210 40% 98%;
	/* --border: 214.3 31.8% 91.4%; */
	--input: 214.3 31.8% 91.4%;
	--ring: 222.2 84% 4.9%;
	--radius: 0.5rem;
}

[data-theme='dark'] {
	--background: 222.2 84% 4.9%;
	--foreground: 210 40% 98%;
	--card: 222.2 84% 4.9%;
	--card-foreground: 210 40% 98%;
	--popover: 222.2 84% 4.9%;
	--popover-foreground: 210 40% 98%;
	--primary: 210 40% 98%;
	--primary-foreground: 222.2 47.4% 11.2%;
	--secondary: 217.2 32.6% 17.5%;
	--secondary-foreground: 210 40% 98%;
	--muted: 217.2 32.6% 17.5%;
	--muted-foreground: 215 20.2% 65.1%;
	--accent: 217.2 32.6% 17.5%;
	--accent-foreground: 210 40% 98%;
	--destructive: 0 62.8% 30.6%;
	--destructive-foreground: 210 40% 98%;
	/* --border: 217.2 32.6% 17.5%; */
	--input: 217.2 32.6% 17.5%;
	--ring: 212.7 26.8% 83.9%;
}

/* Dynamic background based on theme */
body {
	background-size: cover;
	background-position: center;
	background-attachment: fixed;
	background-repeat: no-repeat;
	transition: background-image 0.3s ease;
	background-image: url('/bg/bg.avif');
	/* default light */
}

[data-theme='dark'] body {
	background-image: url('/bg/bg-dark.avif');
}

img {
	-webkit-user-select: none;
	/* -khtml-user-select: none; */
	-moz-user-select: none;
	-o-user-select: none;
	-ms-user-select: none;
	user-select: none;

	-webkit-user-drag: none;
	/* -khtml-user-drag: none; */
	-moz-user-drag: none;
	-o-user-drag: none;
	-ms-user-drag: none;
	/* user-drag: none; */
}

input,
select,
textarea,
input:focus,
select:focus,
textarea:focus {
	@apply select-none border-none ring-1 ring-base-content/20 focus:ring-primary focus:ring-2 shadow-none rounded-md outline-none;
}
