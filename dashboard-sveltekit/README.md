# WebShop Admin Dashboard

ระบบจัดการเว็บไซต์ที่สร้างด้วย SvelteKit 2.26.1 และ Svelte 5 พร้อมฟีเจอร์ครบครัน

## ฟีเจอร์หลัก

### 🔐 ระบบสมาชิก

- เข้าสู่ระบบ / สมัครสมาชิก
- จัดการผู้ใช้งาน (CRUD)
- ระบบบทบาท (Admin, Moderator, User)
- โปรไฟล์ผู้ใช้งาน

### 🌐 จัดการเว็บไซต์

- สร้าง/แก้ไข/ลบเว็บไซต์
- เทมเพลตหลากหลาย
- การจัดการโดเมนและ SSL
- ระบบสำรองข้อมูล

### 📊 Analytics

- สถิติการเข้าชม
- หน้าเว็บยอดนิยม
- การวิเคราะห์ประเทศและอุปกรณ์
- กราฟแสดงผล

### ⚙️ การตั้งค่า

- การตั้งค่าทั่วไป
- ความปลอดภัย
- อีเมล SMTP
- การสำรองข้อมูล
- รูปลักษณ์และธีม

### 🎨 UI/UX

- **Svelte 5 Runes & Signals** - ใช้ระบบ reactive ใหม่
- **Tailwind CSS** - สำหรับ styling
- **DaisyUI** - UI components
- **Dark/Light Mode** - เปลี่ยนธีมได้
- **Multi-language** - รองรับ 3 ภาษา (ไทย, อังกฤษ, ลาว)
- **Iconify** - ระบบไอคอน
- **Responsive Design** - รองรับทุกขนาดหน้าจอ

## เทคโนโลยีที่ใช้

- **SvelteKit 2.26.1** - Full-stack framework
- **Svelte 5** - Frontend framework with runes
- **TypeScript** - Type safety
- **Tailwind CSS 4.0** - Utility-first CSS
- **DaisyUI 5.0** - UI component library
- **svelte-i18n** - Internationalization
- **@iconify/svelte** - Icon system

## การติดตั้ง

1. **Clone repository**

```bash
git clone <repository-url>
cd dashboard-sveltekit
```

2. **ติดตั้ง dependencies**

```bash
npm install
# หรือ
bun install
```

3. **รันในโหมด development**

```bash
npm run dev
# หรือ
bun dev
```

4. **เปิดเบราว์เซอร์ไปที่**

```
http://localhost:5173
```

## โครงสร้างโปรเจค

```
src/
├── lib/
│   ├── components/          # UI Components
│   │   ├── ui/             # Base UI components
│   │   ├── layout/         # Layout components
│   │   ├── users/          # User management components
│   │   └── websites/       # Website management components
│   ├── stores/             # Svelte stores (with runes)
│   │   ├── auth.svelte.ts  # Authentication store
│   │   ├── users.svelte.ts # Users management store
│   │   ├── websites.svelte.ts # Websites store
│   │   └── theme.svelte.ts # Theme store
│   └── i18n/               # Internationalization
│       ├── index.ts        # i18n setup
│       └── locales/        # Language files
├── routes/
│   ├── (dashboard)/        # Protected dashboard routes
│   │   ├── dashboard/      # Main dashboard
│   │   ├── users/          # User management
│   │   ├── website/        # Website management
│   │   ├── analytics/      # Analytics
│   │   ├── profile/        # User profile
│   │   └── settings/       # System settings
│   ├── login/              # Login page
│   ├── register/           # Registration page
│   └── +layout.svelte      # Root layout
├── app.html                # HTML template
└── app.css                 # Global styles
```

## การใช้งาน

### 1. เข้าสู่ระบบ

- ไปที่หน้า `signin`
- กรอกอีเมลและรหัสผ่าน
- หรือสมัครสมาชิกใหม่ที่ `/signup`

### 2. จัดการผู้ใช้งาน

- ไปที่ **Users** ในเมนู
- เพิ่ม/แก้ไข/ลบผู้ใช้งาน
- กำหนดบทบาทและสถานะ

### 3. จัดการเว็บไซต์

- ไปที่ **Website** ในเมนู
- สร้างเว็บไซต์ใหม่
- เลือกเทมเพลต
- ตั้งค่าโดเมนและ SSL

### 4. ดูสถิติ

- ไปที่ **Analytics**
- ดูสถิติการเข้าชม
- วิเคราะห์พฤติกรรมผู้ใช้

### 5. ตั้งค่าระบบ

- ไปที่ **Settings** (Admin เท่านั้น)
- ปรับแต่งการตั้งค่าต่างๆ

## การปรับแต่ง

### เปลี่ยนธีม

```typescript
import { themeStore } from '$lib/stores/theme.svelte.ts';

// เปลี่ยนเป็น dark mode
themeStore.setTheme('dark');

// Toggle theme
themeStore.toggle();
```

### เปลี่ยนภาษา

```typescript
import { setLocale } from '$lib/i18n';

// เปลี่ยนเป็นภาษาไทย
setLocale('th');

// เปลี่ยนเป็นภาษาอังกฤษ
setLocale('en');

// เปลี่ยนเป็นภาษาลาว
setLocale('lo');
```

### เพิ่มภาษาใหม่

1. สร้างไฟล์ใน `src/lib/i18n/locales/`
2. เพิ่มใน `supportedLocales` ใน `src/lib/i18n/index.ts`
3. Register ใน `init` function

### ปรับแต่งสี

แก้ไขใน `tailwind.config.js`:

```javascript
daisyui: {
  themes: [
    {
      light: {
        // ปรับสีตามต้องการ
        primary: '...',
        secondary: '...',
      },
    },
  ];
}
```

## API Integration

ระบบนี้พร้อมสำหรับการเชื่อมต่อ API โดยมี stores ที่จัดการ:

- `authStore` - Authentication
- `usersStore` - User management
- `websitesStore` - Website management

แก้ไข API endpoints ใน stores ตามเซิร์ฟเวอร์ของคุณ

## การ Build

```bash
# Build สำหรับ production
npm run build

# Preview build
npm run preview
```

## การ Deploy

1. Build โปรเจค
2. Upload ไฟล์ใน `build/` ไปยังเซิร์ฟเวอร์
3. ตั้งค่า web server ให้ serve static files
4. ตั้งค่า API backend

## การพัฒนาต่อ

### เพิ่มหน้าใหม่

1. สร้างไฟล์ใน `src/routes/`
2. เพิ่มลิงก์ในเมนู (`Sidebar.svelte`)
3. เพิ่มการแปลใน language files

### เพิ่ม Component ใหม่

1. สร้างใน `src/lib/components/`
2. ใช้ Svelte 5 runes syntax
3. รองรับ TypeScript

### เพิ่ม Store ใหม่

1. สร้างใน `src/lib/stores/`
2. ใช้ `$state` และ `$derived` runes
3. Export class instance

## License

MIT License

## การสนับสนุน

หากมีปัญหาหรือข้อสงสัย สามารถสร้าง issue ใน repository นี้ได้
