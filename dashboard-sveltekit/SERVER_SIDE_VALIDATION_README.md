# Server-Side Validation System

## 📋 ภาพรวม

ระบบ validation ใหม่ที่ใช้ **Server-Side Validation** ตาม SvelteKit Form Actions pattern แทนการใช้ client-side validation เพียงอย่างเดียว

## 🎯 ข้อดีของ Server-Side Validation

- ✅ **ความปลอดภัย**: Validation ทำงานบน server ไม่สามารถ bypass ได้
- ✅ **Consistency**: ใช้ schema เดียวกันทั้ง client และ server
- ✅ **SEO Friendly**: ทำงานได้แม้ JavaScript ถูกปิด
- ✅ **Progressive Enhancement**: เพิ่มประสบการณ์ผู้ใช้ด้วย JavaScript
- ✅ **Field-Level Errors**: แสดง error แยกตาม field

## 🔧 การใช้งาน

### 1. สร้าง Schema (Zod)

```typescript
// lib/schemas/auth.schema.ts
export const signinSchema = z.object({
  email: z.string().email('รูปแบบอีเมลไม่ถูกต้อง'),
  password: z.string().min(6, 'รหัสผ่านต้องมีอย่างน้อย 6 ตัวอักษร'),
  rememberMe: z.boolean().optional().default(false),
});

// Validation function ที่ส่งคืน field-level errors
export function validateSigninForm(data: unknown) {
  const result = signinSchema.safeParse(data);
  if (result.success) {
    return {
      success: true,
      data: result.data,
      errors: {},
    };
  }

  const errors: Record<string, string> = {};
  result.error.issues.forEach(issue => {
    const path = issue.path.join('.');
    if (path) {
      errors[path] = issue.message;
    }
  });

  return {
    success: false,
    errors,
  };
}
```

### 2. Server Action (+page.server.ts)

```typescript
import { validateSigninForm } from '$lib/schemas/auth.schema';
import { fail } from '@sveltejs/kit';

export const actions: Actions = {
  signin: async ({ request }) => {
    const formData = await request.formData();
    const email = formData.get('email') as string;
    const password = formData.get('password') as string;
    const rememberMe = formData.get('rememberMe') === 'true';

    const data = {
      email: email?.trim() || '',
      password: password?.trim() || '',
      rememberMe,
    };

    // Validate input with detailed field errors
    const validation = validateSigninForm(data);
    if (!validation.success) {
      return fail(400, {
        message: 'กรุณาตรวจสอบข้อมูลที่กรอก',
        type: 'validation',
        errors: validation.errors,
        email: data.email,
        password: '', // ไม่ส่งรหัสผ่านกลับ
        rememberMe: data.rememberMe,
      });
    }

    // Process valid data...
    // ...

    return {
      success: true,
      message: 'เข้าสู่ระบบสำเร็จ',
    };
  },
};
```

### 3. Component (+page.svelte)

```svelte
<script lang="ts">
  import { enhance } from '$app/forms';

  const { form } = $props<{
    form?: any;
  }>();

  // Form state variables
  let emailValue = $state(form?.email || '');
  let passwordValue = $state(''); // ไม่เก็บรหัสผ่านจาก server
  let rememberMeValue = $state(form?.rememberMe || false);
  let isSubmitting = $state(false);
</script>

<form method="POST" action="?/signin" use:enhance={() => {
  isSubmitting = true;
  return async ({ update }) => {
    await update();
    isSubmitting = false;
  };
}}>
  <!-- Email Input -->
  <input
    name="email"
    type="email"
    bind:value={emailValue}
    class="input {form?.errors?.email ? 'input-error' : ''}"
    disabled={isSubmitting}
  />
  {#if form?.errors?.email}
    <span class="error">{form.errors.email}</span>
  {/if}

  <!-- Password Input -->
  <input
    name="password"
    type="password"
    bind:value={passwordValue}
    class="input {form?.errors?.password ? 'input-error' : ''}"
    disabled={isSubmitting}
  />
  {#if form?.errors?.password}
    <span class="error">{form.errors.password}</span>
  {/if}

  <!-- Submit Button -->
  <button type="submit" disabled={isSubmitting}>
    {isSubmitting ? 'กำลังส่ง...' : 'เข้าสู่ระบบ'}
  </button>
</form>

<!-- Display general messages -->
{#if form?.message}
  <div class="alert {form.success ? 'alert-success' : 'alert-error'}">
    {form.message}
  </div>
{/if}
```

## 📁 ไฟล์ที่เกี่ยวข้อง

### หน้า Signin

- `src/routes/(public)/signin/+page.server.ts` - Server action และ validation
- `src/routes/(public)/signin/+page.svelte` - Form component
- `src/lib/schemas/auth.schema.ts` - Zod schemas และ validation functions

### หน้าทดสอบ

- `src/routes/(public)/test-validation/+page.server.ts` - ตัวอย่าง server validation
- `src/routes/(public)/test-validation/+page.svelte` - ตัวอย่าง form component

## 🧪 การทดสอบ

### 1. หน้าทดสอบ: `/test-validation`

- ทดสอบ validation พื้นฐาน
- แสดง server response และ field errors
- ตัวอย่างการใช้งานที่ง่าย

### 2. หน้า Signin: `/signin`

- ระบบ authentication จริง
- รวม rate limiting และ security features
- ตัวอย่างการใช้งานในโปรเจ็คจริง

## 🔍 การ Debug

### Server Response Structure

```typescript
// Success Response
{
  success: true,
  message: 'บันทึกข้อมูลสำเร็จ',
  data: { ... }
}

// Error Response (fail())
{
  success: false,
  message: 'กรุณาตรวจสอบข้อมูลที่กรอก',
  type: 'validation',
  errors: {
    email: 'รูปแบบอีเมลไม่ถูกต้อง',
    password: 'รหัสผ่านต้องมีอย่างน้อย 6 ตัวอักษร'
  },
  // Form data (ยกเว้นข้อมูลที่ sensitive)
  email: '<EMAIL>',
  rememberMe: true
}
```

## 🚀 ข้อแนะนำ

1. **ใช้ `fail()` สำหรับ validation errors** - จะทำให้ form data ยังคงอยู่
2. **ไม่ส่งข้อมูล sensitive กลับ** - เช่น password
3. **ใช้ `enhance` สำหรับ progressive enhancement** - ปรับปรุง UX
4. **แสดง loading state** - ให้ผู้ใช้รู้ว่าระบบกำลังประมวลผล
5. **Validate ทั้ง client และ server** - client สำหรับ UX, server สำหรับ security

## 🔄 Migration จาก Client-Side Validation

1. ลบ client-side validation utilities
2. สร้าง server actions ใน `+page.server.ts`
3. ใช้ `form` prop ใน component
4. แสดง errors จาก `form.errors`
5. ใช้ `enhance` สำหรับ better UX

ระบบนี้ให้ความปลอดภัยและประสบการณ์ผู้ใช้ที่ดีกว่าการใช้ client-side validation เพียงอย่างเดียว! 🎉
