{"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "eslint.validate": ["javascript", "typescript"], "[typescript]": {"editor.defaultFormatter": "dprint.dprint"}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[svelte]": {"editor.defaultFormatter": "dprint.dprint"}}